-- Final database fix for RSS Aggregator
-- Execute these commands in phpMyAdmin
-- Replace 'stg_' with your actual WordPress table prefix

-- Add all missing columns at once
ALTER TABLE `stg_rss_aggregator_feeds` 
ADD COLUMN IF NOT EXISTS `geodirectory_place_name` varchar(255) DEFAULT NULL AFTER `geodirectory_place_id`,
ADD COLUMN IF NOT EXISTS `assigned_user_id` bigint(20) DEFAULT NULL AFTER `geodirectory_place_name`,
ADD COLUMN IF NOT EXISTS `assigned_user_name` varchar(255) DEFAULT NULL AFTER `assigned_user_id`,
ADD COLUMN IF NOT EXISTS `initial_import_count` int(11) DEFAULT 10 AFTER `update_frequency`,
ADD COLUMN IF NOT EXISTS `region` varchar(255) DEFAULT NULL AFTER `county`;

-- Add indexes if they don't exist
ALTER TABLE `stg_rss_aggregator_feeds` 
ADD INDEX IF NOT EXISTS `idx_assigned_user` (`assigned_user_id`),
ADD INDEX IF NOT EXISTS `idx_geodirectory_place` (`geodirectory_place_id`),
ADD INDEX IF NOT EXISTS `idx_county` (`county`),
ADD INDEX IF NOT EXISTS `idx_region` (`region`);

-- Verify the final structure
SHOW COLUMNS FROM `stg_rss_aggregator_feeds`;

-- Expected columns:
-- id, name, url, geodirectory_place_id, geodirectory_place_name, 
-- assigned_user_id, assigned_user_name, county, region, 
-- update_frequency, initial_import_count, status, created_at, last_updated
