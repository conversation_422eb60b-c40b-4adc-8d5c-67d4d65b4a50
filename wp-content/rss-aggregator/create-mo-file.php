<?php
/**
 * Simple script to create .mo file from .po file
 * Run this to generate the binary translation file
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. You must be an administrator to run this script.');
}

echo "<h1>Creating .mo file from .po file</h1>";

$po_file = __DIR__ . '/languages/rss-aggregator-pl_PL.po';
$mo_file = __DIR__ . '/languages/rss-aggregator-pl_PL.mo';

if (!file_exists($po_file)) {
    die("PO file not found: $po_file");
}

echo "<p>Reading PO file: $po_file</p>";

// Read PO file
$po_content = file_get_contents($po_file);
$lines = explode("\n", $po_content);

$translations = array();
$current_msgid = '';
$current_msgstr = '';
$in_msgid = false;
$in_msgstr = false;

foreach ($lines as $line) {
    $line = trim($line);
    
    if (empty($line) || $line[0] === '#') {
        continue;
    }
    
    if (strpos($line, 'msgid ') === 0) {
        // Save previous translation
        if (!empty($current_msgid) && !empty($current_msgstr)) {
            $translations[$current_msgid] = $current_msgstr;
        }
        
        $current_msgid = substr($line, 7, -1); // Remove 'msgid "' and '"'
        $current_msgstr = '';
        $in_msgid = true;
        $in_msgstr = false;
    } elseif (strpos($line, 'msgstr ') === 0) {
        $current_msgstr = substr($line, 8, -1); // Remove 'msgstr "' and '"'
        $in_msgid = false;
        $in_msgstr = true;
    } elseif ($line[0] === '"' && $line[-1] === '"') {
        $content = substr($line, 1, -1); // Remove quotes
        if ($in_msgid) {
            $current_msgid .= $content;
        } elseif ($in_msgstr) {
            $current_msgstr .= $content;
        }
    }
}

// Save last translation
if (!empty($current_msgid) && !empty($current_msgstr)) {
    $translations[$current_msgid] = $current_msgstr;
}

echo "<p>Found " . count($translations) . " translations</p>";

// Create MO file content
$mo_content = '';

// MO file header
$mo_content .= pack('V', 0x950412de); // Magic number
$mo_content .= pack('V', 0); // Version
$mo_content .= pack('V', count($translations)); // Number of strings
$mo_content .= pack('V', 28); // Offset of table with original strings
$mo_content .= pack('V', 28 + count($translations) * 8); // Offset of table with translation strings
$mo_content .= pack('V', 0); // Hash table size
$mo_content .= pack('V', 0); // Hash table offset

// Calculate string offsets
$originals_offset = 28 + count($translations) * 16;
$translations_offset = $originals_offset;

foreach ($translations as $original => $translation) {
    $translations_offset += strlen($original) + 1;
}

// Write original strings table
$current_offset = $originals_offset;
foreach ($translations as $original => $translation) {
    $mo_content .= pack('V', strlen($original)); // Length
    $mo_content .= pack('V', $current_offset); // Offset
    $current_offset += strlen($original) + 1;
}

// Write translation strings table
$current_offset = $translations_offset;
foreach ($translations as $original => $translation) {
    $mo_content .= pack('V', strlen($translation)); // Length
    $mo_content .= pack('V', $current_offset); // Offset
    $current_offset += strlen($translation) + 1;
}

// Write original strings
foreach ($translations as $original => $translation) {
    $mo_content .= $original . "\0";
}

// Write translation strings
foreach ($translations as $original => $translation) {
    $mo_content .= $translation . "\0";
}

// Save MO file
$result = file_put_contents($mo_file, $mo_content);

if ($result !== false) {
    echo "<p style='color: green;'>✓ Successfully created MO file: $mo_file</p>";
    echo "<p>File size: " . filesize($mo_file) . " bytes</p>";
} else {
    echo "<p style='color: red;'>✗ Failed to create MO file</p>";
}

echo "<h2>Sample translations:</h2>";
echo "<ul>";
$count = 0;
foreach ($translations as $original => $translation) {
    if ($count++ >= 10) break;
    echo "<li><strong>$original</strong> → $translation</li>";
}
echo "</ul>";

echo "<p><a href='" . admin_url('admin.php?page=rss-aggregator') . "'>Go to RSS Aggregator</a></p>";
?>
