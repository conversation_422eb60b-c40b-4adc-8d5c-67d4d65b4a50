<?php
/**
 * Admin settings view
 *
 * @package RSS_Aggregator
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Display admin notices
$this->display_admin_notices();

// Get current settings
$default_frequency = get_option('rss_aggregator_default_frequency', 'hourly');
$max_items_per_feed = get_option('rss_aggregator_max_items_per_feed', 50);
$default_initial_count = get_option('rss_aggregator_default_initial_count', 10);
$enable_thumbnails = get_option('rss_aggregator_enable_thumbnails', 1);
$enable_buddyboss = get_option('rss_aggregator_enable_buddyboss', 1);
$enable_geodirectory = get_option('rss_aggregator_enable_geodirectory', 1);
$post_status = get_option('rss_aggregator_post_status', 'publish');
$post_author = get_option('rss_aggregator_post_author', 1);
$cleanup_days = get_option('rss_aggregator_cleanup_days', 30);

// Get available frequencies
$cron = new RSS_Cron();
$frequencies = $cron->get_available_frequencies();
?>

<div class="wrap">
    <h1><?php _e('RSS Aggregator Settings', 'rss-aggregator'); ?></h1>
    
    <hr class="wp-header-end">
    
    <form method="post" action="<?php echo admin_url('admin-post.php'); ?>">
        <?php wp_nonce_field('rss_aggregator_settings'); ?>
        <input type="hidden" name="action" value="rss_aggregator_save_settings">
        
        <div class="rss-settings-tabs">
            <nav class="nav-tab-wrapper">
                <a href="#general" class="nav-tab nav-tab-active"><?php _e('General', 'rss-aggregator'); ?></a>
                <a href="#integrations" class="nav-tab"><?php _e('Integrations', 'rss-aggregator'); ?></a>
                <a href="#posts" class="nav-tab"><?php _e('Posts', 'rss-aggregator'); ?></a>
                <a href="#maintenance" class="nav-tab"><?php _e('Maintenance', 'rss-aggregator'); ?></a>
            </nav>
            
            <!-- General Settings -->
            <div id="general" class="tab-content active">
                <h2><?php _e('General Settings', 'rss-aggregator'); ?></h2>
                
                <table class="form-table" role="presentation">
                    <tbody>
                        <tr>
                            <th scope="row">
                                <label for="default-frequency"><?php _e('Default Update Frequency', 'rss-aggregator'); ?></label>
                            </th>
                            <td>
                                <select id="default-frequency" name="rss_aggregator_default_frequency">
                                    <?php foreach ($frequencies as $key => $label): ?>
                                        <option value="<?php echo esc_attr($key); ?>" 
                                                <?php selected($default_frequency, $key); ?>>
                                            <?php echo esc_html($label); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <p class="description">
                                    <?php _e('Default frequency for new feeds. Can be overridden per feed.', 'rss-aggregator'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="max-items"><?php _e('Max Items per Feed', 'rss-aggregator'); ?></label>
                            </th>
                            <td>
                                <input type="number"
                                       id="max-items"
                                       name="rss_aggregator_max_items_per_feed"
                                       value="<?php echo esc_attr($max_items_per_feed); ?>"
                                       min="1"
                                       max="500"
                                       class="small-text">
                                <p class="description">
                                    <?php _e('Maximum number of items to process from each feed update.', 'rss-aggregator'); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="default-initial-count"><?php _e('Default Initial Import Count', 'rss-aggregator'); ?></label>
                            </th>
                            <td>
                                <input type="number"
                                       id="default-initial-count"
                                       name="rss_aggregator_default_initial_count"
                                       value="<?php echo esc_attr($default_initial_count); ?>"
                                       min="1"
                                       max="100"
                                       class="small-text">
                                <p class="description">
                                    <?php _e('Default number of newest posts to import when a new feed is added. This can be overridden for each individual feed.', 'rss-aggregator'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="enable-thumbnails"><?php _e('Enable Thumbnails', 'rss-aggregator'); ?></label>
                            </th>
                            <td>
                                <label>
                                    <input type="checkbox" 
                                           id="enable-thumbnails" 
                                           name="rss_aggregator_enable_thumbnails" 
                                           value="1" 
                                           <?php checked($enable_thumbnails, 1); ?>>
                                    <?php _e('Automatically download and set featured images from RSS feeds', 'rss-aggregator'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('When enabled, the plugin will try to extract thumbnails from RSS feeds and article pages.', 'rss-aggregator'); ?>
                                </p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- Integration Settings -->
            <div id="integrations" class="tab-content">
                <h2><?php _e('Integration Settings', 'rss-aggregator'); ?></h2>
                
                <table class="form-table" role="presentation">
                    <tbody>
                        <tr>
                            <th scope="row">
                                <label for="enable-buddyboss"><?php _e('BuddyBoss Integration', 'rss-aggregator'); ?></label>
                            </th>
                            <td>
                                <label>
                                    <input type="checkbox" 
                                           id="enable-buddyboss" 
                                           name="rss_aggregator_enable_buddyboss" 
                                           value="1" 
                                           <?php checked($enable_buddyboss, 1); ?>
                                           <?php disabled(!class_exists('BuddyPress')); ?>>
                                    <?php _e('Create activity stream entries for RSS posts', 'rss-aggregator'); ?>
                                </label>
                                <?php if (!class_exists('BuddyPress')): ?>
                                    <p class="description" style="color: #d63638;">
                                        <?php _e('BuddyBoss Platform is not active. This feature requires BuddyBoss to be installed and activated.', 'rss-aggregator'); ?>
                                    </p>
                                <?php else: ?>
                                    <p class="description">
                                        <?php _e('RSS posts will appear in the BuddyBoss activity stream with source information.', 'rss-aggregator'); ?>
                                    </p>
                                <?php endif; ?>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="enable-geodirectory"><?php _e('GeoDirectory Integration', 'rss-aggregator'); ?></label>
                            </th>
                            <td>
                                <label>
                                    <input type="checkbox" 
                                           id="enable-geodirectory" 
                                           name="rss_aggregator_enable_geodirectory" 
                                           value="1" 
                                           <?php checked($enable_geodirectory, 1); ?>
                                           <?php disabled(!class_exists('GeoDirectory')); ?>>
                                    <?php _e('Link RSS feeds to GeoDirectory places', 'rss-aggregator'); ?>
                                </label>
                                <?php if (!class_exists('GeoDirectory')): ?>
                                    <p class="description" style="color: #d63638;">
                                        <?php _e('GeoDirectory is not active. This feature requires GeoDirectory to be installed and activated.', 'rss-aggregator'); ?>
                                    </p>
                                <?php else: ?>
                                    <p class="description">
                                        <?php _e('RSS feeds can be associated with specific places and counties in GeoDirectory.', 'rss-aggregator'); ?>
                                    </p>
                                <?php endif; ?>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- Post Settings -->
            <div id="posts" class="tab-content">
                <h2><?php _e('Post Settings', 'rss-aggregator'); ?></h2>
                
                <table class="form-table" role="presentation">
                    <tbody>
                        <tr>
                            <th scope="row">
                                <label for="post-status"><?php _e('Default Post Status', 'rss-aggregator'); ?></label>
                            </th>
                            <td>
                                <select id="post-status" name="rss_aggregator_post_status">
                                    <?php foreach ($post_statuses as $status => $label): ?>
                                        <option value="<?php echo esc_attr($status); ?>" 
                                                <?php selected($post_status, $status); ?>>
                                            <?php echo esc_html($label); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <p class="description">
                                    <?php _e('Status for posts created from RSS feeds.', 'rss-aggregator'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="post-author"><?php _e('Default Post Author', 'rss-aggregator'); ?></label>
                            </th>
                            <td>
                                <select id="post-author" name="rss_aggregator_post_author">
                                    <?php foreach ($users as $user): ?>
                                        <option value="<?php echo esc_attr($user->ID); ?>" 
                                                <?php selected($post_author, $user->ID); ?>>
                                            <?php echo esc_html($user->display_name . ' (' . $user->user_login . ')'); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <p class="description">
                                    <?php _e('User to assign as author for posts created from RSS feeds.', 'rss-aggregator'); ?>
                                </p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- Maintenance Settings -->
            <div id="maintenance" class="tab-content">
                <h2><?php _e('Maintenance Settings', 'rss-aggregator'); ?></h2>
                
                <table class="form-table" role="presentation">
                    <tbody>
                        <tr>
                            <th scope="row">
                                <label for="cleanup-days"><?php _e('Cleanup Old Items', 'rss-aggregator'); ?></label>
                            </th>
                            <td>
                                <input type="number" 
                                       id="cleanup-days" 
                                       name="rss_aggregator_cleanup_days" 
                                       value="<?php echo esc_attr($cleanup_days); ?>" 
                                       min="1" 
                                       max="365" 
                                       class="small-text">
                                <span><?php _e('days', 'rss-aggregator'); ?></span>
                                <p class="description">
                                    <?php _e('Automatically delete RSS items older than this many days. Runs daily via cron.', 'rss-aggregator'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <?php _e('Manual Cleanup', 'rss-aggregator'); ?>
                            </th>
                            <td>
                                <button type="button" id="manual-cleanup" class="button">
                                    <?php _e('Run Cleanup Now', 'rss-aggregator'); ?>
                                </button>
                                <p class="description">
                                    <?php _e('Manually run the cleanup process to remove old RSS items.', 'rss-aggregator'); ?>
                                </p>
                                <div id="cleanup-results" style="margin-top: 10px;"></div>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <?php _e('Reset Cron Schedules', 'rss-aggregator'); ?>
                            </th>
                            <td>
                                <button type="button" id="reset-cron" class="button">
                                    <?php _e('Reset All Schedules', 'rss-aggregator'); ?>
                                </button>
                                <p class="description">
                                    <?php _e('Reset all cron schedules if feeds are not updating properly.', 'rss-aggregator'); ?>
                                </p>
                                <div id="cron-results" style="margin-top: 10px;"></div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <?php submit_button(__('Save Settings', 'rss-aggregator')); ?>
    </form>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Tab switching
    $('.nav-tab').on('click', function(e) {
        e.preventDefault();
        
        var target = $(this).attr('href');
        
        // Update active tab
        $('.nav-tab').removeClass('nav-tab-active');
        $(this).addClass('nav-tab-active');
        
        // Show target content
        $('.tab-content').removeClass('active');
        $(target).addClass('active');
    });
    
    // Manual cleanup
    $('#manual-cleanup').on('click', function(e) {
        e.preventDefault();
        
        var $button = $(this);
        var originalText = $button.text();
        var $results = $('#cleanup-results');
        
        $button.text('<?php _e('Running...', 'rss-aggregator'); ?>').prop('disabled', true);
        
        $.post(rssAggregatorAdmin.ajaxUrl, {
            action: 'rss_aggregator_manual_cleanup',
            nonce: rssAggregatorAdmin.nonce
        }, function(response) {
            $button.text(originalText).prop('disabled', false);
            
            if (response.success) {
                $results.html('<div class="notice notice-success inline"><p>' + response.data.message + '</p></div>');
            } else {
                $results.html('<div class="notice notice-error inline"><p>' + response.data + '</p></div>');
            }
        }).fail(function() {
            $button.text(originalText).prop('disabled', false);
            $results.html('<div class="notice notice-error inline"><p><?php _e('Failed to run cleanup.', 'rss-aggregator'); ?></p></div>');
        });
    });
    
    // Reset cron schedules
    $('#reset-cron').on('click', function(e) {
        e.preventDefault();
        
        if (!confirm('<?php _e('This will reset all cron schedules. Continue?', 'rss-aggregator'); ?>')) {
            return;
        }
        
        var $button = $(this);
        var originalText = $button.text();
        var $results = $('#cron-results');
        
        $button.text('<?php _e('Resetting...', 'rss-aggregator'); ?>').prop('disabled', true);
        
        $.post(rssAggregatorAdmin.ajaxUrl, {
            action: 'rss_aggregator_reset_cron',
            nonce: rssAggregatorAdmin.nonce
        }, function(response) {
            $button.text(originalText).prop('disabled', false);
            
            if (response.success) {
                $results.html('<div class="notice notice-success inline"><p>' + response.data.message + '</p></div>');
            } else {
                $results.html('<div class="notice notice-error inline"><p>' + response.data + '</p></div>');
            }
        }).fail(function() {
            $button.text(originalText).prop('disabled', false);
            $results.html('<div class="notice notice-error inline"><p><?php _e('Failed to reset cron schedules.', 'rss-aggregator'); ?></p></div>');
        });
    });
});
</script>
