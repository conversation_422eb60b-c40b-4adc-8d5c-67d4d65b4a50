<?php
/**
 * Admin status view
 *
 * @package RSS_Aggregator
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <h1><?php _e('RSS Aggregator Status', 'rss-aggregator'); ?></h1>
    
    <hr class="wp-header-end">
    
    <div class="rss-status-grid">
        <!-- System Status -->
        <div class="rss-status-card">
            <h2><?php _e('System Status', 'rss-aggregator'); ?></h2>
            
            <table class="rss-status-table">
                <tr>
                    <td><?php _e('WordPress Version:', 'rss-aggregator'); ?></td>
                    <td><?php echo get_bloginfo('version'); ?></td>
                </tr>
                <tr>
                    <td><?php _e('PHP Version:', 'rss-aggregator'); ?></td>
                    <td><?php echo PHP_VERSION; ?></td>
                </tr>
                <tr>
                    <td><?php _e('Plugin Version:', 'rss-aggregator'); ?></td>
                    <td><?php echo RSS_AGGREGATOR_VERSION; ?></td>
                </tr>
                <tr>
                    <td><?php _e('WP-Cron Status:', 'rss-aggregator'); ?></td>
                    <td>
                        <?php if ($cron_status['wp_cron_enabled']): ?>
                            <span class="status-good">
                                <span class="dashicons dashicons-yes-alt"></span>
                                <?php _e('Enabled', 'rss-aggregator'); ?>
                            </span>
                        <?php else: ?>
                            <span class="status-error">
                                <span class="dashicons dashicons-dismiss"></span>
                                <?php _e('Disabled', 'rss-aggregator'); ?>
                            </span>
                        <?php endif; ?>
                    </td>
                </tr>
                <tr>
                    <td><?php _e('BuddyBoss:', 'rss-aggregator'); ?></td>
                    <td>
                        <?php if (class_exists('BuddyPress')): ?>
                            <span class="status-good">
                                <span class="dashicons dashicons-yes-alt"></span>
                                <?php _e('Active', 'rss-aggregator'); ?>
                            </span>
                        <?php else: ?>
                            <span class="status-warning">
                                <span class="dashicons dashicons-warning"></span>
                                <?php _e('Not Active', 'rss-aggregator'); ?>
                            </span>
                        <?php endif; ?>
                    </td>
                </tr>
                <tr>
                    <td><?php _e('GeoDirectory:', 'rss-aggregator'); ?></td>
                    <td>
                        <?php if (class_exists('GeoDirectory')): ?>
                            <span class="status-good">
                                <span class="dashicons dashicons-yes-alt"></span>
                                <?php _e('Active', 'rss-aggregator'); ?>
                            </span>
                        <?php else: ?>
                            <span class="status-warning">
                                <span class="dashicons dashicons-warning"></span>
                                <?php _e('Not Active', 'rss-aggregator'); ?>
                            </span>
                        <?php endif; ?>
                    </td>
                </tr>
            </table>
        </div>
        
        <!-- Cron Status -->
        <div class="rss-status-card">
            <h2><?php _e('Cron Status', 'rss-aggregator'); ?></h2>
            
            <table class="rss-status-table">
                <tr>
                    <td><?php _e('Main Cron Job:', 'rss-aggregator'); ?></td>
                    <td>
                        <?php if ($cron_status['main_cron_scheduled']): ?>
                            <span class="status-good">
                                <span class="dashicons dashicons-yes-alt"></span>
                                <?php echo date('Y-m-d H:i:s', $cron_status['main_cron_scheduled']); ?>
                            </span>
                        <?php else: ?>
                            <span class="status-error">
                                <span class="dashicons dashicons-dismiss"></span>
                                <?php _e('Not Scheduled', 'rss-aggregator'); ?>
                            </span>
                        <?php endif; ?>
                    </td>
                </tr>
                <tr>
                    <td><?php _e('Cleanup Job:', 'rss-aggregator'); ?></td>
                    <td>
                        <?php if ($cron_status['cleanup_cron_scheduled']): ?>
                            <span class="status-good">
                                <span class="dashicons dashicons-yes-alt"></span>
                                <?php echo date('Y-m-d H:i:s', $cron_status['cleanup_cron_scheduled']); ?>
                            </span>
                        <?php else: ?>
                            <span class="status-error">
                                <span class="dashicons dashicons-dismiss"></span>
                                <?php _e('Not Scheduled', 'rss-aggregator'); ?>
                            </span>
                        <?php endif; ?>
                    </td>
                </tr>
                <tr>
                    <td><?php _e('Next Run:', 'rss-aggregator'); ?></td>
                    <td>
                        <?php if ($time_until_next['scheduled']): ?>
                            <?php if ($time_until_next['overdue']): ?>
                                <span class="status-warning">
                                    <span class="dashicons dashicons-warning"></span>
                                    <?php echo $time_until_next['message']; ?>
                                </span>
                            <?php else: ?>
                                <span class="status-good">
                                    <span class="dashicons dashicons-clock"></span>
                                    <?php echo $time_until_next['message']; ?>
                                </span>
                            <?php endif; ?>
                        <?php else: ?>
                            <span class="status-error">
                                <span class="dashicons dashicons-dismiss"></span>
                                <?php echo $time_until_next['message']; ?>
                            </span>
                        <?php endif; ?>
                    </td>
                </tr>
            </table>
            
            <div class="rss-status-actions">
                <button type="button" id="trigger-cron" class="button">
                    <?php _e('Trigger Cron Now', 'rss-aggregator'); ?>
                </button>
                <button type="button" id="reset-cron" class="button">
                    <?php _e('Reset Schedules', 'rss-aggregator'); ?>
                </button>
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="rss-status-card">
            <h2><?php _e('Statistics', 'rss-aggregator'); ?></h2>
            
            <div class="rss-stats-grid">
                <div class="rss-stat-item">
                    <div class="rss-stat-number"><?php echo $statistics['total_feeds']; ?></div>
                    <div class="rss-stat-label"><?php _e('Total Feeds', 'rss-aggregator'); ?></div>
                </div>
                <div class="rss-stat-item">
                    <div class="rss-stat-number"><?php echo $statistics['active_feeds']; ?></div>
                    <div class="rss-stat-label"><?php _e('Active Feeds', 'rss-aggregator'); ?></div>
                </div>
                <div class="rss-stat-item">
                    <div class="rss-stat-number"><?php echo $statistics['total_items']; ?></div>
                    <div class="rss-stat-label"><?php _e('Total Items', 'rss-aggregator'); ?></div>
                </div>
                <div class="rss-stat-item">
                    <div class="rss-stat-number"><?php echo $statistics['processed_items']; ?></div>
                    <div class="rss-stat-label"><?php _e('Processed Items', 'rss-aggregator'); ?></div>
                </div>
                <div class="rss-stat-item">
                    <div class="rss-stat-number"><?php echo $statistics['recent_items']; ?></div>
                    <div class="rss-stat-label"><?php _e('Last 24h', 'rss-aggregator'); ?></div>
                </div>
            </div>
        </div>
        
        <!-- Individual Feed Schedules -->
        <div class="rss-status-card rss-status-full-width">
            <h2><?php _e('Individual Feed Schedules', 'rss-aggregator'); ?></h2>
            
            <?php if (!empty($cron_status['individual_feeds'])): ?>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('Feed Name', 'rss-aggregator'); ?></th>
                            <th><?php _e('Frequency', 'rss-aggregator'); ?></th>
                            <th><?php _e('Next Run', 'rss-aggregator'); ?></th>
                            <th><?php _e('Status', 'rss-aggregator'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($cron_status['individual_feeds'] as $feed_schedule): ?>
                            <tr>
                                <td>
                                    <strong><?php echo esc_html($feed_schedule['feed_name']); ?></strong>
                                </td>
                                <td>
                                    <?php
                                    $frequencies = $cron->get_available_frequencies();
                                    echo isset($frequencies[$feed_schedule['frequency']]) ? $frequencies[$feed_schedule['frequency']] : $feed_schedule['frequency'];
                                    ?>
                                </td>
                                <td>
                                    <?php if ($feed_schedule['next_run']): ?>
                                        <span title="<?php echo esc_attr($feed_schedule['next_run_formatted']); ?>">
                                            <?php echo human_time_diff($feed_schedule['next_run'], current_time('timestamp')); ?>
                                            <?php if ($feed_schedule['next_run'] < time()): ?>
                                                <?php _e('ago (overdue)', 'rss-aggregator'); ?>
                                            <?php else: ?>
                                                <?php _e('from now', 'rss-aggregator'); ?>
                                            <?php endif; ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="status-error"><?php _e('Not scheduled', 'rss-aggregator'); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($feed_schedule['next_run']): ?>
                                        <?php if ($feed_schedule['next_run'] < time()): ?>
                                            <span class="status-warning">
                                                <span class="dashicons dashicons-warning"></span>
                                                <?php _e('Overdue', 'rss-aggregator'); ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="status-good">
                                                <span class="dashicons dashicons-yes-alt"></span>
                                                <?php _e('Scheduled', 'rss-aggregator'); ?>
                                            </span>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="status-error">
                                            <span class="dashicons dashicons-dismiss"></span>
                                            <?php _e('Not Scheduled', 'rss-aggregator'); ?>
                                        </span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p><?php _e('No active feeds found.', 'rss-aggregator'); ?></p>
            <?php endif; ?>
        </div>
        
        <!-- Recent Activity -->
        <div class="rss-status-card rss-status-full-width">
            <h2><?php _e('Recent Activity', 'rss-aggregator'); ?></h2>
            
            <?php if (!empty($recent_items)): ?>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('Title', 'rss-aggregator'); ?></th>
                            <th><?php _e('Feed', 'rss-aggregator'); ?></th>
                            <th><?php _e('Status', 'rss-aggregator'); ?></th>
                            <th><?php _e('Created', 'rss-aggregator'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_items as $item): ?>
                            <?php
                            $feed = $this->database->get_feed($item->feed_id);
                            ?>
                            <tr>
                                <td>
                                    <strong><?php echo esc_html($item->title); ?></strong>
                                    <?php if ($item->url): ?>
                                        <br><small>
                                            <a href="<?php echo esc_url($item->url); ?>" target="_blank" rel="noopener">
                                                <?php _e('View Original', 'rss-aggregator'); ?>
                                                <span class="dashicons dashicons-external"></span>
                                            </a>
                                        </small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php echo $feed ? esc_html($feed->name) : __('Unknown', 'rss-aggregator'); ?>
                                </td>
                                <td>
                                    <?php if ($item->processed): ?>
                                        <span class="status-good">
                                            <span class="dashicons dashicons-yes-alt"></span>
                                            <?php _e('Processed', 'rss-aggregator'); ?>
                                        </span>
                                        <?php if ($item->post_id): ?>
                                            <br><small>
                                                <a href="<?php echo get_edit_post_link($item->post_id); ?>">
                                                    <?php _e('Edit Post', 'rss-aggregator'); ?>
                                                </a>
                                            </small>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="status-warning">
                                            <span class="dashicons dashicons-warning"></span>
                                            <?php _e('Pending', 'rss-aggregator'); ?>
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span title="<?php echo esc_attr($item->created_at); ?>">
                                        <?php echo human_time_diff(strtotime($item->created_at), current_time('timestamp')) . ' ' . __('ago', 'rss-aggregator'); ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p><?php _e('No recent items found.', 'rss-aggregator'); ?></p>
            <?php endif; ?>
        </div>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Auto-refresh status every 30 seconds
    setInterval(function() {
        location.reload();
    }, 30000);
    
    // Trigger cron manually
    $('#trigger-cron').on('click', function(e) {
        e.preventDefault();
        
        var $button = $(this);
        var originalText = $button.text();
        
        $button.text('<?php _e('Running...', 'rss-aggregator'); ?>').prop('disabled', true);
        
        $.post(rssAggregatorAdmin.ajaxUrl, {
            action: 'rss_aggregator_trigger_cron',
            nonce: rssAggregatorAdmin.nonce
        }, function(response) {
            $button.text(originalText).prop('disabled', false);
            
            if (response.success) {
                $('<div class="notice notice-success is-dismissible"><p>' + response.data.message + '</p></div>')
                    .insertAfter('.wp-header-end')
                    .delay(3000)
                    .fadeOut();
                
                // Refresh page after 2 seconds
                setTimeout(function() {
                    location.reload();
                }, 2000);
            } else {
                $('<div class="notice notice-error is-dismissible"><p>' + response.data + '</p></div>')
                    .insertAfter('.wp-header-end')
                    .delay(3000)
                    .fadeOut();
            }
        }).fail(function() {
            $button.text(originalText).prop('disabled', false);
            $('<div class="notice notice-error is-dismissible"><p><?php _e('Failed to trigger cron.', 'rss-aggregator'); ?></p></div>')
                .insertAfter('.wp-header-end')
                .delay(3000)
                .fadeOut();
        });
    });
    
    // Reset cron schedules
    $('#reset-cron').on('click', function(e) {
        e.preventDefault();
        
        if (!confirm('<?php _e('This will reset all cron schedules. Continue?', 'rss-aggregator'); ?>')) {
            return;
        }
        
        var $button = $(this);
        var originalText = $button.text();
        
        $button.text('<?php _e('Resetting...', 'rss-aggregator'); ?>').prop('disabled', true);
        
        $.post(rssAggregatorAdmin.ajaxUrl, {
            action: 'rss_aggregator_reset_cron',
            nonce: rssAggregatorAdmin.nonce
        }, function(response) {
            $button.text(originalText).prop('disabled', false);
            
            if (response.success) {
                $('<div class="notice notice-success is-dismissible"><p>' + response.data.message + '</p></div>')
                    .insertAfter('.wp-header-end')
                    .delay(3000)
                    .fadeOut();
                
                // Refresh page after 2 seconds
                setTimeout(function() {
                    location.reload();
                }, 2000);
            } else {
                $('<div class="notice notice-error is-dismissible"><p>' + response.data + '</p></div>')
                    .insertAfter('.wp-header-end')
                    .delay(3000)
                    .fadeOut();
            }
        }).fail(function() {
            $button.text(originalText).prop('disabled', false);
            $('<div class="notice notice-error is-dismissible"><p><?php _e('Failed to reset cron schedules.', 'rss-aggregator'); ?></p></div>')
                .insertAfter('.wp-header-end')
                .delay(3000)
                .fadeOut();
        });
    });
});
</script>
