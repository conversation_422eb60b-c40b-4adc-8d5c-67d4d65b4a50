<?php
/**
 * Admin feed form view
 *
 * @package RSS_Aggregator
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Display admin notices
$this->display_admin_notices();
?>

<div class="wrap">
    <h1>
        <?php if ($edit_mode): ?>
            <?php _e('Edit RSS Feed', 'rss-aggregator'); ?>
        <?php else: ?>
            <?php _e('Add New RSS Feed', 'rss-aggregator'); ?>
        <?php endif; ?>
    </h1>
    
    <hr class="wp-header-end">
    
    <form method="post" action="<?php echo admin_url('admin-post.php'); ?>" class="rss-aggregator-form">
        <?php wp_nonce_field('rss_aggregator_save_feed'); ?>
        <input type="hidden" name="action" value="rss_aggregator_save_feed">
        
        <?php if ($edit_mode && $feed): ?>
            <input type="hidden" name="feed_id" value="<?php echo $feed->id; ?>">
        <?php endif; ?>
        
        <table class="form-table" role="presentation">
            <tbody>
                <!-- Feed Name -->
                <tr>
                    <th scope="row">
                        <label for="feed-name"><?php _e('Feed Name', 'rss-aggregator'); ?> <span class="required">*</span></label>
                    </th>
                    <td>
                        <input type="text" 
                               id="feed-name" 
                               name="name" 
                               class="regular-text" 
                               value="<?php echo $edit_mode && $feed ? esc_attr($feed->name) : ''; ?>" 
                               required>
                        <p class="description">
                            <?php _e('A descriptive name for this RSS feed.', 'rss-aggregator'); ?>
                        </p>
                    </td>
                </tr>
                
                <!-- Feed URL -->
                <tr>
                    <th scope="row">
                        <label for="feed-url"><?php _e('Feed URL', 'rss-aggregator'); ?> <span class="required">*</span></label>
                    </th>
                    <td>
                        <input type="url" 
                               id="feed-url" 
                               name="url" 
                               class="regular-text" 
                               value="<?php echo $edit_mode && $feed ? esc_attr($feed->url) : ''; ?>" 
                               required>
                        <button type="button" id="test-feed-btn" class="button">
                            <?php _e('Test Feed', 'rss-aggregator'); ?>
                        </button>
                        <p class="description">
                            <?php _e('The full URL to the RSS feed.', 'rss-aggregator'); ?>
                        </p>
                        <div id="test-feed-results" style="margin-top: 10px;"></div>
                    </td>
                </tr>
                
                <!-- County -->
                <tr>
                    <th scope="row">
                        <label for="feed-county"><?php _e('County', 'rss-aggregator'); ?></label>
                    </th>
                    <td>
                        <?php if (!empty($counties)): ?>
                            <select id="feed-county" name="county" class="regular-text">
                                <option value=""><?php _e('Select County', 'rss-aggregator'); ?></option>
                                <?php foreach ($counties as $county): ?>
                                    <option value="<?php echo esc_attr($county); ?>" 
                                            <?php selected($edit_mode && $feed ? $feed->county : '', $county); ?>>
                                        <?php echo esc_html($county); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <br><br>
                            <input type="text" 
                                   id="feed-county-custom" 
                                   name="county_custom" 
                                   class="regular-text" 
                                   placeholder="<?php _e('Or enter custom county', 'rss-aggregator'); ?>">
                        <?php else: ?>
                            <input type="text" 
                                   id="feed-county" 
                                   name="county" 
                                   class="regular-text" 
                                   value="<?php echo $edit_mode && $feed ? esc_attr($feed->county) : ''; ?>" 
                                   placeholder="<?php _e('Enter county name', 'rss-aggregator'); ?>">
                        <?php endif; ?>
                        <p class="description">
                            <?php _e('The county this feed represents. Used for categorization and BuddyBoss integration.', 'rss-aggregator'); ?>
                        </p>
                    </td>
                </tr>
                
                <!-- GeoDirectory Place -->
                <?php if (!empty($places)): ?>
                <tr>
                    <th scope="row">
                        <label for="geodirectory-place"><?php _e('GeoDirectory Place', 'rss-aggregator'); ?></label>
                    </th>
                    <td>
                        <select id="geodirectory-place" name="geodirectory_place_id" class="regular-text">
                            <option value=""><?php _e('Select Place', 'rss-aggregator'); ?></option>
                            <?php foreach ($places as $place_id => $place_name): ?>
                                <option value="<?php echo esc_attr($place_id); ?>" 
                                        <?php selected($edit_mode && $feed ? $feed->geodirectory_place_id : '', $place_id); ?>>
                                    <?php echo esc_html($place_name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <p class="description">
                            <?php _e('Link this feed to a specific place in GeoDirectory.', 'rss-aggregator'); ?>
                        </p>
                    </td>
                </tr>
                <?php endif; ?>
                
                <!-- Update Frequency -->
                <tr>
                    <th scope="row">
                        <label for="update-frequency"><?php _e('Update Frequency', 'rss-aggregator'); ?></label>
                    </th>
                    <td>
                        <select id="update-frequency" name="update_frequency" class="regular-text">
                            <?php foreach ($frequencies as $key => $label): ?>
                                <option value="<?php echo esc_attr($key); ?>"
                                        <?php selected($edit_mode && $feed ? $feed->update_frequency : 'hourly', $key); ?>>
                                    <?php echo esc_html($label); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <p class="description">
                            <?php _e('How often should this feed be checked for new items?', 'rss-aggregator'); ?>
                        </p>
                    </td>
                </tr>

                <!-- Initial Import Count -->
                <tr>
                    <th scope="row">
                        <label for="initial-import-count"><?php _e('Initial Import Count', 'rss-aggregator'); ?></label>
                    </th>
                    <td>
                        <input type="number"
                               id="initial-import-count"
                               name="initial_import_count"
                               class="small-text"
                               value="<?php echo $edit_mode && $feed ? esc_attr($feed->initial_import_count) : esc_attr(get_option('rss_aggregator_default_initial_count', 10)); ?>"
                               min="1"
                               max="100"
                               step="1">
                        <p class="description">
                            <?php _e('Number of newest posts to import when this feed is first added (1-100). This only applies to the first import - subsequent updates will import all new items.', 'rss-aggregator'); ?>
                        </p>
                    </td>
                </tr>
                
                <!-- Status -->
                <tr>
                    <th scope="row">
                        <label for="feed-status"><?php _e('Status', 'rss-aggregator'); ?></label>
                    </th>
                    <td>
                        <select id="feed-status" name="status" class="regular-text">
                            <option value="active" 
                                    <?php selected($edit_mode && $feed ? $feed->status : 'active', 'active'); ?>>
                                <?php _e('Active', 'rss-aggregator'); ?>
                            </option>
                            <option value="inactive" 
                                    <?php selected($edit_mode && $feed ? $feed->status : 'active', 'inactive'); ?>>
                                <?php _e('Inactive', 'rss-aggregator'); ?>
                            </option>
                        </select>
                        <p class="description">
                            <?php _e('Inactive feeds will not be updated automatically.', 'rss-aggregator'); ?>
                        </p>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <?php submit_button($edit_mode ? __('Update Feed', 'rss-aggregator') : __('Add Feed', 'rss-aggregator')); ?>
    </form>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Test feed functionality
    $('#test-feed-btn').on('click', function(e) {
        e.preventDefault();
        
        var feedUrl = $('#feed-url').val();
        if (!feedUrl) {
            alert('<?php _e('Please enter a feed URL first.', 'rss-aggregator'); ?>');
            return;
        }
        
        var $button = $(this);
        var originalText = $button.text();
        var $results = $('#test-feed-results');
        
        $button.text(rssAggregatorAdmin.strings.testing).prop('disabled', true);
        $results.html('<div class="spinner is-active" style="float: none; margin: 0;"></div>');
        
        $.post(rssAggregatorAdmin.ajaxUrl, {
            action: 'rss_aggregator_test_feed',
            nonce: rssAggregatorAdmin.nonce,
            feed_url: feedUrl
        }, function(response) {
            $button.text(originalText).prop('disabled', false);
            
            var resultsHtml = '';
            if (response.success) {
                resultsHtml = '<div class="notice notice-success inline"><p><strong>' + 
                             rssAggregatorAdmin.strings.success + ':</strong> ' + 
                             response.data.message + '</p></div>';
                
                if (response.data.sample_items && response.data.sample_items.length > 0) {
                    resultsHtml += '<div class="rss-sample-items">';
                    resultsHtml += '<h4><?php _e('Sample Items:', 'rss-aggregator'); ?></h4>';
                    resultsHtml += '<ul>';
                    response.data.sample_items.forEach(function(item) {
                        resultsHtml += '<li>';
                        resultsHtml += '<strong>' + item.title + '</strong><br>';
                        resultsHtml += '<small><a href="' + item.url + '" target="_blank">' + item.url + '</a></small>';
                        if (item.pub_date) {
                            resultsHtml += '<br><small><?php _e('Published:', 'rss-aggregator'); ?> ' + item.pub_date + '</small>';
                        }
                        resultsHtml += '</li>';
                    });
                    resultsHtml += '</ul>';
                    resultsHtml += '</div>';
                }
            } else {
                resultsHtml = '<div class="notice notice-error inline"><p><strong>' + 
                             rssAggregatorAdmin.strings.error + ':</strong> ' + 
                             response.data + '</p></div>';
            }
            
            $results.html(resultsHtml);
        }).fail(function() {
            $button.text(originalText).prop('disabled', false);
            $results.html('<div class="notice notice-error inline"><p>' + 
                         '<?php _e('Failed to test feed. Please try again.', 'rss-aggregator'); ?>' + 
                         '</p></div>');
        });
    });
    
    // Handle custom county input
    $('#feed-county-custom').on('input', function() {
        if ($(this).val()) {
            $('#feed-county').val('');
        }
    });
    
    $('#feed-county').on('change', function() {
        if ($(this).val()) {
            $('#feed-county-custom').val('');
        }
    });
    
    // Form submission handler
    $('.rss-aggregator-form').on('submit', function(e) {
        // Use custom county if provided
        var customCounty = $('#feed-county-custom').val();
        if (customCounty) {
            $('#feed-county').val(customCounty);
        }
        
        // Basic validation
        var name = $('#feed-name').val().trim();
        var url = $('#feed-url').val().trim();
        
        if (!name) {
            alert('<?php _e('Please enter a feed name.', 'rss-aggregator'); ?>');
            $('#feed-name').focus();
            return false;
        }
        
        if (!url) {
            alert('<?php _e('Please enter a feed URL.', 'rss-aggregator'); ?>');
            $('#feed-url').focus();
            return false;
        }
        
        // URL validation
        var urlPattern = /^https?:\/\/.+/i;
        if (!urlPattern.test(url)) {
            alert('<?php _e('Please enter a valid URL starting with http:// or https://', 'rss-aggregator'); ?>');
            $('#feed-url').focus();
            return false;
        }
        
        return true;
    });
    
    // Auto-generate feed name from URL
    $('#feed-url').on('blur', function() {
        var url = $(this).val();
        var name = $('#feed-name').val();
        
        if (url && !name) {
            try {
                var hostname = new URL(url).hostname;
                var suggestedName = hostname.replace('www.', '').replace(/\./g, ' ').toUpperCase();
                $('#feed-name').val(suggestedName + ' RSS Feed');
            } catch (e) {
                // Invalid URL, ignore
            }
        }
    });
});
</script>
