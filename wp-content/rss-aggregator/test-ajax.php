<?php
/**
 * Test AJAX functionality for RSS Aggregator
 * 
 * Access via: /wp-content/plugins/rss-aggregator/test-ajax.php
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. You must be an administrator to run this script.');
}

echo "<h1>RSS Aggregator AJAX Test</h1>";

// Test database connection
global $wpdb;
echo "<h2>Database Connection Test</h2>";

$feeds_table = $wpdb->prefix . 'rss_aggregator_feeds';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$feeds_table}'") == $feeds_table;

if ($table_exists) {
    echo "<p style='color: green;'>✓ Feeds table exists: {$feeds_table}</p>";
    
    // Show table structure
    $columns = $wpdb->get_results("SHOW COLUMNS FROM {$feeds_table}");
    echo "<h3>Table Structure:</h3>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li><strong>{$column->Field}</strong> - {$column->Type}</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: red;'>✗ Feeds table does not exist: {$feeds_table}</p>";
}

// Test RSS Database class
echo "<h2>RSS Database Class Test</h2>";

if (class_exists('RSS_Database')) {
    $database = new RSS_Database();
    echo "<p style='color: green;'>✓ RSS_Database class loaded</p>";
    
    // Test search functions
    echo "<h3>Testing Search Functions:</h3>";
    
    // Test places search
    echo "<h4>Places Search Test:</h4>";
    $places = $database->search_geodirectory_places('war', 5);
    echo "<p>Search for 'war' returned " . count($places) . " results:</p>";
    if (!empty($places)) {
        echo "<ul>";
        foreach ($places as $place) {
            echo "<li>ID: {$place->id}, Name: {$place->name}, City: {$place->city}, Region: {$place->region}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No places found</p>";
    }
    
    // Test users search
    echo "<h4>Users Search Test:</h4>";
    $users = $database->search_users('admin', 5);
    echo "<p>Search for 'admin' returned " . count($users) . " results:</p>";
    if (!empty($users)) {
        echo "<ul>";
        foreach ($users as $user) {
            echo "<li>ID: {$user->id}, Name: {$user->name}, Login: {$user->login}, Email: {$user->email}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No users found</p>";
    }
    
    // Test counties search
    echo "<h4>Counties Search Test:</h4>";
    $counties = $database->search_counties('dąb', 5);
    echo "<p>Search for 'dąb' returned " . count($counties) . " results:</p>";
    if (!empty($counties)) {
        echo "<ul>";
        foreach ($counties as $county) {
            echo "<li>Name: {$county->name}, Region: {$county->region}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No counties found</p>";
    }
    
} else {
    echo "<p style='color: red;'>✗ RSS_Database class not found</p>";
}

// Test WordPress users
echo "<h2>WordPress Users Test</h2>";
$wp_users = get_users(array('number' => 5));
echo "<p>Found " . count($wp_users) . " WordPress users:</p>";
if (!empty($wp_users)) {
    echo "<ul>";
    foreach ($wp_users as $user) {
        echo "<li>ID: {$user->ID}, Name: {$user->display_name}, Login: {$user->user_login}</li>";
    }
    echo "</ul>";
}

// Test GeoDirectory
echo "<h2>GeoDirectory Test</h2>";
if (class_exists('GeoDirectory')) {
    echo "<p style='color: green;'>✓ GeoDirectory plugin is active</p>";
} else {
    echo "<p style='color: orange;'>⚠ GeoDirectory plugin is not active</p>";
}

// Check for GeoDirectory posts
$gd_posts = get_posts(array(
    'post_type' => array('gd_place', 'geodir_place', 'place'),
    'numberposts' => 5,
    'post_status' => 'publish'
));

echo "<p>Found " . count($gd_posts) . " GeoDirectory place posts:</p>";
if (!empty($gd_posts)) {
    echo "<ul>";
    foreach ($gd_posts as $post) {
        echo "<li>ID: {$post->ID}, Title: {$post->post_title}, Type: {$post->post_type}</li>";
    }
    echo "</ul>";
}

// Test AJAX URLs
echo "<h2>AJAX Configuration Test</h2>";
echo "<p>WordPress AJAX URL: " . admin_url('admin-ajax.php') . "</p>";
echo "<p>Current user can manage options: " . (current_user_can('manage_options') ? 'Yes' : 'No') . "</p>";
echo "<p>Nonce for testing: " . wp_create_nonce('rss_aggregator_admin') . "</p>";

// JavaScript test
echo "<h2>JavaScript AJAX Test</h2>";
?>
<div id="ajax-test-results"></div>
<button id="test-places" type="button">Test Places Search</button>
<button id="test-users" type="button">Test Users Search</button>
<button id="test-counties" type="button">Test Counties Search</button>

<script type="text/javascript">
jQuery(document).ready(function($) {
    var ajaxUrl = '<?php echo admin_url('admin-ajax.php'); ?>';
    var nonce = '<?php echo wp_create_nonce('rss_aggregator_admin'); ?>';
    
    $('#test-places').on('click', function() {
        $('#ajax-test-results').html('Testing places search...');
        
        $.post(ajaxUrl, {
            action: 'rss_aggregator_search_places',
            search: 'war',
            nonce: nonce
        }, function(response) {
            console.log('Places response:', response);
            if (response.success) {
                $('#ajax-test-results').html('<h4>Places Results:</h4><pre>' + JSON.stringify(response.data, null, 2) + '</pre>');
            } else {
                $('#ajax-test-results').html('<h4>Places Error:</h4><p style="color: red;">' + response.data + '</p>');
            }
        }).fail(function(xhr, status, error) {
            $('#ajax-test-results').html('<h4>Places AJAX Error:</h4><p style="color: red;">' + error + '</p>');
            console.log('AJAX Error:', xhr.responseText);
        });
    });
    
    $('#test-users').on('click', function() {
        $('#ajax-test-results').html('Testing users search...');
        
        $.post(ajaxUrl, {
            action: 'rss_aggregator_search_users',
            search: 'admin',
            nonce: nonce
        }, function(response) {
            console.log('Users response:', response);
            if (response.success) {
                $('#ajax-test-results').html('<h4>Users Results:</h4><pre>' + JSON.stringify(response.data, null, 2) + '</pre>');
            } else {
                $('#ajax-test-results').html('<h4>Users Error:</h4><p style="color: red;">' + response.data + '</p>');
            }
        }).fail(function(xhr, status, error) {
            $('#ajax-test-results').html('<h4>Users AJAX Error:</h4><p style="color: red;">' + error + '</p>');
            console.log('AJAX Error:', xhr.responseText);
        });
    });
    
    $('#test-counties').on('click', function() {
        $('#ajax-test-results').html('Testing counties search...');
        
        $.post(ajaxUrl, {
            action: 'rss_aggregator_search_counties',
            search: 'dąb',
            nonce: nonce
        }, function(response) {
            console.log('Counties response:', response);
            if (response.success) {
                $('#ajax-test-results').html('<h4>Counties Results:</h4><pre>' + JSON.stringify(response.data, null, 2) + '</pre>');
            } else {
                $('#ajax-test-results').html('<h4>Counties Error:</h4><p style="color: red;">' + response.data + '</p>');
            }
        }).fail(function(xhr, status, error) {
            $('#ajax-test-results').html('<h4>Counties AJAX Error:</h4><p style="color: red;">' + error + '</p>');
            console.log('AJAX Error:', xhr.responseText);
        });
    });
});
</script>

<p><a href="<?php echo admin_url('admin.php?page=rss-aggregator'); ?>">Go to RSS Aggregator</a></p>
