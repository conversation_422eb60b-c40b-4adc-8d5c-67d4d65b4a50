# RSS Aggregator Plugin dla WordPress

## Opis
Wtyczka do agregacji treści z kanałów RSS portali informacyjnych z integracją BuddyBoss i GeoDirectory.

## Funkcjonalności
- Agregacja kanałów RSS z zaawansowanym pobieraniem miniaturek
- **Kontrola pierwszego importu**: Wybór liczby najnowszych wpisów (1-100) przy dodaniu kanału
- **Przypisywanie do użytkowników i miejsc**: Kanały mogą być przypisane do użytkowników WordPress i/lub miejsc GeoDirectory
- **Inteligentne pola tekstowe**: Autocomplete dla miejsc i użytkowników z podpowiedziami
- **Zaawansowane pobieranie miniaturek**: Automatyczne pobieranie z og:image, twitter:image i treści strony
- Integracja z GeoDirectory (przypisywanie do miejsc i powiatów)
- Integracja z BuddyBoss (wyświetlanie w strumieniu aktywności)
- Panel administracyjny do zarządzania kanałami
- Automatyczne aktualizacje przez WP-Cron
- Indywidualne ustawienia częstotliwości dla każdego kanału

## Struktura plików

```
wp-content/plugins/rss-aggregator/
├── rss-aggregator.php              # Główny plik wtyczki
├── includes/
│   ├── class-rss-aggregator.php    # Główna klasa wtyczki
│   ├── class-rss-fetcher.php       # Klasa do pobierania RSS
│   ├── class-rss-parser.php        # Klasa do parsowania RSS
│   ├── class-rss-database.php      # Klasa do zarządzania bazą danych
│   ├── class-rss-cron.php          # Klasa do zarządzania WP-Cron
│   └── class-rss-integrations.php  # Klasa integracji z BuddyBoss/GeoDirectory
├── admin/
│   ├── class-rss-admin.php         # Panel administracyjny
│   ├── partials/
│   │   ├── admin-display.php       # Główny widok panelu
│   │   ├── feed-form.php           # Formularz dodawania/edycji kanału
│   │   └── feed-list.php           # Lista kanałów
│   ├── css/
│   │   └── admin.css               # Style panelu administracyjnego
│   └── js/
│       └── admin.js                # JavaScript panelu administracyjnego
├── public/
│   ├── class-rss-public.php        # Funkcjonalności publiczne
│   ├── css/
│   │   └── public.css              # Style publiczne
│   └── js/
│       └── public.js               # JavaScript publiczny
├── languages/                      # Pliki tłumaczeń
├── assets/                         # Zasoby (ikony, obrazy)
└── uninstall.php                   # Skrypt dezinstalacji
```

## Schemat bazy danych

### Tabela: wp_rss_aggregator_feeds
```sql
CREATE TABLE wp_rss_aggregator_feeds (
    id int(11) NOT NULL AUTO_INCREMENT,
    name varchar(255) NOT NULL,
    url varchar(500) NOT NULL,
    geodirectory_place_id bigint(20) DEFAULT NULL,
    county varchar(100) DEFAULT NULL,
    update_frequency varchar(50) DEFAULT 'hourly',
    last_updated datetime DEFAULT NULL,
    status enum('active','inactive') DEFAULT 'active',
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY url (url),
    KEY geodirectory_place_id (geodirectory_place_id),
    KEY county (county),
    KEY status (status)
);
```

### Tabela: wp_rss_aggregator_items
```sql
CREATE TABLE wp_rss_aggregator_items (
    id int(11) NOT NULL AUTO_INCREMENT,
    feed_id int(11) NOT NULL,
    post_id bigint(20) DEFAULT NULL,
    title varchar(500) NOT NULL,
    description text,
    url varchar(500) NOT NULL,
    guid varchar(500) DEFAULT NULL,
    pub_date datetime DEFAULT NULL,
    thumbnail_url varchar(500) DEFAULT NULL,
    thumbnail_local varchar(500) DEFAULT NULL,
    processed tinyint(1) DEFAULT 0,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY feed_guid (feed_id, guid),
    KEY feed_id (feed_id),
    KEY post_id (post_id),
    KEY processed (processed),
    KEY pub_date (pub_date),
    FOREIGN KEY (feed_id) REFERENCES wp_rss_aggregator_feeds(id) ON DELETE CASCADE
);
```

## Klasy główne

### RSS_Aggregator
- Główna klasa wtyczki
- Inicjalizacja wszystkich komponentów
- Zarządzanie hookami WordPress

### RSS_Fetcher
- Pobieranie kanałów RSS
- Obsługa błędów połączenia
- Cache'owanie wyników

### RSS_Parser
- Parsowanie treści RSS
- Pobieranie miniaturek z kanału lub strony
- Walidacja danych

### RSS_Database
- Operacje na bazie danych
- CRUD dla kanałów i elementów
- Migracje schematu

### RSS_Cron
- Zarządzanie zadaniami WP-Cron
- Planowanie aktualizacji
- Optymalizacja wydajności

### RSS_Integrations
- Integracja z BuddyBoss
- Integracja z GeoDirectory
- Publikacja w strumieniu aktywności

## Integracje

### BuddyBoss
- Hook: `bp_activity_post_type_publish`
- Publikacja wpisów RSS jako aktywności
- Wyświetlanie miniaturek w strumieniu

### GeoDirectory
- Powiązanie z miejscami (`gd_place`)
- Wykorzystanie pól niestandardowych dla powiatów
- Integracja z systemem lokalizacji

## WP-Cron
- Dynamiczne planowanie zadań
- Różne częstotliwości dla każdego kanału
- Optymalizacja dla środowiska WebCloud OVH
