<?php
/**
 * Test thumbnail extraction functionality
 * 
 * Access via: /wp-content/plugins/rss-aggregator/test-thumbnails.php
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. You must be an administrator to run this script.');
}

echo "<h1>RSS Aggregator Thumbnail Extraction Test</h1>";

// Test URLs
$test_urls = array(
    'https://rdn.pl/feed/',
    'https://example.com/sample-article',
    'https://wordpress.org/news/',
);

// Test RSS item
$test_item = array(
    'title' => 'Test Article',
    'url' => 'https://rdn.pl/sample-article',
    'description' => 'Test description with <img src="https://example.com/image.jpg" alt="test">',
    'content' => 'Test content with images'
);

echo "<h2>Settings Check</h2>";
$thumbnails_enabled = get_option('rss_aggregator_enable_thumbnails', 1);
echo "<p>Thumbnails enabled: " . ($thumbnails_enabled ? 'YES' : 'NO') . "</p>";

if (class_exists('RSS_Thumbnail_Extractor')) {
    $extractor = new RSS_Thumbnail_Extractor();
    echo "<p style='color: green;'>✓ RSS_Thumbnail_Extractor class loaded</p>";
    
    echo "<h2>Test RSS Item Extraction</h2>";
    $thumbnail = $extractor->extract_thumbnail($test_item);
    echo "<p>Extracted thumbnail: " . ($thumbnail ? $thumbnail : 'none') . "</p>";
    
    echo "<h2>Test Direct URL Extraction</h2>";
    foreach ($test_urls as $url) {
        echo "<h3>Testing: {$url}</h3>";
        
        // Create a fake RSS item
        $fake_item = array(
            'title' => 'Test from ' . $url,
            'url' => $url,
            'description' => '',
            'content' => ''
        );
        
        $thumbnail = $extractor->extract_thumbnail($fake_item);
        echo "<p>Result: " . ($thumbnail ? "<a href='{$thumbnail}' target='_blank'>{$thumbnail}</a>" : 'No thumbnail found') . "</p>";
    }
    
} else {
    echo "<p style='color: red;'>✗ RSS_Thumbnail_Extractor class not found</p>";
}

// Test WordPress media functions
echo "<h2>WordPress Media Functions Test</h2>";
echo "<p>media_handle_sideload function exists: " . (function_exists('media_handle_sideload') ? 'YES' : 'NO') . "</p>";
echo "<p>wp_remote_get function exists: " . (function_exists('wp_remote_get') ? 'YES' : 'NO') . "</p>";

// Test a simple image download
echo "<h2>Test Image Download</h2>";
$test_image_url = 'https://via.placeholder.com/300x200.jpg';
echo "<p>Testing download from: {$test_image_url}</p>";

$response = wp_remote_get($test_image_url, array('timeout' => 15));
if (is_wp_error($response)) {
    echo "<p style='color: red;'>Error: " . $response->get_error_message() . "</p>";
} else {
    $status_code = wp_remote_retrieve_response_code($response);
    $content_type = wp_remote_retrieve_header($response, 'content-type');
    $content_length = wp_remote_retrieve_header($response, 'content-length');
    
    echo "<p>Status: {$status_code}</p>";
    echo "<p>Content-Type: {$content_type}</p>";
    echo "<p>Content-Length: {$content_length} bytes</p>";
    
    if ($status_code === 200) {
        echo "<p style='color: green;'>✓ Image download successful</p>";
    } else {
        echo "<p style='color: red;'>✗ Image download failed</p>";
    }
}

// Test upload directory
echo "<h2>Upload Directory Test</h2>";
$upload_dir = wp_upload_dir();
echo "<p>Upload path: {$upload_dir['path']}</p>";
echo "<p>Upload URL: {$upload_dir['url']}</p>";
echo "<p>Upload writable: " . (is_writable($upload_dir['path']) ? 'YES' : 'NO') . "</p>";

// Check recent RSS posts
echo "<h2>Recent RSS Posts</h2>";
$rss_posts = get_posts(array(
    'meta_key' => '_rss_aggregator_feed_id',
    'numberposts' => 5,
    'post_status' => 'publish'
));

if ($rss_posts) {
    echo "<p>Found " . count($rss_posts) . " RSS posts:</p>";
    echo "<ul>";
    foreach ($rss_posts as $post) {
        $feed_id = get_post_meta($post->ID, '_rss_aggregator_feed_id', true);
        $thumbnail_id = get_post_thumbnail_id($post->ID);
        $thumbnail_url = $thumbnail_id ? wp_get_attachment_url($thumbnail_id) : 'none';
        
        echo "<li>";
        echo "<strong>{$post->post_title}</strong> (ID: {$post->ID}, Feed: {$feed_id})<br>";
        echo "Thumbnail: {$thumbnail_url}";
        echo "</li>";
    }
    echo "</ul>";
} else {
    echo "<p>No RSS posts found</p>";
}

// Manual thumbnail extraction test
echo "<h2>Manual Extraction Test</h2>";
$manual_url = $_GET['test_url'] ?? '';
if ($manual_url) {
    echo "<h3>Testing URL: {$manual_url}</h3>";
    
    if (class_exists('RSS_Thumbnail_Extractor')) {
        $extractor = new RSS_Thumbnail_Extractor();
        $fake_item = array(
            'title' => 'Manual test',
            'url' => $manual_url,
            'description' => '',
            'content' => ''
        );
        
        $thumbnail = $extractor->extract_thumbnail($fake_item);
        if ($thumbnail) {
            echo "<p>Found thumbnail: <a href='{$thumbnail}' target='_blank'>{$thumbnail}</a></p>";
            echo "<img src='{$thumbnail}' style='max-width: 300px; height: auto;' alt='Extracted thumbnail'>";
        } else {
            echo "<p>No thumbnail found</p>";
        }
    }
}

echo "<form method='get'>";
echo "<p>Test custom URL: <input type='url' name='test_url' value='{$manual_url}' placeholder='https://example.com/article'>";
echo "<input type='submit' value='Test'></p>";
echo "</form>";

echo "<p><a href='" . admin_url('admin.php?page=rss-aggregator') . "'>Go to RSS Aggregator</a></p>";
?>
