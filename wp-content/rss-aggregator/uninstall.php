<?php
/**
 * RSS Aggregator Uninstall
 *
 * @package RSS_Aggregator
 * @since 1.0.0
 */

// If uninstall not called from WordPress, then exit
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

// Define plugin constants if not already defined
if (!defined('RSS_AGGREGATOR_VERSION')) {
    define('RSS_AGGREGATOR_VERSION', '1.0.0');
}

global $wpdb;

// Define table names
$feeds_table = $wpdb->prefix . 'rss_aggregator_feeds';
$items_table = $wpdb->prefix . 'rss_aggregator_items';

/**
 * Remove all plugin data
 */
function rss_aggregator_uninstall_cleanup() {
    global $wpdb, $feeds_table, $items_table;
    
    // Remove all RSS aggregated posts
    rss_aggregator_remove_posts();
    
    // Remove all plugin options
    rss_aggregator_remove_options();
    
    // Remove custom tables
    rss_aggregator_remove_tables();
    
    // Clear all cron jobs
    rss_aggregator_clear_cron_jobs();
    
    // Remove user meta
    rss_aggregator_remove_user_meta();
    
    // Remove transients
    rss_aggregator_remove_transients();
    
    // Remove uploaded files
    rss_aggregator_remove_uploaded_files();
}

/**
 * Remove all RSS aggregated posts
 */
function rss_aggregator_remove_posts() {
    global $wpdb;
    
    // Get all posts created by RSS aggregator
    $post_ids = $wpdb->get_col("
        SELECT post_id 
        FROM {$wpdb->postmeta} 
        WHERE meta_key = '_rss_aggregator_feed_id'
    ");
    
    if (!empty($post_ids)) {
        foreach ($post_ids as $post_id) {
            // Remove post attachments (thumbnails)
            $attachments = get_attached_media('', $post_id);
            foreach ($attachments as $attachment) {
                wp_delete_attachment($attachment->ID, true);
            }
            
            // Remove the post
            wp_delete_post($post_id, true);
        }
    }
    
    // Remove any orphaned meta data
    $wpdb->query("
        DELETE FROM {$wpdb->postmeta} 
        WHERE meta_key LIKE '_rss_aggregator_%'
    ");
}

/**
 * Remove all plugin options
 */
function rss_aggregator_remove_options() {
    $options = array(
        'rss_aggregator_db_version',
        'rss_aggregator_default_frequency',
        'rss_aggregator_max_items_per_feed',
        'rss_aggregator_enable_thumbnails',
        'rss_aggregator_enable_buddyboss',
        'rss_aggregator_enable_geodirectory',
        'rss_aggregator_post_status',
        'rss_aggregator_post_author',
        'rss_aggregator_cleanup_days'
    );
    
    foreach ($options as $option) {
        delete_option($option);
    }
    
    // Remove any other options that might have been added
    global $wpdb;
    $wpdb->query("
        DELETE FROM {$wpdb->options} 
        WHERE option_name LIKE 'rss_aggregator_%'
    ");
}

/**
 * Remove custom database tables
 */
function rss_aggregator_remove_tables() {
    global $wpdb, $feeds_table, $items_table;
    
    // Drop items table first (foreign key constraint)
    $wpdb->query("DROP TABLE IF EXISTS {$items_table}");
    
    // Drop feeds table
    $wpdb->query("DROP TABLE IF EXISTS {$feeds_table}");
}

/**
 * Clear all cron jobs
 */
function rss_aggregator_clear_cron_jobs() {
    // Clear main cron jobs
    wp_clear_scheduled_hook('rss_aggregator_check_feeds');
    wp_clear_scheduled_hook('rss_aggregator_cleanup');
    
    // Clear individual feed cron jobs
    global $wpdb;
    $feeds_table = $wpdb->prefix . 'rss_aggregator_feeds';
    
    // Check if table exists before querying
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$feeds_table}'") === $feeds_table;
    
    if ($table_exists) {
        $feeds = $wpdb->get_results("SELECT id FROM {$feeds_table}");
        foreach ($feeds as $feed) {
            wp_clear_scheduled_hook('rss_aggregator_update_feed_' . $feed->id);
        }
    }
    
    // Clear any other RSS aggregator cron jobs
    $cron_jobs = _get_cron_array();
    foreach ($cron_jobs as $timestamp => $cron) {
        foreach ($cron as $hook => $dings) {
            if (strpos($hook, 'rss_aggregator_') === 0) {
                wp_clear_scheduled_hook($hook);
            }
        }
    }
}

/**
 * Remove user meta data
 */
function rss_aggregator_remove_user_meta() {
    global $wpdb;
    
    $wpdb->query("
        DELETE FROM {$wpdb->usermeta} 
        WHERE meta_key LIKE 'rss_aggregator_%'
    ");
}

/**
 * Remove transients
 */
function rss_aggregator_remove_transients() {
    global $wpdb;
    
    // Remove transients
    $wpdb->query("
        DELETE FROM {$wpdb->options} 
        WHERE option_name LIKE '_transient_rss_aggregator_%' 
        OR option_name LIKE '_transient_timeout_rss_aggregator_%'
    ");
    
    // Remove site transients
    $wpdb->query("
        DELETE FROM {$wpdb->options} 
        WHERE option_name LIKE '_site_transient_rss_aggregator_%' 
        OR option_name LIKE '_site_transient_timeout_rss_aggregator_%'
    ");
}

/**
 * Remove uploaded files
 */
function rss_aggregator_remove_uploaded_files() {
    $upload_dir = wp_upload_dir();
    $rss_dir = $upload_dir['basedir'] . '/rss-aggregator/';
    
    if (is_dir($rss_dir)) {
        rss_aggregator_remove_directory($rss_dir);
    }
}

/**
 * Recursively remove directory
 *
 * @param string $dir Directory path
 */
function rss_aggregator_remove_directory($dir) {
    if (!is_dir($dir)) {
        return;
    }
    
    $files = array_diff(scandir($dir), array('.', '..'));
    
    foreach ($files as $file) {
        $path = $dir . DIRECTORY_SEPARATOR . $file;
        
        if (is_dir($path)) {
            rss_aggregator_remove_directory($path);
        } else {
            unlink($path);
        }
    }
    
    rmdir($dir);
}

/**
 * Remove BuddyBoss activity entries
 */
function rss_aggregator_remove_buddyboss_activities() {
    if (!function_exists('bp_activity_delete_by_item_id')) {
        return;
    }
    
    global $wpdb;
    
    // Get all RSS aggregator activity IDs
    $activity_ids = $wpdb->get_col("
        SELECT activity_id 
        FROM {$wpdb->prefix}bp_activity_meta 
        WHERE meta_key = 'rss_aggregator_feed_id'
    ");
    
    if (!empty($activity_ids)) {
        foreach ($activity_ids as $activity_id) {
            bp_activity_delete(array('id' => $activity_id));
        }
    }
}

/**
 * Remove GeoDirectory custom fields
 */
function rss_aggregator_remove_geodirectory_fields() {
    if (!class_exists('GeoDirectory')) {
        return;
    }
    
    global $wpdb;
    
    // Remove custom fields if they exist
    $custom_fields_table = $wpdb->prefix . 'geodir_custom_fields';
    
    if ($wpdb->get_var("SHOW TABLES LIKE '{$custom_fields_table}'") === $custom_fields_table) {
        $wpdb->query("
            DELETE FROM {$custom_fields_table} 
            WHERE htmlvar_name IN ('rss_feed_url', 'county')
        ");
    }
    
    // Remove meta data
    $wpdb->query("
        DELETE FROM {$wpdb->postmeta} 
        WHERE meta_key IN ('rss_feed_url', 'county', '_rss_aggregator_posts')
    ");
}

/**
 * Log uninstall process
 */
function rss_aggregator_log_uninstall() {
    $log_message = sprintf(
        '[%s] RSS Aggregator plugin uninstalled. All data removed.',
        current_time('mysql')
    );
    
    error_log($log_message);
}

// Only proceed if this is a complete uninstall
if (defined('WP_UNINSTALL_PLUGIN')) {
    // Log the uninstall
    rss_aggregator_log_uninstall();
    
    // Remove BuddyBoss activities
    rss_aggregator_remove_buddyboss_activities();
    
    // Remove GeoDirectory custom fields
    rss_aggregator_remove_geodirectory_fields();
    
    // Perform main cleanup
    rss_aggregator_uninstall_cleanup();
    
    // Final cleanup - remove any remaining plugin traces
    global $wpdb;
    
    // Remove any remaining options
    $wpdb->query("
        DELETE FROM {$wpdb->options} 
        WHERE option_name LIKE '%rss_aggregator%' 
        OR option_name LIKE '%rss-aggregator%'
    ");
    
    // Remove any remaining meta data
    $wpdb->query("
        DELETE FROM {$wpdb->postmeta} 
        WHERE meta_key LIKE '%rss_aggregator%' 
        OR meta_key LIKE '%rss-aggregator%'
    ");
    
    $wpdb->query("
        DELETE FROM {$wpdb->usermeta} 
        WHERE meta_key LIKE '%rss_aggregator%' 
        OR meta_key LIKE '%rss-aggregator%'
    ");
    
    // Clear object cache
    wp_cache_flush();
}
