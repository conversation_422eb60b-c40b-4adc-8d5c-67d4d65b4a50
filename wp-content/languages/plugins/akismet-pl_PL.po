# Translation of Plugins - Akismet Anti-spam: Spam Protection - Stable (latest release) in Polish
# This file is distributed under the same license as the Plugins - Akismet Anti-spam: Spam Protection - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-06-18 18:11:32+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n == 1) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: pl\n"
"Project-Id-Version: Plugins - Akismet Anti-spam: Spam Protection - Stable (latest release)\n"

#: views/compatible-plugins.php:111
msgid "Show less"
msgstr "Pokaż mniej"

#. translators: %d: number of compatible plugins, which is guaranteed to be
#. more than 1.
#: views/compatible-plugins.php:107 views/compatible-plugins.php:115
msgid "Show all %d plugins"
msgstr "Pokaż wszystkie %d wtyczek"

#: views/compatible-plugins.php:92
msgid "View documentation"
msgstr "Zobacz dokumentację"

#. translators: The placeholder is the name of a plugin, like "Jetpack" .
#: views/compatible-plugins.php:86
msgid "Documentation for %s"
msgstr "Dokumentacja dla %s"

#. translators: The placeholder is the name of a plugin, like "Jetpack" .
#: views/compatible-plugins.php:61
msgid "%s logo"
msgstr "Logo %s"

#: views/compatible-plugins.php:32
msgid "The plugin you've installed is compatible. Follow the documentation link to get started."
msgid_plural "The plugins you've installed are compatible. Follow the documentation links to get started."
msgstr[0] "Zainstalowana wtyczka jest zgodna. Aby rozpocząć, skorzystaj z odnośnika dokumentacji."
msgstr[1] "Zainstalowane przez Ciebie wtyczki są zgodne. Aby rozpocząć, skorzystaj z odnośników dokumentacji."
msgstr[2] "Zainstalowane przez Ciebie wtyczki są zgodne. Aby rozpocząć, skorzystaj z odnośników dokumentacji."

#: views/compatible-plugins.php:28
msgid "See supported integrations"
msgstr "Zobacz obsługiwane integracje"

#: views/compatible-plugins.php:21
msgid "Akismet works with other plugins to keep spam away."
msgstr "Akismet współpracuje z innymi wtyczkami, aby blokować spam."

#: views/compatible-plugins.php:13
msgid "New"
msgstr "Nowa"

#: views/compatible-plugins.php:12
msgid "Compatible plugins"
msgstr "Zgodne wtyczki"

#: views/compatible-plugins.php:11
msgid "Compatible plugins (new feature)"
msgstr "Zgodne wtyczki (nowa funkcja)"

#: class-akismet-compatible-plugins.php:86
msgid "Error getting compatible plugins."
msgstr "Błąd podczas pobierania zgodnych wtyczek."

#: views/notice.php:70
msgid "Upgrade plan"
msgstr "Ulepsz plan"

#. translators: The placeholder is a URL to the contact form.
#: views/notice.php:64
msgid "If you believe your site should not be classified as commercial, <a href=\"%s\">please get in touch</a>."
msgstr "Jeśli uważasz, że twoja strona nie powinna być sklasyfikowana jako komercyjna, proszę <a href=\"%s\">skontaktuj się z nami</a>."

#. translators: The placeholder is a URL.
#: views/notice.php:58
msgid "Your current subscription is for <a href=\"%s\">personal, non-commercial use</a>. Please upgrade your plan to continue using Akismet."
msgstr "Twoja obecna subskrypcja dotyczy <a href=\"%s\">użytku osobistego, niekomercyjnego</a>. Proszę zaktualizować swój plan, aby kontynuować korzystanie z Akismet."

#: views/notice.php:54
msgid "We detected commercial activity on your site"
msgstr "Wykryliśmy działalność komercyjną na twojej stronie."

#: views/notice.php:27
msgid "Almost done! Configure Akismet and say goodbye to spam"
msgstr "Prawie gotowe! Skonfiguruj Akismet i pożegnaj się ze spamem."

#: views/setup.php:7
msgid "Choose an Akismet plan"
msgstr "Wybierz plan Akismet"

#: class.akismet-admin.php:761
msgid "This comment was not sent to Akismet when it was submitted because it was caught by the comment disallowed list."
msgstr "Ten komentarz nie został wysłany do Akismet ponieważ zawiera słowa z listy niedozwolonych wyrazów."

#: class.akismet-admin.php:758
msgid "This comment was not sent to Akismet when it was submitted because it was caught by something else."
msgstr "Ten komentarz nie został wysłany do Akismet podczas jego przesyłania, ponieważ został złapany przez coś innego."

#. translators: the placeholder is the URL to the Akismet pricing page.
#: views/notice.php:180
msgid "Please <a href=\"%s\" target=\"_blank\">choose a plan</a> to get started with Akismet."
msgstr "Proszę <a href=\"%s\" target=\"_blank\">wybierz plan</a>, aby rozpocząć korzystanie z Akismet."

#: views/notice.php:176
msgid "Your API key must have an Akismet plan before it can protect your site from spam."
msgstr "Twój klucz API musi mieć przypisany plan Akismet, zanim będzie mógł chronić swoją stronę przed spamem."

#: class.akismet-rest-api.php:509
msgid "Multiple comments matched request."
msgstr "Do zapytania pasuje wiele komentarzy."

#: class.akismet-rest-api.php:499
msgid "Could not find matching comment."
msgstr "Nie można było odnaleźć pasującego komentarza."

#: class.akismet-rest-api.php:457
msgid "The 'comments' parameter must be an array."
msgstr "Parametr 'comments' musi być tablicą."

#: class.akismet-admin.php:755
msgid "Akismet cleared this comment during a recheck. It did not update the comment status because it had already been modified by another user or plugin."
msgstr "Akismet usunął ten komentarz podczas ponownego sprawdzania. Nie zaktualizował statusu komentarza, ponieważ został już zmodyfikowany przez innego użytkownika lub wtyczkę."

#: class.akismet-admin.php:752
msgid "Akismet determined this comment was spam during a recheck. It did not update the comment status because it had already been modified by another user or plugin."
msgstr "Akismet uznał ten komentarz za spam podczas ponownego sprawdzania. Nie zaktualizował statusu komentarza, ponieważ został już zmodyfikowany przez innego użytkownika lub wtyczkę."

#: class.akismet-admin.php:749
msgid "Akismet cleared this comment and updated its status via webhook."
msgstr "Akismet usunął ten komentarz i zaktualizował jego status za pomocą webhooka."

#: class.akismet-admin.php:746
msgid "Akismet caught this comment as spam and updated its status via webhook."
msgstr "Akismet oznaczył ten komentarz jako spam i zaktualizował jego status za pomocą webhooka."

#: views/notice.php:198
msgid "Akismet is now protecting your site from spam."
msgstr "Akismet teraz chroni twoją witrynę przed spamem."

#: views/config.php:304
msgid "Account overview"
msgstr "Przegląd konta"

#. translators: %1$s: spam folder link, %2$d: delete interval in days
#: views/config.php:192
msgid "Spam in the %1$s older than %2$d day is deleted automatically."
msgid_plural "Spam in the %1$s older than %2$d days is deleted automatically."
msgstr[0] "Spam w %1$s starszy niż %2$d dzień jest usuwany automatycznie."
msgstr[1] "Spam w %1$s starszy niż %2$d dni jest usuwany automatycznie."
msgstr[2] "Spam w %1$s starszy niż %2$d dni jest usuwany automatycznie."

#: views/config.php:187
msgid "spam folder"
msgstr "katalog spam"

#: views/stats.php:11
msgid "Akismet detailed stats"
msgstr "Szczegółowe statystyki Akismet"

#: views/stats.php:6
msgid "Back to settings"
msgstr "Wróć do ustawień"

#: views/config.php:268
msgid "Subscription type"
msgstr "Rodzaj subskrypcji"

#: views/config.php:232
msgid "To help your site with transparency under privacy laws like the GDPR, Akismet can display a notice to your users under your comment forms."
msgstr "Aby pomóc twojej stronie w zapewnieniu przejrzystości zgodnie z przepisami o prywatności, takimi jak RODO, Akismet może wyświetlać komunikat dla użytkowników poniżej formularzy komentarzy."

#: views/config.php:154
msgid "Spam filtering"
msgstr "Filtr spamu"

#: views/config.php:94 views/enter.php:9
msgid "API key"
msgstr "Klucz API"

#: views/config.php:44
msgid "Akismet stats"
msgstr "Statystyki Akismet"

#. Author of the plugin
#: akismet.php
msgid "Automattic - Anti-spam Team"
msgstr "Automattic - Anti-Spam-Team"

#. Plugin Name of the plugin
#: akismet.php
msgid "Akismet Anti-spam: Spam Protection"
msgstr "Akismet Anti-spam: Ochrona przed spamem"

#: views/notice.php:47
msgid "WP-Cron has been disabled using the DISABLE_WP_CRON constant. Comment rechecks may not work properly."
msgstr "WP-Cron został wyłączony za pomocą stałej DISABLE_WP_CRON. Ponowne sprawdzanie komentarzy może nie działać poprawnie."

#. translators: %1$s is a human-readable time difference, like "3 hours ago",
#. and %2$s is an already-translated phrase describing how a comment's status
#. changed, like "This comment was reported as spam."
#: class.akismet-admin.php:793
msgid "%1$s - %2$s"
msgstr "%1$s - %2$s"

#: views/get.php:17
msgid "(opens in a new tab)"
msgstr "(otworzy się w nowej karcie)"

#. translators: The placeholder is the name of a subscription level, like
#. "Plus" or "Enterprise" .
#: views/notice.php:341
msgid "Upgrade to %s"
msgstr "Aktualizuj do %s"

#: views/notice.php:336
msgid "Upgrade your subscription level"
msgstr "Ulepsz swój poziom subskrypcji"

#: views/notice.php:293 views/notice.php:301 views/notice.php:309
#: views/notice.php:318
msgid "Learn more about usage limits."
msgstr "Dowiedz się więcej o limitach używania."

#. translators: The first placeholder is a date, the second is a (formatted)
#. number, the third is another formatted number.
#: views/notice.php:285
msgid "Since %1$s, your account made %2$s API calls, compared to your plan&#8217;s limit of %3$s."
msgstr "Od %1$s twoje konto wykonało %2$s wywołań interfejsu API w porównaniu z limitem planu wynoszącym %3$s."

#: views/notice.php:315
msgid "Your Akismet usage has been over your plan&#8217;s limit for three consecutive months. We have restricted your account for the rest of the month. Upgrade your plan so Akismet can continue blocking spam."
msgstr "Wykorzystanie Akismet przekroczyło limit twojego planu przez trzy kolejne miesiące. Ograniczyliśmy twoje konto na resztę miesiąca. Uaktualnij swój plan, aby Akismet mógł nadal blokować spam."

#: views/notice.php:306
msgid "Your Akismet usage is nearing your plan&#8217;s limit for the third consecutive month. We will restrict your account after you reach the limit. Upgrade your plan so Akismet can continue blocking spam."
msgstr "Wykorzystanie Akismet zbliża się do limitu planu przez trzeci miesiąc z rzędu. Po osiągnięciu limitu ograniczymy twoje konto. Uaktualnij swój plan, aby Akismet mógł nadal blokować spam."

#: views/notice.php:298
msgid "Your Akismet usage has been over your plan&#8217;s limit for two consecutive months. Next month, we will restrict your account after you reach the limit. Please consider upgrading your plan."
msgstr "Wykorzystanie Akismet przekroczyło limit twojego planu przez dwa kolejne miesiące. W przyszłym miesiącu ograniczymy twoje konto po osiągnięciu limitu. Rozważ uaktualnienie swojego planu."

#: views/notice.php:272
msgid "Your account has been restricted"
msgstr "Twoje konto zostało ograniczone"

#: views/notice.php:268
msgid "Your Akismet account usage is approaching your plan&#8217;s limit"
msgstr "Wykorzystanie twojego konta Akismet zbliża się do limitu twojego planu"

#: views/notice.php:265
msgid "Your Akismet account usage is over your plan&#8217;s limit"
msgstr "Wykorzystanie twojego konta Akismet przekracza limit twojego planu"

#. translators: The placeholder is a URL to the Akismet contact form.
#: views/notice.php:228
msgid "Please enter a new key or <a href=\"%s\" target=\"_blank\">contact Akismet support</a>."
msgstr "Wprowadź nowy klucz lub <a href=\"%s\" target=\"_blank\">skontaktuj się z pomocą techniczną Akismet</a>."

#: views/notice.php:222
msgid "Your API key is no longer valid."
msgstr "Twój klucz API jest już nieważny."

#. translators: The placeholder is for showing how much of the process has
#. completed, as a percent. e.g., "Checking for Spam (40%)"
#: class.akismet-admin.php:481
msgid "Checking for Spam (%1$s%)"
msgstr "Sprawdzanie spamu (%1$s%)"

#: class.akismet-admin.php:809
msgid "No comment history."
msgstr "Brak historii komentarza."

#: class.akismet-admin.php:742
msgid "Akismet was unable to recheck this comment."
msgstr "Akismet nie mógł ponownie sprawdzić komentarza."

#: class.akismet-admin.php:734
msgid "Akismet was unable to check this comment but will automatically retry later."
msgstr "Akismet nie mógł ponownie sprawdzić komentarza, ale później zrobi to automatycznie."

#. translators: The placeholder is a WordPress PHP function name.
#: class.akismet-admin.php:703
msgid "Comment was caught by %s."
msgstr "Komentarz został przechwycony przez %s."

#: class.akismet.php:802
msgid "Akismet is not configured. Please enter an API key."
msgstr "Akismet nie jest skonfigurowany. Proszę dodać klucz API."

#: views/enter.php:7
msgid "Enter your API key"
msgstr "Wprowadź klucz API"

#: views/connect-jp.php:92
msgid "Set up a different account"
msgstr "Skonfiguruj inne konto"

#: views/setup.php:2
msgid "Set up your Akismet account to enable spam filtering on this site."
msgstr "Skonfiguruj konto Akismet, żeby włączyć ochronę przed spamem na tej witrynie."

#: class.akismet-admin.php:1332
msgid "Akismet could not recheck your comments for spam."
msgstr "Akismet nie był w stanie przeanalizować komentarzy pod kątem spamu."

#: class.akismet-admin.php:514
msgid "You don&#8217;t have permission to do that."
msgstr "Nie posiadasz uprawnień do wykonania tej operacji."

#: class.akismet-cli.php:167
msgid "Stats response could not be decoded."
msgstr "Nie można oczytać odpowiedzi Stats."

#: class.akismet-cli.php:161
msgid "Currently unable to fetch stats. Please try again."
msgstr "Obecnie nie można pobrać statystyk. Spróbuj ponownie później."

#: class.akismet-cli.php:135
msgid "API key must be set to fetch stats."
msgstr "Klucz API musi być ustawiony, aby pobrać statystyki."

#: views/config.php:225
msgid "Do not display privacy notice."
msgstr "Nie wyświetlaj informacji o prywatności."

#: views/config.php:217
msgid "Display a privacy notice under your comment forms."
msgstr "Wyświetl pod formularzami komentarzy informacje o prywatności."

#: views/config.php:211
msgid "Akismet privacy notice"
msgstr "Polityka prywatności Akismeta"

#: views/config.php:206
msgid "Privacy"
msgstr "Prywatność"

#. translators: %s: Akismet privacy URL
#: class.akismet.php:1917
msgid "This site uses Akismet to reduce spam. <a href=\"%s\" target=\"_blank\" rel=\"nofollow noopener\">Learn how your comment data is processed.</a>"
msgstr "Ta strona używa Akismet do redukcji spamu. <a href=\"%s\" target=\"_blank\" rel=\"nofollow noopener\">Dowiedz się, w jaki sposób przetwarzane są dane Twoich komentarzy.</a>"

#: class.akismet-admin.php:108
msgid "We collect information about visitors who comment on Sites that use our Akismet Anti-spam service. The information we collect depends on how the User sets up Akismet for the Site, but typically includes the commenter's IP address, user agent, referrer, and Site URL (along with other information directly provided by the commenter such as their name, username, email address, and the comment itself)."
msgstr "Gromadzimy informacje o odwiedzających, którzy komentują witryny korzystające z naszej usługi antyspamowej Akismet. Informacje, które zbieramy, zależą od tego, jak użytkownik konfiguruje Akismet dla witryny, ale zazwyczaj obejmują adres IP komentującego, agenta użytkownika, odsyłacza i adres URL witryny (wraz z innymi informacjami bezpośrednio podanymi przez komentującego, takimi jak jego imię, nazwa użytkownika, adres e-mail i sam komentarz)."

#: class.akismet.php:430
msgid "Comment discarded."
msgstr "Komentarz został odrzucony."

#: class.akismet-rest-api.php:206
msgid "This site's API key is hardcoded and cannot be deleted."
msgstr "Klucz API witryny zostały wprowadzony na twardo i nie da się go skasować."

#: class.akismet-rest-api.php:190
msgid "The value provided is not a valid and registered API key."
msgstr "Wprowadzona wartość nie jest prawidłowym i zarejestrowanym kluczem API."

#: class.akismet-rest-api.php:184
msgid "This site's API key is hardcoded and cannot be changed via the API."
msgstr "Klucz API witryny zostały wprowadzony na twardo i nie da się go zmienić przez API."

#: class.akismet-rest-api.php:84 class.akismet-rest-api.php:97
msgid "The time period for which to retrieve stats. Options: 60-days, 6-months, all"
msgstr "Okres, w którym należy pobrać statystyki. Opcje: 60 dni, 6 miesięcy, całość"

#: class.akismet-rest-api.php:65
msgid "If true, show the number of approved comments beside each comment author in the comments list page."
msgstr "Pokazuj liczbę zatwierdzonych komentarzy obok każdego autora na stronie z listą komentarzy."

#: class.akismet-rest-api.php:60
msgid "If true, Akismet will automatically discard the worst spam automatically rather than putting it in the spam folder."
msgstr "Włączone pozwoli na automatyczne odrzucanie najgorszy spam, zamiast przenoszenia do katalogu ze spamem."

#: class.akismet-rest-api.php:31 class.akismet-rest-api.php:122
#: class.akismet-rest-api.php:135 class.akismet-rest-api.php:148
msgid "A 12-character Akismet API key. Available at akismet.com/get/"
msgstr "12-znakowy klucz API Akismeta. Dostępny na stronie akismet.com/get/"

#: views/notice.php:109
msgid "Your site can&#8217;t connect to the Akismet servers."
msgstr "Twoja witryna nie może połączyć się z serwerami Akismeta."

#. translators: %s is the wp-config.php file
#: views/predefined.php:7
msgid "An Akismet API key has been defined in the %s file for this site."
msgstr "Klucz API Akismet dla tej witryny został zdefiniowany w pliku %s."

#: views/predefined.php:2
msgid "Manual Configuration"
msgstr "Konfiguracja ręczna"

#: class.akismet-admin.php:275
msgid "On this page, you are able to update your Akismet settings and view spam stats."
msgstr "Na stronie możesz aktualizować ustawienia Akismeta i przeglądać statystyki spamu."

#. Description of the plugin
#: akismet.php
msgid "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. Akismet Anti-spam keeps your site protected even while you sleep. To get started: activate the Akismet plugin and then go to your Akismet Settings page to set up your API key."
msgstr "Używany przez miliony, Akismet jest prawdopodobnie najlepszym sposobem na świecie, aby <strong>chronić swojego bloga przed spamem</strong>. Akismet Anti-spam chroni Twoją witrynę nawet podczas snu. Aby zacząć: włącz wtyczkę Akismet, a następnie przejdź do strony ustawień Akismet, aby skonfigurować klucz API."

#: class.akismet-admin.php:135 class.akismet-admin.php:137
msgid "Akismet Anti-spam"
msgstr "Akismet Anti-spam"

#: views/enter.php:10
msgid "Connect with API key"
msgstr "Połącz używając klucza API"

#. translators: %s is the WordPress.com username
#: views/connect-jp.php:25 views/connect-jp.php:79
msgid "You are connected as %s."
msgstr "Połączono jako: %s."

#: views/connect-jp.php:10 views/connect-jp.php:18 views/connect-jp.php:38
#: views/connect-jp.php:72 views/connect-jp.php:91
msgid "Connect with Jetpack"
msgstr "Połącz z Jetpackiem"

#: views/connect-jp.php:12 views/connect-jp.php:32 views/connect-jp.php:67
msgid "Use your Jetpack connection to set up Akismet."
msgstr "Użyj połączenia z Jetpackiem aby ustawić Akismet."

#: views/title.php:2
msgid "Eliminate spam from your site"
msgstr "Wyeliminuj spam ze swojej witryny"

#. translators: The placeholder is a URL for checking pending comments.
#: views/notice.php:205
msgid "Would you like to <a href=\"%s\">check pending comments</a>?"
msgstr "Czy chcesz <a href=\"%s\">sprawdzić oczekujące komentarze</a>?"

#: views/notice.php:25
msgid "Set up your Akismet account"
msgstr "Skonfiguruj swoje konto Akismeta"

#: views/config.php:36
msgid "Detailed stats"
msgstr "Szczegółowe statystyki"

#: views/config.php:31
msgid "Statistics"
msgstr "Statystyka"

#: class.akismet-admin.php:1448
msgid "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. It keeps your site protected even while you sleep. To get started, just go to <a href=\"admin.php?page=akismet-key-config\">your Akismet Settings page</a> to set up your API key."
msgstr "Używany przez miliony, Akismet jest być może najlepszym rozwiązaniem na świecie stworzonym w celu <strong>ochrony bloga przed spamem</strong>. Chroni witrynę nawet wtedy kiedy śpisz. Aby rozpocząć, przejdź do <a href=\"admin.php?page=akismet-key-config\">strony konfiguracji Akismet</a>, aby ustawić klucz API."

#: class.akismet-admin.php:1446
msgid "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. Your site is fully configured and being protected, even while you sleep."
msgstr "Używany przez miliony, Akismet jest być może najlepszym rozwiązaniem na świecie w celu <strong>ochrony bloga przed spamem</strong>. Witryna jest w pełni skonfigurowana i jest chroniona, nawet wtedy kiedy śpisz."

#. translators: %s: Number of comments.
#: class.akismet-admin.php:1326
msgid "%s comment was caught as spam."
msgid_plural "%s comments were caught as spam."
msgstr[0] "%s komentarz został oznaczony jako spam."
msgstr[1] "%s komentarze zostały oznaczone jako spam."
msgstr[2] "%s komentarzy zostało oznaczonych jako spam."

#: class.akismet-admin.php:1323
msgid "No comments were caught as spam."
msgstr "Nie znaleziono komentarzy do oznaczenia jako spam."

#. translators: %s: Number of comments.
#: class.akismet-admin.php:1319
msgid "Akismet checked %s comment."
msgid_plural "Akismet checked %s comments."
msgstr[0] "Akismet sprawdził %s komentarz."
msgstr[1] "Akismet sprawdził %s komentarze."
msgstr[2] "Akismet sprawdził %s komentarzy."

#: class.akismet-admin.php:1316
msgid "There were no comments to check. Akismet will only check comments awaiting moderation."
msgstr "Brak komentarzy do sprawdzenia. Akismet sprawdza jedynie komentarze w kolejce oczekujących."

#: class.akismet.php:808
msgid "Comment not found."
msgstr "Nie znaleziono komentarza."

#. translators: %d: Number of comments.
#: class.akismet-cli.php:89
msgid "%d comment could not be checked."
msgid_plural "%d comments could not be checked."
msgstr[0] "%d komentarz nie mógł zostać sprawdzony."
msgstr[1] "%d komentarze nie mogły zostać sprawdzone."
msgstr[2] "%d komentarzy nie mogło zostać sprawdzonych."

#. translators: %d: Number of comments.
#: class.akismet-cli.php:85
msgid "%d comment moved to Spam."
msgid_plural "%d comments moved to Spam."
msgstr[0] "%d komentarz został przeniesiony do spamu."
msgstr[1] "%d komentarze zostały przeniesione do spamu."
msgstr[2] "%d komentarzy zostało przeniesionych do spamu."

#. translators: %d: Number of comments.
#: class.akismet-cli.php:82
msgid "Processed %d comment."
msgid_plural "Processed %d comments."
msgstr[0] "Przetworzono %d komentarz."
msgstr[1] "Przetworzono %d komentarze."
msgstr[2] "Przetworzono %d komentarzy."

#. translators: %d: Comment ID.
#: class.akismet-cli.php:45
msgid "Comment #%d could not be checked."
msgstr "Komentarz #%d nie mógł zostać sprawdzony."

#. translators: %d: Comment ID.
#: class.akismet-cli.php:42
msgid "Failed to connect to Akismet."
msgstr "Nie udało się połączyć z Akismetem."

#. translators: %d: Comment ID.
#: class.akismet-cli.php:39
msgid "Comment #%d is not spam."
msgstr "Komentarz #%d nie jest spamem."

#. translators: %d: Comment ID.
#: class.akismet-cli.php:36
msgid "Comment #%d is spam."
msgstr "Komentarz #%d to spam."

#. translators: %s: number of false positive spam flagged by Akismet
#: views/config.php:66
msgid "%s false positive"
msgid_plural "%s false positives"
msgstr[0] "%s niesłusznie zakwalifikowany spam"
msgstr[1] "%s niesłusznie zakwalifikowane spamy"
msgstr[2] "%s niesłusznie zakwalifikowanych spamów"

#. translators: %s: number of spam missed by Akismet
#: views/config.php:64
msgid "%s missed spam"
msgid_plural "%s missed spam"
msgstr[0] "%s przeoczony spam"
msgstr[1] "%s przeoczone spamy"
msgstr[2] "%s przeoczonych spamów"

#: views/notice.php:175
msgid "You don&#8217;t have an Akismet plan."
msgstr "Nie masz włączonego pakietu Akismeta."

#: views/notice.php:142
msgid "Your Akismet subscription is suspended."
msgstr "Twój abonament Akismet jest zawieszony."

#: views/notice.php:131
msgid "Your Akismet plan has been cancelled."
msgstr "Twój pakiet Akismeta został anulowany."

#. translators: The placeholder is a URL.
#: views/notice.php:124
msgid "We cannot process your payment. Please <a href=\"%s\" target=\"_blank\">update your payment details</a>."
msgstr "Nie możemy przetworzyć twojej płatności. Prosimy o <a href=\"%s\" target=\"_blank\">zaktualizowanie danych płatności</a>."

#: views/notice.php:120
msgid "Please update your payment information."
msgstr "Zaktualizuj swoje dane płatności."

#. translators: %s: Number of minutes.
#: class.akismet-admin.php:1226
msgid "Akismet has saved you %d minute!"
msgid_plural "Akismet has saved you %d minutes!"
msgstr[0] "Akismet oszczędził ci %d minutę!"
msgstr[1] "Akismet oszczędził ci %d minuty!"
msgstr[2] "Akismet oszczędził ci %d minut!"

#. translators: %s: Number of hours.
#: class.akismet-admin.php:1223
msgid "Akismet has saved you %d hour!"
msgid_plural "Akismet has saved you %d hours!"
msgstr[0] "Akismet oszczędził ci %d godzinę!"
msgstr[1] "Akismet oszczędził ci %d godziny!"
msgstr[2] "Akismet oszczędził ci %d godzin!"

#. translators: %s: Number of days.
#: class.akismet-admin.php:1220
msgid "Akismet has saved you %s day!"
msgid_plural "Akismet has saved you %s days!"
msgstr[0] "Akismet oszczędził ci %s dzień!"
msgstr[1] "Akismet oszczędził ci %s dni!"
msgstr[2] "Akismet oszczędził ci %s dni!"

#: class.akismet-admin.php:224 class.akismet-admin.php:262
#: class.akismet-admin.php:274
msgid "Akismet filters out spam, so you can focus on more important things."
msgstr "Akismet odfiltrowuje spam, dzięki czemu możesz skupić się na ważniejszych rzeczach."

#. translators: The placeholder is a URL.
#: views/notice.php:245
msgid "The connection to akismet.com could not be established. Please refer to <a href=\"%s\" target=\"_blank\">our guide about firewalls</a> and check your server configuration."
msgstr "Brak możliwości połączenia się do akismet.com. Proszę zapoznać się z naszym <a href=\"%s\" target=\"_blank\">poradnikiem o firewallach</a> i sprawdzić konfigurację serwera.."

#: views/notice.php:239
msgid "The API key you entered could not be verified."
msgstr "Nie można zweryfikować wprowadzonego klucza."

#: views/config.php:121
msgid "All systems functional."
msgstr "Wszystko działa."

#: views/config.php:120
msgid "Enabled."
msgstr "Dostępne."

#: views/config.php:118
msgid "Akismet encountered a problem with a previous SSL request and disabled it temporarily. It will begin using SSL for requests again shortly."
msgstr "Akismet tymczasowo wyłączył połączenia SSL z powodu problemów z poprzednim połączeniem tego typu. Wkrótce znowu spróbuje połączeń SSL."

#: views/config.php:117
msgid "Temporarily disabled."
msgstr "Tymczasowo niedostępne."

#: views/config.php:112
msgid "Your Web server cannot make SSL requests; contact your Web host and ask them to add support for SSL requests."
msgstr "Twój serwer nie potrafi skorzystać z SSL'a. Proszę skontaktować się z administratorem serwera i dodać obsługę zapytań SSL."

#: views/config.php:111
msgid "Disabled."
msgstr "Niedostępne."

#: views/config.php:108
msgid "SSL status"
msgstr "Status SSL"

#: class.akismet-admin.php:720
msgid "This comment was reported as not spam."
msgstr "Ten komentarz został zgłoszony jako nie spam."

#: class.akismet-admin.php:712
msgid "This comment was reported as spam."
msgstr "Ten komentarz został zgłoszony jako spam."

#. Author URI of the plugin
#: akismet.php
msgid "https://automattic.com/wordpress-plugins/"
msgstr "https://automattic.com/wordpress-plugins/"

#. Plugin URI of the plugin
#: akismet.php
msgid "https://akismet.com/"
msgstr "https://akismet.com/"

#: views/enter.php:2
msgid "Manually enter an API key"
msgstr "Wprowadź klucz do API ręcznie"

#: views/connect-jp.php:53 views/notice.php:333
msgid "Contact Akismet support"
msgstr "Skontaktuj się z obsługą Akismeta"

#: views/connect-jp.php:64
msgid "No worries! Get in touch and we&#8217;ll sort this out."
msgstr "Nie martw się! Skontaktuj się z nami, a pomożemy ci to rozwiązać."

#. translators: %s is the WordPress.com email address
#: views/connect-jp.php:60
msgid "Your subscription for %s is suspended."
msgstr "Twój abonament %s jest zawieszony."

#. translators: %s is the WordPress.com email address
#: views/connect-jp.php:45
msgid "Your subscription for %s is cancelled."
msgstr "Abonament %s został anulowany."

#: views/notice.php:217
msgid "The key you entered is invalid. Please double-check it."
msgstr "Wprowadzony klucz jest nieprawidłowy. Proszę sprawdzić go dwukrotnie."

#: views/notice.php:164
msgid "There is a problem with your API key."
msgstr "Wystąpił problem z twoim kluczem API."

#. translators: the placeholder is a clickable URL to the Akismet account
#. upgrade page.
#: views/notice.php:157
msgid "You can help us fight spam and upgrade your account by <a href=\"%s\" target=\"_blank\">contributing a token amount</a>."
msgstr "Możesz nam pomóc walczyć ze spamem i ulepszyć swoje konto, <a href=\"%s\" target=\"_blank\">uiszczając symboliczną opłatę</a>."

#. translators: The placeholder is a URL.
#. translators: The placeholder is a URL to the Akismet contact form.
#: views/notice.php:146 views/notice.php:168
msgid "Please contact <a href=\"%s\" target=\"_blank\">Akismet support</a> for assistance."
msgstr "Proszę skontaktować się z <a href=\"%s\" target=\"_blank\">obsługą Akismeta</a>, aby uzyskać pomoc."

#. translators: The placeholder is a URL.
#: views/notice.php:135
msgid "Please visit your <a href=\"%s\" target=\"_blank\">Akismet account page</a> to reactivate your subscription."
msgstr "Proszę przejść na <a href=\"%s\" target=\"_blank\">stronę konta w Akismecie</a>, aby ponownie włączyć swój abonament."

#. translators: The placeholder is a URL.
#: views/notice.php:113
msgid "Your firewall may be blocking Akismet from connecting to its API. Please contact your host and refer to <a href=\"%s\" target=\"_blank\">our guide about firewalls</a>."
msgstr "Błąd połączenia Akismem do API może być spowodowany blokadą ruchu przez zaporę sieciową. Proszę skontaktować się z administratorem serwera i wysłać mu <a href=\"%s\" target=\"_blank\">nasz poradnik dotyczący zapór sieciowych</a>."

#. translators: The placeholder is a URL.
#: views/notice.php:102
msgid "Your web host or server administrator has disabled PHP&#8217;s <code>gethostbynamel</code> function.  <strong>Akismet cannot work correctly until this is fixed.</strong>  Please contact your web host or firewall administrator and give them <a href=\"%s\" target=\"_blank\">this information about Akismet&#8217;s system requirements</a>."
msgstr "Administrator serwera zablokował funkcje PHP&#8217;s <code>gethostbynamel</code>.  <strong>Akismet nie może pracować bez tej funkcji.</strong>  Proszę skontaktować się z administratorem i przekazać <a href=\"%s\" target=\"_blank\">informację na temat minimalnych wymagań Akismeta</a>."

#: views/notice.php:98
msgid "Network functions are disabled."
msgstr "Funkcja sieci jest wyłączona."

#. translators: the placeholder is a clickable URL that leads to more
#. information regarding an error code.
#: views/notice.php:83
msgid "For more information: %s"
msgstr "Więcej informacji: %s"

#. translators: The placeholder is an error code returned by Akismet.
#: views/notice.php:78
msgid "Akismet error code: %s"
msgstr "Kod błędu Aksimet: %s"

#: views/notice.php:37
msgid "Some comments have not yet been checked for spam by Akismet. They have been temporarily held for moderation and will automatically be rechecked later."
msgstr "Niektóre z komentarzy nie zostały jeszcze sprawdzone przez Akismet. Zostaną jeszcze tymczasowo zatrzymane do moderacji i będą automatycznie sprawdzone za jakiś czas."

#: views/notice.php:36 views/notice.php:46
msgid "Akismet has detected a problem."
msgstr "Akismet wykrył problem."

#: views/config.php:312
msgid "Change"
msgstr "Zmień"

#: views/config.php:312
msgid "Upgrade"
msgstr "Ulepszenie"

#: views/config.php:293
msgid "Next billing date"
msgstr "Następna data rozliczenia"

#: views/config.php:286
msgid "Active"
msgstr "Włączono"

#: views/config.php:284
msgid "No subscription found"
msgstr "Nie znaleziono abonamentu"

#: views/config.php:282
msgid "Missing"
msgstr "Brak"

#: views/config.php:280
msgid "Suspended"
msgstr "Zawieszone"

#: views/config.php:278
msgid "Cancelled"
msgstr "Anulowano"

#: views/config.php:249
msgid "Save changes"
msgstr "Zapisz zmiany"

#: views/config.php:241
msgid "Disconnect this account"
msgstr "Odłącz to konto"

#: views/config.php:180
msgid "Note:"
msgstr "Adnotacja:"

#: views/config.php:173
msgid "Always put spam in the Spam folder for review."
msgstr "Zawsze umieszczaj wiadomości podejrzane o bycie spamem do przejrzenia w folderze &bdquo;Spam&rdquo;."

#: views/config.php:165
msgid "Silently discard the worst and most pervasive spam so I never see it."
msgstr "Automatycznie odrzucaj najbardziej podejrzane wiadomości, aby nie były nigdy wyświetlane."

#: views/config.php:159
msgid "Akismet Anti-spam strictness"
msgstr "Rygorystyczność Akismet Anti-spam"

#: views/config.php:146
msgid "Show the number of approved comments beside each comment author."
msgstr "Pokaż liczbę zatwierdzonych komentarzy obok każdego autora komentarza."

#: views/config.php:59
msgid "Accuracy"
msgstr "Trafność"

#: views/config.php:54
msgid "All time"
msgstr "Cały czas"

#: views/config.php:51 views/config.php:56
msgid "Spam blocked"
msgid_plural "Spam blocked"
msgstr[0] "Zablokowany spam"
msgstr[1] ""
msgstr[2] ""

#: views/config.php:49
msgid "Past six months"
msgstr "W ciągu ostatnich sześciu miesięcy"

#. translators: 1: WordPress documentation URL, 2: Akismet download URL.
#: class.akismet.php:1732
msgid "Please <a href=\"%1$s\">upgrade WordPress</a> to a current version, or <a href=\"%2$s\">downgrade to version 2.4 of the Akismet plugin</a>."
msgstr "<a href=\"%1$s\">Zaktualizuj WordPressa</a> do najnowszej wersji lub <a href=\"%2$s\">przywróć wersję 2.4 wtyczki Akismet</a>."

#. translators: 1: Current Akismet version number, 2: Minimum WordPress version
#. number required.
#: class.akismet.php:1730
msgid "Akismet %1$s requires WordPress %2$s or higher."
msgstr "Akismet %1$s wymaga WordPress %2$s lub nowszego."

#: class.akismet-admin.php:727
msgid "Akismet cleared this comment during an automatic retry."
msgstr "Akismet przepuścił ten komentarz podczas automatycznego ponownego sprawdzenia."

#: class.akismet-admin.php:724
msgid "Akismet caught this comment as spam during an automatic retry."
msgstr "Akismet wychwycił ten komentarz jako będący spamem podczas automatycznej ponownej próby sprawdzenia."

#. translators: The placeholder is a username.
#: class.akismet-admin.php:718
msgid "%s reported this comment as not spam."
msgstr "%s zgłosił ten komentarz jako nie spam."

#. translators: The placeholder is a username.
#: class.akismet-admin.php:710
msgid "%s reported this comment as spam."
msgstr "Użytkownik %s zgłosił ten komentarz jako będący spamem."

#. translators: %1$s is a username; %2$s is a short string (like 'spam' or
#. 'approved') denoting the new comment status.
#: class.akismet-admin.php:775
msgid "%1$s changed the comment status to %2$s."
msgstr "Użytkownik %1$s zmienił status komentarza na %2$s."

#. translators: The placeholder is an error response returned by the API
#. server.
#: class.akismet-admin.php:732
msgid "Akismet was unable to check this comment (response: %s) but will automatically retry later."
msgstr "Akismet nie mógł sprawdzić tego komentarza (odpowiedź: %s), ale automatycznie spróbuje ponownie później."

#: class.akismet-admin.php:697
msgid "Akismet cleared this comment."
msgstr "Akismet zatwierdził ten komentarz."

#. translators: The placeholder is a short string (like 'spam' or 'approved')
#. denoting the new comment status.
#: class.akismet-admin.php:769
msgid "Comment status was changed to %s"
msgstr "Stan komentarza został zmieniony na &#8222;%s&#8221;"

#: class.akismet-admin.php:691
msgid "Akismet caught this comment as spam."
msgstr "Akismet oznaczył ten komentarz jako spam."

#. translators: The placeholder is the number of pieces of spam blocked by
#. Akismet.
#: class.akismet-widget.php:135
msgid "<strong class=\"count\">%1$s spam</strong> blocked by <strong>Akismet</strong>"
msgid_plural "<strong class=\"count\">%1$s spam</strong> blocked by <strong>Akismet</strong>"
msgstr[0] "<strong class=\"count\">%1$s komentarz będący spamem</strong> został zablokowany przez <strong>Akismeta</strong>"
msgstr[1] "<strong class=\"count\">%1$s komentarze będące spamem</strong> zostały zablokowane przez <strong>Akismeta</strong>"
msgstr[2] "<strong class=\"count\">%1$s komentarzy będących spamem</strong> zostało zablokowanych przez <strong>Akismeta</strong>"

#: class.akismet-widget.php:99
msgid "Title:"
msgstr "Tytuł:"

#: class.akismet-widget.php:94 class.akismet-widget.php:116
msgid "Spam Blocked"
msgstr "Zablokowany spam"

#: class.akismet-widget.php:17
msgid "Display the number of spam comments Akismet has caught"
msgstr "Wyświetl liczbę komentarzy, które Akismet uznał za spam"

#: class.akismet-widget.php:16
msgid "Akismet Widget"
msgstr "Widget Akismeta"

#: class.akismet-admin.php:1216
msgid "Cleaning up spam takes time."
msgstr "Usuwanie spamu może trochę potrwać."

#. translators: The Akismet configuration page URL.
#: class.akismet-admin.php:1088
msgid "Please check your <a href=\"%s\">Akismet configuration</a> and contact your web host if problems persist."
msgstr "Sprawdź <a href=\"%s\">konfigurację Akismeta</a> i jeżeli problem nadal występuje, to skontaktuj się z administratorem serwera."

#. translators: The placeholder is an amount of time, like "7 seconds" or "3
#. days" returned by the function human_time_diff().
#: class.akismet-admin.php:789
msgid "%s ago"
msgstr "%s temu"

#. translators: %s: Number of comments.
#: class.akismet-admin.php:664
msgid "%s approved"
msgid_plural "%s approved"
msgstr[0] "%s zatwierdzono"
msgstr[1] "%s zatwierdzone"
msgstr[2] "%s zatwierdzonych"

#: class.akismet-admin.php:638
msgid "History"
msgstr "Historia"

#: class.akismet-admin.php:638 class.akismet-admin.php:646
msgid "View comment history"
msgstr "Pokaż oś czasu komentarzy"

#. translators: %s: Username.
#: class.akismet-admin.php:625
msgid "Un-spammed by %s"
msgstr "Cofnięte ze spamu przez %s"

#. translators: %s: Username.
#: class.akismet-admin.php:622
msgid "Flagged as spam by %s"
msgstr "Oznaczone jako spam przez %s"

#: class.akismet-admin.php:616
msgid "Cleared by Akismet"
msgstr "Przepuszczony przez Akismeta"

#: class.akismet-admin.php:614
msgid "Flagged as spam by Akismet"
msgstr "Oznaczone jako spam przez Akismet"

#: class.akismet-admin.php:610
msgid "Awaiting spam check"
msgstr "Oczekujące na sprawdzenie"

#. translators: The placeholder is an error response returned by the API
#. server.
#: class.akismet-admin.php:740
msgid "Akismet was unable to recheck this comment (response: %s)."
msgstr "Akismet nie mógł sprawdzić ponownie tego komentarza (odpowiedź: %s)."

#: class.akismet-admin.php:694
msgid "Akismet re-checked and cleared this comment."
msgstr "Akismet ponownie przeanalizował i zatwierdził ten komentarz."

#: class.akismet-admin.php:688
msgid "Akismet re-checked and caught this comment as spam."
msgstr "Akismet sprawdził ponownie i oznaczył ten komentarz jako spam."

#: class.akismet-admin.php:498
msgid "Check for Spam"
msgstr "Szukaj spamu"

#. translators: %s: Comments page URL.
#: class.akismet-admin.php:440
msgid "There&#8217;s nothing in your <a href='%s'>spam queue</a> at the moment."
msgstr "W <a href='%s'>kolejce</a> nie znajdują się obecnie żadne komentarze."

#. translators: 1: Number of comments, 2: Comments page URL.
#: class.akismet-admin.php:429
msgid "There&#8217;s <a href=\"%2$s\">%1$s comment</a> in your spam queue right now."
msgid_plural "There are <a href=\"%2$s\">%1$s comments</a> in your spam queue right now."
msgstr[0] "Obecnie <a href=\"%2$s\">%1$s komentarz</a> oczekuje w kolejce spamu."
msgstr[1] "Obecnie <a href=\"%2$s\">%1$s komentarze</a> oczekują w kolejce spamu."
msgstr[2] "Obecnie <a href=\"%2$s\">%1$s komentarzy</a> oczekuje w kolejce spamu."

#. translators: %s: Akismet website URL.
#: class.akismet-admin.php:421
msgid "<a href=\"%s\">Akismet</a> blocks spam from getting to your blog. "
msgstr "<a href=\"%s\">Akismet</a> blokuje spam przed pojawieniem się w serwisie. "

#. translators: 1: Akismet website URL, 2: Number of spam comments.
#: class.akismet-admin.php:410
msgid "<a href=\"%1$s\">Akismet</a> has protected your site from %2$s spam comment already. "
msgid_plural "<a href=\"%1$s\">Akismet</a> has protected your site from %2$s spam comments already. "
msgstr[0] "Dotychczas <a href=\"%1$s\">Akismet</a> ochronił Twoją witrynę przed %2$s komentarzem będącym spamem."
msgstr[1] "Dotychczas <a href=\"%1$s\">Akismet</a> ochronił Twoją witrynę przed %2$s komentarzami będącymi spamem."
msgstr[2] "Dotychczas <a href=\"%1$s\">Akismet</a> ochronił Twoją witrynę przed %2$s komentarzami będącymi spamem."

#. translators: 1: Akismet website URL, 2: Comments page URL, 3: Number of spam
#. comments.
#: class.akismet-admin.php:393
msgid "<a href=\"%1$s\">Akismet</a> has protected your site from <a href=\"%2$s\">%3$s spam comment</a>."
msgid_plural "<a href=\"%1$s\">Akismet</a> has protected your site from <a href=\"%2$s\">%3$s spam comments</a>."
msgstr[0] "Dotychczas <a href=\"%1$s\">Akismet</a> ochronił witrynę przed <a href=\"%2$s\">%3$s komentarzem będącym spamem</a>."
msgstr[1] "Dotychczas <a href=\"%1$s\">Akismet</a> ochronił witrynę przed <a href=\"%2$s\">%3$s komentarzami będącymi spamem</a>."
msgstr[2] "Dotychczas <a href=\"%1$s\">Akismet</a> ochronił witrynę przed <a href=\"%2$s\">%3$s komentarzami będącymi spamem</a>."

#: class.akismet-admin.php:389
msgctxt "comments"
msgid "Spam"
msgstr "Spam"

#: class.akismet-admin.php:316
msgid "Cheatin&#8217; uh?"
msgstr "Oszukujemy, co?"

#: class.akismet-admin.php:310
msgid "Akismet Support"
msgstr "Obsługa Akismeta"

#: class.akismet-admin.php:309
msgid "Akismet FAQ"
msgstr "FAQ Akismeta"

#: class.akismet-admin.php:308
msgid "For more information:"
msgstr "Więcej informacji:"

#: class.akismet-admin.php:299
msgid "The subscription status - active, cancelled or suspended"
msgstr "Stan abonamentu - aktywny, anulowany lub zawieszony"

#: class.akismet-admin.php:299 views/config.php:274
msgid "Status"
msgstr "Status"

#: class.akismet-admin.php:298
msgid "The Akismet subscription plan"
msgstr "Abonament Akismetu"

#: class.akismet-admin.php:298
msgid "Subscription Type"
msgstr "Rodzaj abonamentu"

#: class.akismet-admin.php:295 views/config.php:260
msgid "Account"
msgstr "Konto"

#: class.akismet-admin.php:287
msgid "Choose to either discard the worst spam automatically or to always put all spam in spam folder."
msgstr "Wybierz czy najbardziej podejrzane wiadomości mają być automatycznie odrzucane, czy umieszczane, jak inne wiadomości podejrzane o bycie spamem, w folderze ze spamem."

#: class.akismet-admin.php:287
msgid "Strictness"
msgstr "Surowość"

#: class.akismet-admin.php:286
msgid "Show the number of approved comments beside each comment author in the comments list page."
msgstr "Pokazuj liczbę zatwierdzonych komentarzy obok każdego autora na stronie z listą komentarzy."

#: class.akismet-admin.php:286 views/config.php:131
msgid "Comments"
msgstr "Komentarze"

#: class.akismet-admin.php:285
msgid "Enter/remove an API key."
msgstr "Wprowadź/usuń klucz do API."

#: class.akismet-admin.php:285
msgid "API Key"
msgstr "Klucz API"

#: class.akismet-admin.php:273 class.akismet-admin.php:284
#: class.akismet-admin.php:297
msgid "Akismet Configuration"
msgstr "Konfiguracja Akismeta"

#: class.akismet-admin.php:263
msgid "On this page, you are able to view stats on spam filtered on your site."
msgstr "Na tej stronie wyświetlane są statystyki dot. spamu odfiltrowanego na twojej witrynie."

#: class.akismet-admin.php:261
msgid "Akismet Stats"
msgstr "Statystyki Akismeta"

#: class.akismet-admin.php:250
msgid "Click the Use this Key button."
msgstr "Kliknij przycisk &bdquo;Użyj tego klucza&rdquo;."

#: class.akismet-admin.php:249
msgid "Copy and paste the API key into the text field."
msgstr "Skopiuj go i wklej do poniższego pola."

#: class.akismet-admin.php:247
msgid "If you already have an API key"
msgstr "Jeśli posiadasz już klucz do API"

#: class.akismet-admin.php:244
msgid "Enter an API Key"
msgstr "Wprowadź klucz do API"

#. translators: %s: a link to the signup page with the text 'Akismet.com'.
#: class.akismet-admin.php:237
msgid "Sign up for an account on %s to get an API Key."
msgstr "Zarejestruj konto na %s, aby otrzymać klucz do API."

#: class.akismet-admin.php:235
msgid "You need to enter an API key to activate the Akismet service on your site."
msgstr "Potrzebujesz kulcza do API Akismeta, aby móc go używać na swojej witrynie."

#: class.akismet-admin.php:232
msgid "New to Akismet"
msgstr "Nowy użytkownik Akismeta"

#: class.akismet-admin.php:225
msgid "On this page, you are able to set up the Akismet plugin."
msgstr "Na tej stronie możesz ustawić wtyczkę Akismet."

#: class.akismet-admin.php:223 class.akismet-admin.php:234
#: class.akismet-admin.php:246
msgid "Akismet Setup"
msgstr "Ustawienia Akismeta"

#: class.akismet-admin.php:221 class.akismet-admin.php:259
#: class.akismet-admin.php:271
msgid "Overview"
msgstr "Przegląd"

#: class.akismet-admin.php:190
msgid "Re-adding..."
msgstr "Przywracanie..."

#: class.akismet-admin.php:189
msgid "(undo)"
msgstr "(cofnij)"

#: class.akismet-admin.php:188
msgid "URL removed"
msgstr "Adres URL został usunięty"

#: class.akismet-admin.php:187
msgid "Removing..."
msgstr "Usuwanie…"

#: class.akismet-admin.php:186
msgid "Remove this URL"
msgstr "Usuń ten adres URL"

#: class.akismet-admin.php:107 class.akismet-admin.php:1463
msgid "Akismet"
msgstr "Akismet"

#: class.akismet-admin.php:128 class.akismet-admin.php:282
#: class.akismet-admin.php:816 views/config.php:83
msgid "Settings"
msgstr "Ustawienia"

#: class.akismet-admin.php:103
msgid "Comment History"
msgstr "Oś czasu komentarzy"