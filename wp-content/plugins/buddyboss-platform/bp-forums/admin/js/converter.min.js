jQuery(document).ready(function(t){var r=t("#bbp-converter-message"),n=t("#bbp-converter-state-message"),s=t("#bbp-converter-stop"),a=t("#bbp-converter-start"),e=t("#_bbp_converter_restart"),o=t("#bbp-converter-status"),_=t("#bbp-converter-spinner"),i=t("#bbp-converter-settings"),c=t("#_bbp_converter_db_pass"),p=t(".bbp-db-pass-toggle"),v=t("#bbp-converter-step-percentage"),B=t("#bbp-converter-total-percentage"),u=i.find("table:first-of-type input, table:first-of-type select");function l(){var r;t.post(ajaxurl,(r={},t.each(i.serializeArray(),function(t,e){r[e.name]=e.value}),r._bbp_converter_restart&&e.removeAttr("checked"),r._bbp_converter_delay_time&&(BBP_Converter.state.delay=1e3*parseInt(r._bbp_converter_delay_time,10)),r.action="bbp_converter_process",r._ajax_nonce=BBP_Converter.ajax_nonce,r),function(t){try{var e=t.data;!0===t.success?(r=e,BBP_Converter.state.running&&(C(r.progress),((t,e)=>{v.width(t+"%"),B.width(e+"%")})(r.step_percent,r.total_percent),(t=>{var e=parseInt(BBP_Converter.state.delay,10)/1e3;o.text(BBP_Converter.strings.status_counting.replace("%s",e)),clearInterval(BBP_Converter.state.status),BBP_Converter.state.status=setInterval(function(){e--,o.text(BBP_Converter.strings.status_counting.replace("%s",e)),e<=0&&(clearInterval(BBP_Converter.state.status),parseInt(t.current_step,10)<parseInt(t.final_step,10)?o.text(BBP_Converter.strings.status_up_next.replace("%s",t.current_step)):o.text(BBP_Converter.strings.status_complete))},1e3)})(r),clearTimeout(BBP_Converter.state.running),BBP_Converter.state.running&&(BBP_Converter.state.running=setTimeout(function(){l()},parseInt(BBP_Converter.state.delay,10))),r.current_step===r.final_step)&&b(BBP_Converter.strings.button_start,BBP_Converter.strings.import_complete)):b()}catch(t){b()}var r},"json")}function b(t,e){clearTimeout(BBP_Converter.state.running),clearInterval(BBP_Converter.state.status),BBP_Converter.state.running=!1,BBP_Converter.state.status=!1,g(t=t||BBP_Converter.strings.button_continue,e=e||BBP_Converter.strings.status_stopped,BBP_Converter.strings.status_stopped),a.show(),s.hide(),_.css("visibility","hidden"),u.prop("readonly",!1)}function g(t,e,r){a.val(t),C(e),o.text(r)}function C(t){r.prepend(t="<p>"+t+"</p>")}p.on("click",function(t){var e="password"===c.attr("type")?"text":"password";c.attr("type",e),p.toggleClass("password").toggleClass("text"),t.preventDefault()}),a.on("click",function(t){clearTimeout(BBP_Converter.state.running),clearInterval(BBP_Converter.state.status),BBP_Converter.state.running=!0;var e=BBP_Converter.strings.start_continue;!1===BBP_Converter.state.started&&(e=BBP_Converter.strings.start_start,BBP_Converter.state.started=!0),g(BBP_Converter.strings.button_continue,e,BBP_Converter.strings.status_starting),r.addClass("started"),a.hide(),s.show(),_.css("visibility","visible"),r.css("display","block"),n.css("display","block"),u.prop("readonly",!0),l(),t.preventDefault()}),t(s).on("click",function(t){b(BBP_Converter.strings.button_continue,BBP_Converter.strings.import_stopped_user),t.preventDefault()})});