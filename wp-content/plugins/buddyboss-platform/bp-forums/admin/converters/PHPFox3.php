<?php

/**
 * Implementation of PHPFox v3.x Forum converter.
 *
 * @since bbPress (r5146)
 * @link Codex Docs http://codex.bbpress.org/import-forums/phpfox
 */
class PHPFox3 extends BBP_Converter_Base {

	/**
	 * Main Constructor
	 *
	 * @uses PHPFox3::setup_globals()
	 */
	function __construct() {
		parent::__construct();
		$this->setup_globals();
	}

	/**
	 * Sets up the field mappings
	 */
	public function setup_globals() {

		/** Forum Section */

		// Forum id (Stored in postmeta)
		$this->field_map[] = array(
			'from_tablename' => 'forum',
			'from_fieldname' => 'forum_id',
			'to_type'        => 'forum',
			'to_fieldname'   => '_bbp_forum_id',
		);

		// Forum parent id (If no parent, then 0, Stored in postmeta)
		$this->field_map[] = array(
			'from_tablename' => 'forum',
			'from_fieldname' => 'parent_id',
			'to_type'        => 'forum',
			'to_fieldname'   => '_bbp_forum_parent_id',
		);

		// Forum topic count (Stored in postmeta)
		$this->field_map[] = array(
			'from_tablename' => 'forum',
			'from_fieldname' => 'total_thread',
			'to_type'        => 'forum',
			'to_fieldname'   => '_bbp_topic_count',
		);

		// Forum reply count (Stored in postmeta)
		$this->field_map[] = array(
			'from_tablename' => 'forum',
			'from_fieldname' => 'total_post',
			'to_type'        => 'forum',
			'to_fieldname'   => '_bbp_reply_count',
		);

		// Forum total topic count (Stored in postmeta)
		$this->field_map[] = array(
			'from_tablename' => 'forum',
			'from_fieldname' => 'total_thread',
			'to_type'        => 'forum',
			'to_fieldname'   => '_bbp_total_topic_count',
		);

		// Forum total reply count (Stored in postmeta)
		$this->field_map[] = array(
			'from_tablename' => 'forum',
			'from_fieldname' => 'total_post',
			'to_type'        => 'forum',
			'to_fieldname'   => '_bbp_total_reply_count',
		);

		// Forum title.
		$this->field_map[] = array(
			'from_tablename' => 'forum',
			'from_fieldname' => 'name',
			'to_type'        => 'forum',
			'to_fieldname'   => 'post_title',
		);

		// Forum slug (Clean name to avoid conflicts)
		$this->field_map[] = array(
			'from_tablename'  => 'forum',
			'from_fieldname'  => 'name_url',
			'to_type'         => 'forum',
			'to_fieldname'    => 'post_name',
			'callback_method' => 'callback_slug',
		);

		// Forum description.
		$this->field_map[] = array(
			'from_tablename'  => 'forum',
			'from_fieldname'  => 'description',
			'to_type'         => 'forum',
			'to_fieldname'    => 'post_content',
			'callback_method' => 'callback_null',
		);

		// Forum display order (Starts from 1)
		$this->field_map[] = array(
			'from_tablename' => 'forum',
			'from_fieldname' => 'ordering',
			'to_type'        => 'forum',
			'to_fieldname'   => 'menu_order',
		);

		// Forum type (Forum = 0 or Category = 1, Stored in postmeta)
		$this->field_map[] = array(
			'from_tablename'  => 'forum',
			'from_fieldname'  => 'is_category',
			'to_type'         => 'forum',
			'to_fieldname'    => '_bbp_forum_type',
			'callback_method' => 'callback_forum_type',
		);

		// Forum status (0=Open or 1=Closed, Stored in postmeta)
		$this->field_map[] = array(
			'from_tablename'  => 'forum',
			'from_fieldname'  => 'is_closed',
			'to_type'         => 'forum',
			'to_fieldname'    => '_bbp_status',
			'callback_method' => 'callback_forum_status',
		);

		// Forum dates.
		$this->field_map[] = array(
			'to_type'      => 'forum',
			'to_fieldname' => 'post_date',
			'default'      => date( 'Y-m-d H:i:s' ),
		);
		$this->field_map[] = array(
			'to_type'      => 'forum',
			'to_fieldname' => 'post_date_gmt',
			'default'      => date( 'Y-m-d H:i:s' ),
		);
		$this->field_map[] = array(
			'to_type'      => 'forum',
			'to_fieldname' => 'post_modified',
			'default'      => date( 'Y-m-d H:i:s' ),
		);
		$this->field_map[] = array(
			'to_type'      => 'forum',
			'to_fieldname' => 'post_modified_gmt',
			'default'      => date( 'Y-m-d H:i:s' ),
		);

		/** Topic Section */

		// Topic id (Stored in postmeta)
		$this->field_map[] = array(
			'from_tablename' => 'forum_thread',
			'from_fieldname' => 'thread_id',
			'to_type'        => 'topic',
			'to_fieldname'   => '_bbp_topic_id',
		);

		// Topic reply count (Stored in postmeta)
		$this->field_map[] = array(
			'from_tablename'  => 'forum_thread',
			'from_fieldname'  => 'total_post',
			'to_type'         => 'topic',
			'to_fieldname'    => '_bbp_reply_count',
			'callback_method' => 'callback_topic_reply_count',
		);

		// Topic total reply count (Includes unpublished replies, Stored in postmeta)
		$this->field_map[] = array(
			'from_tablename'  => 'forum_thread',
			'from_fieldname'  => 'total_post',
			'to_type'         => 'topic',
			'to_fieldname'    => '_bbp_total_reply_count',
			'callback_method' => 'callback_topic_reply_count',
		);

		// Topic parent forum id (If no parent, then 0. Stored in postmeta)
		$this->field_map[] = array(
			'from_tablename'  => 'forum_thread',
			'from_fieldname'  => 'forum_id',
			'to_type'         => 'topic',
			'to_fieldname'    => '_bbp_forum_id',
			'callback_method' => 'callback_forumid',
		);

		// Topic author.
		$this->field_map[] = array(
			'from_tablename'  => 'forum_thread',
			'from_fieldname'  => 'user_id',
			'to_type'         => 'topic',
			'to_fieldname'    => 'post_author',
			'callback_method' => 'callback_userid',
		);

		// Topic content.
		// Note: We join the 'forum_post_text' table because 'forum_thread' table does not include content.
		// Note: We can use 'text' for original text OR 'text_parsed' for pre-parsed text output
		$this->field_map[] = array(
			'from_tablename'  => 'forum_post_text',
			'from_fieldname'  => 'text_parsed',
			'join_tablename'  => 'forum_thread',
			'join_type'       => 'LEFT',
			'join_expression' => 'ON forum_thread.start_id = forum_post_text.post_id',
			'to_type'         => 'topic',
			'to_fieldname'    => 'post_content',
			'callback_method' => 'callback_html',
		);

		// Topic title.
		$this->field_map[] = array(
			'from_tablename' => 'forum_thread',
			'from_fieldname' => 'title',
			'to_type'        => 'topic',
			'to_fieldname'   => 'post_title',
		);

		// Topic slug (Clean name to avoid conflicts)
		$this->field_map[] = array(
			'from_tablename'  => 'forum_thread',
			'from_fieldname'  => 'title_url',
			'to_type'         => 'topic',
			'to_fieldname'    => 'post_name',
			'callback_method' => 'callback_slug',
		);

		// Topic parent forum id (If no parent, then 0)
		$this->field_map[] = array(
			'from_tablename'  => 'forum_thread',
			'from_fieldname'  => 'forum_id',
			'to_type'         => 'topic',
			'to_fieldname'    => 'post_parent',
			'callback_method' => 'callback_forumid',
		);

		// Topic status (Open or Closed, PHPFox v3.5.x 0=open & 1=closed)
		$this->field_map[] = array(
			'from_tablename'  => 'forum_thread',
			'from_fieldname'  => 'is_closed',
			'to_type'         => 'topic',
			'to_fieldname'    => 'post_status',
			'callback_method' => 'callback_topic_status',
		);

		// Sticky status (Stored in postmeta))
		$this->field_map[] = array(
			'from_tablename'  => 'forum_thread',
			'from_fieldname'  => 'order_id',
			'to_type'         => 'topic',
			'to_fieldname'    => '_bbp_old_sticky_status',
			'callback_method' => 'callback_sticky_status',
		);

		// Topic dates.
		$this->field_map[] = array(
			'from_tablename'  => 'forum_thread',
			'from_fieldname'  => 'time_stamp',
			'to_type'         => 'topic',
			'to_fieldname'    => 'post_date',
			'callback_method' => 'callback_datetime',
		);
		$this->field_map[] = array(
			'from_tablename'  => 'forum_thread',
			'from_fieldname'  => 'time_stamp',
			'to_type'         => 'topic',
			'to_fieldname'    => 'post_date_gmt',
			'callback_method' => 'callback_datetime',
		);
		$this->field_map[] = array(
			'from_tablename'  => 'forum_thread',
			'from_fieldname'  => 'time_update',
			'to_type'         => 'topic',
			'to_fieldname'    => 'post_modified',
			'callback_method' => 'callback_datetime',
		);
		$this->field_map[] = array(
			'from_tablename'  => 'forum_thread',
			'from_fieldname'  => 'time_update',
			'to_type'         => 'topic',
			'to_fieldname'    => 'post_modified_gmt',
			'callback_method' => 'callback_datetime',
		);
		$this->field_map[] = array(
			'from_tablename'  => 'forum_thread',
			'from_fieldname'  => 'time_update',
			'to_type'         => 'topic',
			'to_fieldname'    => '_bbp_last_active_time',
			'callback_method' => 'callback_datetime',
		);

		/** Tags Section */

		// Topic id.
		$this->field_map[] = array(
			'from_tablename'  => 'tag',
			'from_fieldname'  => 'item_id',
			'to_type'         => 'tags',
			'to_fieldname'    => 'objectid',
			'callback_method' => 'callback_topicid',
		);

		// Taxonomy ID.
		$this->field_map[] = array(
			'from_tablename' => 'tag',
			'from_fieldname' => 'tag_id',
			'to_type'        => 'tags',
			'to_fieldname'   => 'taxonomy',
		);

		// Term text.
		$this->field_map[] = array(
			'from_tablename' => 'tag',
			'from_fieldname' => 'tag_text',
			'to_type'        => 'tags',
			'to_fieldname'   => 'name',
		);

		// Term slug.
		$this->field_map[] = array(
			'from_tablename'  => 'tag',
			'from_fieldname'  => 'tag_url',
			'to_type'         => 'tags',
			'to_fieldname'    => 'slug',
			'callback_method' => 'callback_slug',
		);

		/** Reply Section */

		// Reply id (Stored in postmeta)
		$this->field_map[] = array(
			'from_tablename' => 'forum_post',
			'from_fieldname' => 'post_id',
			'to_type'        => 'reply',
			'to_fieldname'   => '_bbp_post_id',
		);

		// Reply parent forum id (If no parent, then 0. Stored in postmeta)
		$this->field_map[] = array(
			'from_tablename'  => 'forum_thread',
			'from_fieldname'  => 'forum_id',
			'join_tablename'  => 'forum_post',
			'join_type'       => 'LEFT',
			'join_expression' => 'USING (thread_id)',
			'to_type'         => 'reply',
			'to_fieldname'    => '_bbp_forum_id',
			'callback_method' => 'callback_topicid_to_forumid',
		);

		// Reply parent topic id (If no parent, then 0. Stored in postmeta)
		$this->field_map[] = array(
			'from_tablename'  => 'forum_post',
			'from_fieldname'  => 'thread_id',
			'to_type'         => 'reply',
			'to_fieldname'    => '_bbp_topic_id',
			'callback_method' => 'callback_topicid',
		);

		// Reply author.
		$this->field_map[] = array(
			'from_tablename'  => 'forum_post',
			'from_fieldname'  => 'user_id',
			'to_type'         => 'reply',
			'to_fieldname'    => 'post_author',
			'callback_method' => 'callback_userid',
		);

		// Reply title.
		$this->field_map[] = array(
			'from_tablename'  => 'forum_thread',
			'from_fieldname'  => 'title',
			'join_tablename'  => 'forum_post',
			'join_type'       => 'LEFT',
			'join_expression' => 'USING (thread_id)',
			'to_type'         => 'reply',
			'to_fieldname'    => 'post_title',
			'callback_method' => 'callback_reply_title',
		);

		// Reply slug (Clean name to avoid conflicts)
		$this->field_map[] = array(
			'from_tablename'  => 'forum_thread',
			'from_fieldname'  => 'title_url',
			'join_tablename'  => 'forum_post',
			'join_type'       => 'LEFT',
			'join_expression' => 'USING (thread_id)',
			'to_type'         => 'reply',
			'to_fieldname'    => 'post_name',
			'callback_method' => 'callback_slug',
		);

		// Reply content.
		// Note: We join the 'forum_post_text' table because 'forum_post' table does not include content.
		// Note: We can use 'text' for original text OR 'text_parsed' for pre-parsed text output
		$this->field_map[] = array(
			'from_tablename'  => 'forum_post_text',
			'from_fieldname'  => 'text_parsed',
			'join_tablename'  => 'forum_post',
			'join_type'       => 'LEFT',
			'join_expression' => 'ON forum_post_text.post_id = forum_post.post_id WHERE forum_thread.start_id != forum_post.post_id',
			'to_type'         => 'reply',
			'to_fieldname'    => 'post_content',
			'callback_method' => 'callback_html',
		);

		// Reply parent topic id (If no parent, then 0)
		$this->field_map[] = array(
			'from_tablename'  => 'forum_post',
			'from_fieldname'  => 'thread_id',
			'to_type'         => 'reply',
			'to_fieldname'    => 'post_parent',
			'callback_method' => 'callback_topicid',
		);

		// Reply dates.
		$this->field_map[] = array(
			'from_tablename'  => 'forum_post',
			'from_fieldname'  => 'time_stamp',
			'to_type'         => 'reply',
			'to_fieldname'    => 'post_date',
			'callback_method' => 'callback_datetime',
		);
		$this->field_map[] = array(
			'from_tablename'  => 'forum_post',
			'from_fieldname'  => 'time_stamp',
			'to_type'         => 'reply',
			'to_fieldname'    => 'post_date_gmt',
			'callback_method' => 'callback_datetime',
		);
		$this->field_map[] = array(
			'from_tablename'  => 'forum_post',
			'from_fieldname'  => 'time_stamp',
			'to_type'         => 'reply',
			'to_fieldname'    => 'post_modified',
			'callback_method' => 'callback_datetime',
		);
		$this->field_map[] = array(
			'from_tablename'  => 'forum_post',
			'from_fieldname'  => 'time_stamp',
			'to_type'         => 'reply',
			'to_fieldname'    => 'post_modified_gmt',
			'callback_method' => 'callback_datetime',
		);

		/** User Section */

		// Store old User id (Stored in usermeta)
		$this->field_map[] = array(
			'from_tablename' => 'user',
			'from_fieldname' => 'user_id',
			'to_type'        => 'user',
			'to_fieldname'   => '_bbp_user_id',
		);

		// Store old User password (Stored in usermeta serialized with salt)
		$this->field_map[] = array(
			'from_tablename'  => 'user',
			'from_fieldname'  => 'password',
			'to_type'         => 'user',
			'to_fieldname'    => '_bbp_password',
			'callback_method' => 'callback_savepass',
		);

		// Store old User Salt (This is only used for the SELECT row info for the above password save)
		$this->field_map[] = array(
			'from_tablename' => 'user',
			'from_fieldname' => 'password_salt',
			'to_type'        => 'user',
			'to_fieldname'   => '',
		);

		// User password verify class (Stored in usermeta for verifying password)
		$this->field_map[] = array(
			'to_type'      => 'user',
			'to_fieldname' => '_bbp_class',
			'default'      => 'PHPFox3',
		);

		// User name.
		$this->field_map[] = array(
			'from_tablename' => 'user',
			'from_fieldname' => 'user_name',
			'to_type'        => 'user',
			'to_fieldname'   => 'user_login',
		);

		// User nice name.
		$this->field_map[] = array(
			'from_tablename' => 'user',
			'from_fieldname' => 'user_name',
			'to_type'        => 'user',
			'to_fieldname'   => 'user_nicename',
		);

		// User email.
		$this->field_map[] = array(
			'from_tablename' => 'user',
			'from_fieldname' => 'email',
			'to_type'        => 'user',
			'to_fieldname'   => 'user_email',
		);

		// User registered.
		$this->field_map[] = array(
			'from_tablename'  => 'user',
			'from_fieldname'  => 'joined',
			'to_type'         => 'user',
			'to_fieldname'    => 'user_registered',
			'callback_method' => 'callback_datetime',
		);

		// User display name.
		$this->field_map[] = array(
			'from_tablename' => 'user',
			'from_fieldname' => 'full_name',
			'to_type'        => 'user',
			'to_fieldname'   => 'display_name',
		);

	}

	/**
	 * This method allows us to indicates what is or is not converted for each
	 * converter.
	 */
	public function info() {
		return '';
	}

	/**
	 * This method is to save the salt and password together.  That
	 * way when we authenticate it we can get it out of the database
	 * as one value. Array values are auto sanitized by WordPress.
	 */
	public function callback_savepass( $field, $row ) {
		 $pass_array = array(
			 'hash' => $field,
			 'salt' => $row['salt'],
		 );
		 return $pass_array;
	}

	/**
	 * This method is to take the pass out of the database and compare
	 * to a pass the user has typed in.
	 */
	public function authenticate_pass( $password, $serialized_pass ) {
		$pass_array = unserialize( $serialized_pass );
		return ( $pass_array['hash'] == md5( md5( $password ) . $pass_array['salt'] ) );
	}

	/**
	 * Translate the forum type from PHPFox v3.5.x numeric's to WordPress's strings.
	 *
	 * @param int $status PHPFox v3.5.x numeric forum type
	 * @return string WordPress safe
	 */
	public function callback_forum_type( $status = 0 ) {
		switch ( $status ) {
			case 1:
				$status = 'category';
				break;

			case 0:
			default:
				$status = 'forum';
				break;
		}
		return $status;
	}

	/**
	 * Translate the forum status from PHPFox v3.5.x numeric's to WordPress's strings.
	 *
	 * @param int $status PHPFox v3.5.x numeric forum status
	 * @return string WordPress safe
	 */
	public function callback_forum_status( $status = 0 ) {
		switch ( $status ) {
			case 1:
				$status = 'closed';
				break;

			case 0:
			default:
				$status = 'open';
				break;
		}
		return $status;
	}

	/**
	 * Translate the post status from PHPFox v3.5.x numeric's to WordPress's strings.
	 *
	 * @param int $status PHPFox v3.5.x numeric topic status
	 * @return string WordPress safe
	 */
	public function callback_topic_status( $status = 0 ) {
		switch ( $status ) {
			case 1:
				$status = 'closed';
				break;

			case 0:
			default:
				$status = 'publish';
				break;
		}
		return $status;
	}

	/**
	 * Translate the topic sticky status type from PHPFox v3.5.x numeric's to WordPress's strings.
	 *
	 * @param int $status PHPFox v3.5.x numeric forum type
	 * @return string WordPress safe
	 */
	public function callback_sticky_status( $status = 0 ) {
		switch ( $status ) {
			case 1:
				$status = 'sticky';       // PHPFox Sticky 'topic_sticky = 1'
				break;

			case 0:
			default:
				$status = 'normal';       // PHPFox Normal Topic 'topic_sticky = 0'
				break;
		}
		return $status;
	}

	/**
	 * Verify the topic/reply count.
	 *
	 * @param int $count PHPFox v3.5.x topic/reply counts
	 * @return string WordPress safe
	 */
	public function callback_topic_reply_count( $count = 1 ) {
		$count = absint( (int) $count - 1 );
		return $count;
	}

	/**
	 * Set the reply title
	 *
	 * @param string $title PHPFox v3.5.x topic title of this reply
	 * @return string Prefixed topic title, or empty string
	 */
	public function callback_reply_title( $title = '' ) {
		$title = ! empty( $title ) ? __( 'Re: ', 'buddyboss' ) . html_entity_decode( $title, ENT_QUOTES | ENT_SUBSTITUTE | ENT_HTML401 ) : '';
		return $title;
	}
}
