/*------------------------------------------------------------------------------

This stylesheet is always loaded in wp-admin.

@since BuddyPress 1.6.0
@since BuddyPress 2.5.0

--------------------------------------------------------------------------------
TABLE OF CONTENTS:
--------------------------------------------------------------------------------
1.0 Welcome Screen
	1.1 Version Badge
	1.2 Credits Panel
2.0 Dashicons
	2.1 Top level menus
	2.2 Settings - Components
	2.3 Tools
	2.4 Tooltips
	2.5 Settings - Tabs
	2.6 Settings - Cards
	2.7 Document Extensions
3.0 Content
	3.1 Users List
	3.2 Activity List
	3.3 Groups List & Types
	3.4 Forums
	3.5 Site Notices
	3.6 Post Types
4.0 Emails - Edit page
5.0 Tools
6.0 Help
7.0 Plugins page
8.0 BuddyBoss App page
9.0 Miscellaneous
10.0 On-screen Notifications
11.0 Notices UI
12.0 Profile Photo
13.0 Group Directories Enhancement
14.0 BB Pro Active/Inactive Notices
15.0 Admin bar Notification
16.0 Moderation
17.0 Domain restriction
18.0 Redirection Settings
19.0 Reactions Settings
20.0 Upgrade
21.0 Activity Filter & Sorting
------------------------------------------------------------------------------*/

@import "tooltips";

/*------------------------------------------------------------------------------
 * 1.0 Welcome Screen
 *----------------------------------------------------------------------------*/

/*
 * 1.1 Version Badge
 */
.bp-badge {
	color: #d84800;
	display: inline-block;
	font: 400 150px/1 dashicons !important;
}

.bp-badge:before {
	content: "\f448";
}

.settings_page_bp-credits code,
.index_page_bp-about code {
	background-color: #e0e0e0;
	color: #636363;
	font-size: 1em;
}

/*
 * 1.2 Credits Panel
 */
.bp-about-wrap {
	position: relative;
	font-size: 15px;
	max-width: 1150px;
	margin-top: 25px;
}

.bp-about-wrap .bp-admin-card {
	margin: 20px 0 0;
}

.bp-about-wrap .bp-admin-card h2 {
	font-weight: 600;
}

.bp-about-wrap .bp-admin-card h2:before {
	content: none;
}

.bp-about-wrap .bp-admin-card img {
	margin: 0;
	max-width: 100%;
	height: auto;
	vertical-align: middle;
}

.bp-about-wrap .bp-admin-card ul {
	margin: 0;
}

.bp-about-wrap .bp-admin-card p {
	line-height: 1.5;
	font-size: 14px;
	margin: 0;
}

.bp-about-wrap .bp-admin-card .wp-person {
	display: inline-block;
	vertical-align: top;
	margin: 0 10px 0 0;
	height: 70px;
	width: 280px;
}

.bp-about-wrap .bp-admin-card .wp-person .gravatar {
	float: left;
	margin: 0 10px 10px 0;
	border-radius: 100%;
	width: 60px;
	height: 60px;
}

.bp-about-wrap .bp-admin-card .wp-person .web {
	margin: 6px 0 2px;
	font-size: 16px;
	font-weight: 400;
	line-height: 2;
	text-decoration: none;
}

.bp-about-wrap .bp-admin-card .wp-person .title {
	display: block;
	font-weight: 300;
}

.bp-about-wrap .bp-admin-card p.wp-credits-list a {
	white-space: nowrap;
}

/*------------------------------------------------------------------------------
 * 2.0 Dashicons
 *----------------------------------------------------------------------------*/

/*
 * 2.1 Top level menus
 */
#adminmenu #toplevel_page_bp-activity_user .wp-menu-image:before,
#adminmenu #toplevel_page_bp-activity_network .wp-menu-image:before {
	content: "\f130";
}

#adminmenu #toplevel_page_bp-groups_user .wp-menu-image:before,
#adminmenu #toplevel_page_bp-groups_network .wp-menu-image:before {
	content: "\f307";
}

#adminmenu #toplevel_page_bp-notifications .wp-menu-image:before,
#adminmenu #toplevel_page_bp-notifications_user .wp-menu-image:before,
#adminmenu #toplevel_page_bp-notifications_network .wp-menu-image:before {
	content: "\f439";
}

#adminmenu #toplevel_page_bp-messages .wp-menu-image:before,
#adminmenu #toplevel_page_bp-messages_user .wp-menu-image:before,
#adminmenu #toplevel_page_bp-messages_network .wp-menu-image:before {
	content: "\f465";
}

#adminmenu #toplevel_page_bp-friends .wp-menu-image:before,
#adminmenu #toplevel_page_bp-friends_user .wp-menu-image:before,
#adminmenu #toplevel_page_bp-friends_network .wp-menu-image:before {
	content: "\f325";
}

#adminmenu #toplevel_page_bp-settings .wp-menu-image:before,
#adminmenu #toplevel_page_bp-settings_user .wp-menu-image:before,
#adminmenu #toplevel_page_bp-settings_network .wp-menu-image:before {
	content: "\f108";
}

#adminmenu li.toplevel_page_bp-components .wp-menu-image,
#adminmenu li.toplevel_page_bp-general-settings .wp-menu-image {
	content: "\f448";
}

#adminmenu li.toplevel_page_buddyboss-platform a[href="bp-plugin-seperator"] {
	pointer-events: none;
	cursor: default;
	padding-bottom: 0;
	margin-right: 16px;
	margin-left: 14px;
	margin-bottom: 5px;
	border-bottom: 1px solid #555;
}

#adminmenu li.toplevel_page_buddyboss-platform a[href="bp-plugin-seperator"] {
	pointer-events: none;
	cursor: default;
	padding-bottom: 0;
	margin-right: 16px;
	margin-left: 14px;
	margin-bottom: 5px;
	border-bottom: 1px solid #555;
}

#adminmenu li.toplevel_page_buddyboss-platform a[href="bp-plugin-separator-notice"] {
	pointer-events: none;
	cursor: default;
	padding-bottom: 0;
	margin-right: 16px;
	margin-left: 14px;
	margin-bottom: 5px;
	border-bottom: 1px solid #555;
}

#adminmenu li.toplevel_page_buddyboss-platform .wp-menu-image:before {
	content: "\edc8";
	font-family: "bb-icons";/* stylelint-disable-line */
	font-style: normal;
	font-weight: 300;
	speak: none;
	display: inline-block;
	text-decoration: inherit;
	width: 1em;
	margin-right: 0.2em;
	text-align: center;
	font-variant: normal;
	text-transform: none;
	line-height: 20px;
	margin-left: 0.2em;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	font-size: 23px;
}

/*
 * 2.2 Settings - Components
 */
#bp-admin-component-form .tablenav {
	margin-bottom: 10px;
}

#bp-admin-component-form .wp-list-table.plugins .plugin-title {
	width: 25%;
	padding-bottom: 15px;
}

#bp-admin-component-form .subsubsub {
	padding-bottom: 5px;
}

#bp-admin-component-form span.required {
	color: #32373c;
}

.nav-settings-subsubsub {
	padding-bottom: 0;

	.subsubsub {
		float: none;
	}
}

@media screen and (max-width: 782px) {

	#bp-admin-component-form .wp-list-table.plugins .plugin-title {
		display: block;
		width: auto;
	}

	#bp-admin-component-form .subsubsub {
		margin-bottom: 0;
		padding-bottom: 35px;
	}
}

/*
 * 2.3 Tools
 */
#adminmenu .toplevel_page_network-tools div.wp-menu-image:before {
	content: "";
}

.buddyboss_page_bp-tools {

	.section-default_data {

		ul li.main {
			border-bottom: 1px solid #eee;
			margin: 0 0 10px;
			padding: 5px 0;
		}

		ul li.main ul {
			margin: 5px 0 0 25px;
		}

		#message ul.results li {
			list-style: disc;
			margin-left: 25px;
		}
	}
}


.buddyboss_page_bp-repair-community {

	.section-default_data {

		ul li.main {
			border-bottom: 1px solid #eee;
			margin: 0 0 10px;
			padding: 5px 0;
		}

		ul li.main ul {
			margin: 5px 0 0 20px;
		}

		#message ul.results li {
			list-style: disc;
			margin-left: 25px;
		}
	}
}

/*
 * 2.5 Settings - Tabs
 */
tr.bp-search-child-field,
tr.bp-search-child-field th {
	padding: 0;
}

tr.bp-search-child-field td {
	padding: 6px 25px;
}

tr.bp-search-parent-field th,
tr.bp-search-parent-field td {
	padding: 15px 20px 10px 0;
}

tr.bp-search-parent-field td p {
	margin-top: 0;
}

tr.bp-search-parent-field td p.section-header {
	margin: 0 0 20px;
}

tr.bp-custom-post-type-child-field td {
	padding: 6px 40px;
}

tr.bp-search-parent-field label,
tr.bp-custom-post-type-child-field {
	font-weight: 600;
}

tr.bp-search-parent-field label {
	font-size: 16px;
}

/*
 * 2.6 Settings - Cards
 */
.bp-admin-card {
	position: relative;
	margin-top: 20px;
	padding: 2.2em 2.8em;
	min-width: 255px;
	max-width: 1050px;
	border-radius: 6px;
	border: 1px solid #ccd0d4;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
	background: #fff;
}

.bp-admin-card .form-table td p {
	color: #999;
	margin-top: 6px;
}

.bp-admin-card .form-table td a.button-secondary {
	margin-top: 1px;
}

.bp-admin-card h2 {
	line-height: 1;
	margin: 0 0 1em;
	padding-bottom: 1.2em;
	font-size: 22px;
	font-weight: 700;
	color: #333;
	border-bottom: 1px solid #eaeaea;
	align-items: center;
	display: flex;

	span {
		font-weight: 400;
	}

	a {
		text-decoration: none;
	}

	[class*=" bb-icon-"] {
		font-size: 36px;
		width: 36px;
		margin-right: 16px;
		vertical-align: middle;
		position: relative;

		&:before {
			margin: 0;
			color: #f6f6f6;
			position: relative;
			z-index: 2;
		}

		&:after {
			position: absolute;
			left: 4px;
			top: 4px;
			z-index: 1;
			content: " ";
			width: 28px;
			height: 28px;
			background: #333;
		}
	}

}

.bp-admin-card h3 {
	font-size: 19px;
	font-weight: 600;
}

.bp-admin-card p.alert {
	padding: 10px 12px;
	background: #f3f5f6;
	border-radius: 3px;
	border: 1px solid #ccd0d4;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);

	code a {
		text-decoration: none;
		color: #444;

		&:hover {
			color: #0073aa;
		}
	}
}


.buddyboss_page_bp-settings .section-bp_custom_post_type .form-table {

	.child-no-padding.bp-display-none td {
		background: #f0f0f1;
		margin: 10px 0 0 10px;
		padding: 10px 15px 10px 20px;
		border: 1px solid #ccd0d4;
		display: block;
		border-radius: 4px;

		p {
			margin: 0;
		}
	}

	.child-no-padding.bp-display-none.child-custom-post-type td {
		margin: 10px 0 10px 10px;
	}

	@media screen and (max-width: 782px) {

		.child-no-padding.bp-display-none td,
		.child-no-padding.bp-display-none.child-custom-post-type td {
			margin-left: 0;
		}
	}
}

/* Media */
.section-bp_video_settings_extensions table.form-table tbody tr.video-extensions-listing > th,
.bp-admin-card.section-bp_video_settings_extensions h2:before,
.bp-admin-card.section-bp_document_settings_extensions h2:before {
	display: none;
}

.section-bp_video_settings_extensions table.form-table tbody tr.video-extensions-listing > td {
	padding: 0;
}

.bp-admin-card.section-bp_media_settings_symlinks {
	display: block !important;
}

/*
.bp-admin-card.section-bp_media_settings_symlinks h2:before {

	background-image: url(../../images/admin/icons/fontawesome/solid/server.svg);
}
*/

/* Connections */

/*
.bp-admin-card.section-bp_friends h2:before {

	background-image: url(../../images/admin/icons/fontawesome/solid/user-friends.svg);
	background-size: 21px 21px;
}
*/

/* Invites */

/*
.bp-admin-card.section-bp_invites h2:before {

	background-image: url(../../images/admin/icons/fontawesome/solid/envelope.svg);
}
*/

/* Moderation */

/*
.bp-admin-card.section-bp_moderation_settings_blocking h2:before {

	background-image: url(../../images/admin/icons/fontawesome/solid/ban.svg);
	background-size: 20px 20px;
}

.bp-admin-card.section-bp_moderation_settings_reporting h2:before {

	background-image: url(../../images/admin/icons/fontawesome/solid/flag.svg);
}
*/

/* Search */

/*
.bp-admin-card.section-bp_search_settings_community h2:before,
.bp-admin-card.section-bp_search_settings_post_types h2:before {

	background-image: url(../../images/admin/icons/fontawesome/solid/search.svg);
}

.bp-admin-card.section-bp_search_settings_general h2:before {

	background-image: url(../../images/admin/icons/fontawesome/solid/caret-square-down.svg);
}
*/

/* Notification */

/*
.bp-admin-card.section-bp_notification_settings_automatic h2:before {

	background-image: url(../../images/admin/icons/fontawesome/solid/bell.svg);
}
*/

/* Labs */

/*
.bp-admin-card.section-bp_labs_settings_notifications h2:before {

	background-image: url(../../images/admin/icons/fontawesome/solid/flask.svg);
}
*/

.bp-admin-card.section-bp_search_settings_community table,
.bp-admin-card.section-bp_search_settings_post_types table {
	margin-bottom: 20px;
}

.bp-admin-card.section-bp_search_settings_community th[scope="row"],
.bp-admin-card.section-bp_search_settings_post_types th[scope="row"] {
	width: 1px;
}

/*
 * 2.7 Document Extensions
 */

.section-bp_document_settings_extensions table.form-table tbody tr.document-extensions-listing {

	th {
		display: none;
	}

	td table.extension-listing thead tr th {
		display: table-cell;
	}

	> td {
		padding: 0;
	}
}

.section-bp_document_settings_extensions + .submit > a {
	margin-left: 5px;
}

table.extension-listing {
	border-radius: 4px;
	margin: 9px 0 12px;

	thead th {
		padding: 12px 18px;

		&.ext-head-extension {
			width: 2.5em;
		}

		&.ext-head-desc {
			width: 16%;
		}

		&.ext-head-icon {
			width: 4em;
		}
	}

	thead td.ext-head-enable {
		width: 1.2em;
	}

	tbody td {
		padding: 10px;

		[class*="bb-icons-rl-"],
		[class*=" bb-icon-"] {
			font-size: 33px;
			color: #999;
			margin-left: 8px;
			vertical-align: middle;
		}

		input.error {
			border: 1px solid #f00;
		}

		input.hide-border {
			border: none;
			background-color: transparent;
			color: #555;
			margin: 0;

			&:focus {
				background-color: #eee;
			}
		}

		input[type="text"] {
			width: 100%;
		}

		#btn-remove-extensions {
			cursor: pointer;
			color: #a00;
			font-size: 18px;
			vertical-align: middle;
			margin-left: 6px;
		}
	}

	tbody tr.custom-extension td input.extension-mime[type="text"] {
		width: 50%;
	}

	tfoot td {

		#btn-add-extensions,
		#btn-add-video-extensions,
		#btn-check-mime-type {
			cursor: pointer;
			float: right;
			margin-left: 8px;
		}
	}


	@media screen and (max-width: 782px) {
		display: block;

		tbody,
		thead,
		tr {
			display: block;
			width: 100%;

			&.extra-extension {

				input[type="text"] {
					margin-top: 15px;
					margin-left: 10px;
					width: calc(100% - 10px);
				}

				.icon-select-main {
					margin-left: 10px;
				}
			}
		}

		tr.custom-extension {
			background-color: #fff;

			td {
				padding-left: 20px !important;
			}
		}

		tfoot {
			width: 100%;
			display: inline-block;
			box-sizing: border-box;

			td #btn-add-extensions {
				margin: 10px 0 15px;
			}

		}

		thead .ext-head:not(.ext-head-enable) {
			display: none !important;
		}

		.ext-head.ext-head-enable {
			width: 100% !important;
			padding: 10px 0 0 10px !important;
		}

		tbody {

			td {
				padding-left: 10px !important;
				width: 100% !important;
				box-sizing: border-box;

				> i {
					margin: 15px 0 10px 10px;
				}

				&:before {
					top: 0;
					left: 20px !important;
					font-weight: 600;
				}

				&:first-child {
					padding-left: 20px !important;
				}

				.extension-mime {
					margin-bottom: 15px;
				}

				.btn-check-mime-type {
					margin-left: 10px;
				}
			}
		}

	}

	select.extension-icon {
		display: none;
	}

	.icon-select-main {
		position: relative;
		display: inline-block;
		vertical-align: middle;
		width: 100%;

		.icon-select-button {
			font-size: 14px;
			line-height: 2;
			color: #32373c;
			border: 1px solid #7e8993;
			border-width: 1px;
			box-shadow: none;
			border-radius: 3px;
			padding: 3px 24px 3px 8px;
			-webkit-appearance: none;
			background: #fff url(data:image/svg+xml;charset=US-ASCII,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M5%206l5%205%205-5%202%201-7%207-7-7%202-1z%22%20fill%3D%22%23555%22%2F%3E%3C%2Fsvg%3E) no-repeat right 5px top 55%;
			background-size: 16px 16px;
			cursor: pointer;
			vertical-align: middle;
			max-width: 100%;
			width: 100%;
			display: inline-block;
			box-sizing: border-box;

			li {
				list-style: none;
				margin: 0;
				line-height: 20px;
				white-space: nowrap;
				max-width: 100%;
				overflow: hidden;
				text-overflow: ellipsis;
				text-align: left;
				color: #555;
			}

			i {
				font-size: 22px;
				color: #888;
				margin-left: 0;
				margin-right: 10px;
				vertical-align: middle;
			}
		}

		.custom-extension-list {
			position: absolute;
			left: 0;
			right: 0;
			min-width: 160px;
			background-color: #fff;
			border: 1px solid #ccd0d4;
			border-radius: 3px;
			box-shadow: 0 2px 12px 1px rgba(0, 0, 0, 0.1);
			padding: 0;
			bottom: 100%;
			max-height: 60vh;
			overflow: auto;
			display: none;

			ul.custom-extension-list-select {
				font-size: 0;
				color: #888;
			}

			li {
				list-style: none;
				cursor: pointer;
				font-size: 14px;
				padding: 8px 12px;
				margin: 0;

				&:first-child {
					border-bottom: 1px solid #eee;
					padding-top: 10px;
					padding-bottom: 10px;
				}

				&:nth-child(2) {
					padding-top: 10px;
				}

				&:last-child {
					padding-bottom: 10px;
				}

				&:hover {
					background-color: #f9f9f9;
				}

				span {
					vertical-align: middle;
				}
			}

			i {
				font-size: 28px;
				margin-left: 0;
				margin-right: 10px;
				vertical-align: middle;
				color: #888;
			}
		}
	}

}

@media screen and (max-width: 1023px) {

	#bp-hello-container.bp-hello-mime {
		bottom: 0;

		.bp-hello-header {
			height: auto;
			max-height: initial;
			min-height: 58px;
		}
	}
}


/*
 * 3.0 Content
 */

/*
 * 3.1 Users List
 */
body.site-users-php th#role,
body.users-php th#role,
body.users_page_bp-signups th#count_sent {
	width: 10%;
}

body.site-users-php th#name,
body.site-users-php th#email,
body.users-php th#name,
body.users-php th#registered,
body.users-php th#email,
body.users_page_bp-signups th#name,
body.users_page_bp-signups th#registered,
body.users_page_bp-signups th#email,
body.users_page_bp-signups th#date_sent {
	width: 15%;
}

body.users-php th#blogs,
body.users_page_bp-signups th#blogs {
	width: 20%;
}

body.users_page_bp-signups th.column-count_sent,
body.users_page_bp-signups td.count_sent {
	text-align: center;
}

.bp-signups-list table {
	margin: 1em 0;
}

.bp-signups-list .column-fields {
	font-weight: 700;
}

/*
 * 3.2 Activity List
 */

#bp-activities-form .fixed .column-author {
	width: 15%;
}

#bp-activities-form #the-comment-list td.comment {

	img,
	video {
		max-width: 300px;
		display: block;
		margin: 0.6em 0;
	}
}

.activity-attached-gif-container {
	cursor: pointer;
	max-width: 480px;
}

.activity-attached-gif-container .gif-player {
	position: relative;
	max-width: 480px;
}

.activity-attached-gif-container .gif-player video {
	margin: 0;
}

.activity-attached-gif-container .gif-play-button {
	height: 80px;
	width: 80px;
	position: absolute;
	left: 29%;
	margin-left: -32px;
	top: 69%;
	margin-top: -44px;
}

.activity-attached-gif-container .gif-play-button .bb-icon-play-thin {
	font-size: 80px;
	width: 80px;
	height: 80px;
	margin: 0;
	padding: 0;
	line-height: 0;
}

.activity-attached-gif-container .gif-icon {
	background: url(../images/GIF.svg) center no-repeat;
	position: absolute;
	display: block;
	height: 28px;
	width: 44px;
	bottom: 0;
	left: 0;
	border-radius: 0 4px 0 0;
	background-size: 28px;
	padding: 8px;
	background-color: #000;
	opacity: 0.45;
}

/*
 * 3.3 Groups List & Types
 */
form#bp-groups-form .fixed .column-comment {
	width: 20%;
}

/*
 * 3.4 Forums
 */

@media only screen and (min-width: 700px) {

	#poststuff #bbp_reply_attributes select {
		min-width: 70%;
	}
}

body.post-type-reply #edit-slug-box {
	padding-left: 0;
	margin-top: 0;
}

#bbpress_group_admin_ui_meta_box fieldset {
	min-width: 230px;
}

#bbp-dashboard-right-now p.sub,
#bbp-dashboard-right-now .table,
#bbp-dashboard-right-now .versions {
	margin: -12px;
}

#bbp-dashboard-right-now .inside {
	font-size: 12px;
	padding-top: 20px;
	margin-bottom: 0;
}

#bbp-dashboard-right-now p.sub {
	padding: 5px 0 15px;
	color: #8f8f8f;
	font-size: 14px;
	position: absolute;
	top: -17px;
	left: 15px;
	right: 0;
}

#bbp-dashboard-right-now .table {
	margin: 0;
	padding: 0;
	position: relative;
}

#bbp-dashboard-right-now .table_content {
	float: left;
	border-top: #ececec 1px solid;
	width: 45%;
}

#bbp-dashboard-right-now .table_discussion {
	float: right;
	border-top: #ececec 1px solid;
	width: 45%;
}

#bbp-dashboard-right-now table td {
	padding: 3px 0;
	white-space: nowrap;
}

#bbp-dashboard-right-now table tr.first td {
	border-top: none;
}

#bbp-dashboard-right-now td.b {
	padding-right: 6px;
	padding-left: 0;
	text-align: right;
	font-family: Georgia, "Times New Roman", "Bitstream Charter", Times, serif;
	font-size: 14px;
	width: 1%;
}

#bbp-dashboard-right-now td.b a {
	font-size: 18px;
}

#bbp-dashboard-right-now .t .b {
	font-size: 18px;
	font-family: Georgia, "Times New Roman", "Bitstream Charter", Times, serif;
}

#bbp-dashboard-right-now td.b a:hover {
	color: #d54e21;
}

#bbp-dashboard-right-now .t {
	font-size: 12px;
	padding-right: 12px;
	padding-left: 0;
	padding-top: 6px;
	color: #777;
}

#bbp-dashboard-right-now .t a {
	white-space: nowrap;
}

#bbp-dashboard-right-now .spam {
	color: #f00;
}

#bbp-dashboard-right-now .waiting {
	color: #e66f00;
}

#bbp-dashboard-right-now .approved {
	color: #0f0;
}

#bbp-dashboard-right-now a.button {
	float: right;
	clear: right;
	position: relative;
	top: -5px;
}

/*
 * 3.5 Site Notices
 */
.bp-new-notice-panel {
	background: #fff;
	border: 1px solid #e5e5e5;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
	font-size: 13px;
	line-height: 2.1;
	margin: 1.5em 0 3em;
	overflow: auto;
	padding: 10px 25px 25px;
	position: relative;
}

.bp-new-notice-panel label {
	clear: both;
	float: left;
	margin-right: 3%;
	width: 20%;
}

.bp-new-notice-panel input,
.bp-new-notice-panel textarea {
	clear: none;
	margin-bottom: 1em;
	width: 75%;
}

.bp-new-notice-panel input[type="text"]:after,
.bp-new-notice-panel textarea:after {
	clear: both;
	content: " ";
	display: table;
}

.bp-new-notice-panel .button-primary {
	margin-left: 23%;
	width: auto;
}

.bp-notice-about {
	font-size: 1em;
	margin-bottom: 1em;
}

.bp-new-notice {
	margin-bottom: 1em;
	margin-top: 0;
}

.bp-notices-list {
	margin-bottom: 0;
}

@media screen and (max-width: 782px) {

	.bp-new-notice-panel {
		margin-bottom: 1.5em;
	}

	.bp-new-notice-panel input,
	.bp-new-notice-panel textarea {
		margin-left: 0;
		width: 100%;
	}

	.bp-new-notice-panel .button-primary {
		margin-left: 0;
		width: auto;
	}

	.bp-new-notice-panel .button {
		max-width: 45%;
		word-wrap: break-word;
	}

	.bp-notice-about {
		margin-top: 0;
		margin-bottom: 1em;
	}

	.bp-new-notice {
		margin-bottom: 0.5em;
	}

	#bp-activities-form .wp-list-table th.column-primary.column-author,
	form#bp-groups-form .wp-list-table th.column-primary.column-comment {
		width: auto;
	}

	.wp-list-table {

		&.activities,
		&.groups {

			tr:not(.inline-edit-row):not(.no-items) td:not(.column-primary):before {
				font-weight: 600;
			}
		}
	}
}

/*
 * 3.6 Post Types
 */
body.post-type-bp-member-type .postbox .inside,
body.post-type-bp-group-type .postbox .inside {
	padding: 0 12px 12px;

	.bb-meta-box-label-color-main {

		.bb-meta-box-colorpicker {
			display: flex;
			flex-direction: row;
		}

		.bb-colorpicker-row-one {
			display: flex;
			flex-direction: column;
			vertical-align: middle;
			text-align: -webkit-match-parent;
			flex-basis: auto;

			&:not(:first-child) {
				margin-left: 15px;
			}

			.bb-colorpicker-label {
				margin-bottom: 10px;
				color: #686868;
			}
		}

		.bp-member-type-hide-colorpicker,
		.bp-group-type-hide-colorpicker {
			display: none;
		}

		.bp-member-type-show-colorpicker,
		.bp-group-type-show-colorpicker {
			display: block;
		}
	}
}

body.post-type-bp-member-type #member-type-shortcode,
body.post-type-bp-group-type #group-type-shortcode {
	line-height: 1;
	margin-right: 10px;
	padding: 7px;
	display: inline-block;
}

table.bp-postbox-table {
	background: #f9f9f9;
	margin-top: 22px;
	border-radius: 3px;

	thead th {
		font-weight: 600;
	}

	tbody th {
		width: 23%;
		vertical-align: top;
	}

	tbody td input[type="checkbox"] {
		margin-right: 8px;
	}
}

/*------------------------------------------------------------------------------
 * 4.0 Emails - Edit Page
 *----------------------------------------------------------------------------*/
body.post-type-bp-email #excerpt {
	height: auto;
}

body.post-type-bp-email th#situation {
	width: 20%;
}

body.post-type-bp-email td.column-situation ul {
	margin: 0;
}

body.post-type-bp-email .categorydiv label {
	display: block;
	float: left;
	padding-left: 25px;
	text-indent: -25px;
}

/*------------------------------------------------------------------------------
 * 5.0 Tools
 *----------------------------------------------------------------------------*/
.buddyboss_page_bp-tools .wrap,
.buddyboss_page_bp-repair-community .wrap,
.buddyboss_page_bbp-repair .wrap {
	max-width: 950px;
}

.buddyboss_page_bp-tools p,
.buddyboss_page_bp-repair-community p,
.buddyboss_page_bbp-repair p {
	line-height: 2;
}

.buddyboss_page_bp-tools fieldset,
.buddyboss_page_bp-repair-community fieldset,
.buddyboss_page_bbp-repair fieldset {
	margin: 2em 0 0;
}

.buddyboss_page_bp-tools legend,
.buddyboss_page_bp-repair-community legend,
.buddyboss_page_bbp-repair legend {
	color: #23282d;
	font-size: 1.3em;
	font-weight: 600;
	margin: 1em 0;
}

.buddyboss_page_bp-tools label,
.buddyboss_page_bp-repair-community label,
.buddyboss_page_bbp-repair label {
	clear: left;
	display: block;
	line-height: 1.5;
	margin: 0 0 7px;
	padding: 3px;
	border-radius: 3px;
}

@media screen and (max-width: 782px) {

	.buddyboss_page_bp-repair-community p,
	.buddyboss_page_bbp-repair p {
		line-height: 1.5;
	}

	.buddyboss_page_bp-repair-community label,
	.buddyboss_page_bbp-repair label {
		margin-bottom: 1em;
		padding-right: 25px;
		text-indent: -33px;
	}

	.buddyboss_page_bp-repair-community .checkbox,
	.buddyboss_page_bbp-repair .checkbox {
		padding: 0 0 0 30px;
	}
}

/*------------------------------------------------------------------------------
 * 6.0 Help
 *----------------------------------------------------------------------------*/

.bp-display-none {
	display: none;
}

.wrap .bp-help-doc {
	position: relative;
	margin-top: 20px;
	padding: 2.2em 2.8em 2em;
	min-width: 255px;
	max-width: 1050px;
	border-radius: 6px;
	border: 1px solid #e5e5e5;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
	background: #fff;
}

.bp-help-card-grid {
	margin-top: 20px;
	min-width: 255px;
	max-width: 1050px;

	.bp-help-main-menu-wrap {
		display: flex;
		flex-flow: wrap;
		margin-left: -10px;
		margin-right: -10px;
	}

	.bp-help-card {
		margin: 0 10px 2%;
		background-color: #fff;
		border: 1px solid #ccd0d4;
		border-radius: 5px;
		box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
		box-sizing: border-box;
		flex: 0 0 calc(33.33% - 20px);
		min-width: 0;

		.inside {
			padding: 1.8em 2em 5em;
		}

		h2 {
			font-size: 18px;
			padding: 0;
			margin: 0 0 1em;
			line-height: 1.4;
		}

		p:first-child {
			margin-top: 0;
		}
	}

	.bp-help-content-wrap {
		display: flex;
	}
}

.bp-help-content-wrap {
	display: flex;
	flex-flow: row wrap;
	min-width: 255px;
	max-width: 1050px;
	background-color: #fff;
	border-radius: 6px;
	border: 1px solid #ccd0d4;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
	box-sizing: border-box;
}

.bp-help-sidebar > ul {
	margin: 0;

	> li {

		> a {
			color: #23282d;
			font-size: 16px;
			font-weight: 600;
		}

		> ul {
			margin-top: 20px;
		}

		> ul li {
			line-height: 1.6;
			margin: 1.2em 0;
		}

		ul > li.selected > a {
			font-weight: 600;
		}

		> ul > li > ul > li {
			margin-left: 1.6em;
			list-style-type: disc;
		}

	}

	span.open {
		font-size: 0;

		&:after {
			font-family: dashicons;
			content: "\f345";
			margin: 0 5px;
			display: inline-block;
			vertical-align: bottom;
			font-size: 10px;
			color: #999;
			cursor: pointer;
		}

		&.active {

			&:after {
				transform: rotate(90deg);
			}
		}
	}

}

.bp-help-content {

	p,
	ul li {
		line-height: 2;
	}

	ul.bp-help-menu {
		margin: 0 0 2.5em;

		li {
			display: inline;
		}


		li.miscellaneous {
			display: none !important;
		}

		li:not(:last-child):after {
			font-family: dashicons;
			content: "\f345";
			margin: 0 5px;
			display: inline-block;
			vertical-align: middle;
			font-size: 10px;
			color: #999;
		}

		li:last-child a {
			color: #999;
		}
	}

	h1 {
		font-weight: 500;
		font-size: 2em;
		margin-bottom: 1em;
	}

	h2 {
		font-size: 1.5em;
		margin-bottom: 1em;
	}

	h3 {
		font-size: 1.2em;
		margin-bottom: 1em;
	}

	code {
		border-radius: 3px;
	}

	ul,
	ol {
		margin-left: 25px;
		margin-top: 6px;
		line-height: 1.5;
	}

	ul li {
		list-style-type: circle;
	}

	ol li {
		list-style-type: decimal;
	}

	hr {
		border-top: none;
		border-bottom: 1px solid #eaeaea;
	}

	table {
		border: 1px solid #eaeaea;
		border-collapse: collapse;
		width: 100%;
		margin-bottom: 3em;
	}

	th {
		background: #f9f9f9;
		font-weight: 500;
		border-bottom: 1px solid #eaeaea;
	}

	th,
	td {
		padding: 10px 12px;
	}

	img {
		max-width: 100%;
		width: auto;
		height: auto;
		border: 1px solid #ddd;
		border-radius: 5px;
		box-shadow: 0 3px 20px -1px rgba(7, 10, 25, 0.1);
	}

	.article-child {

		> ul > li {
			list-style-type: disc;
		}

		.actions,
		.sub-menu-count {
			display: none;
		}
	}
}

.bp-help-main-menu-wrap div.notice {
	margin: 0 10px !important;
	width: 100%;
}

@media screen and (min-width: 782px) {

	.bp-help-sidebar {
		background: #f9f9f9;
		padding: 2.5em;
		border-right: 1px solid #ccd0d4;
		border-radius: 6px 0 0 6px;
		-webkit-box-flex: 0;
		-ms-flex: 0 0 200px;
		flex: 0 0 200px;
		min-width: 0;
		overflow: initial;
		width: 100%;
		min-height: 60vh;
	}

	.bp-help-content {
		padding: 2.5em 3em;
		-webkit-box-flex: 1;
		-ms-flex: 1;
		flex: 1;
		min-width: 0;
	}

	.bp-help-content th:first-child {
		width: 40%;
	}
}

@media screen and (max-width: 781px) {

	.bp-help-card-grid {

		.bp-help-card {
			flex: 0 0 calc(50% - 20px);
			min-width: 0;
		}
	}

	.bp-help-sidebar {
		background: #f9f9f9;
		padding: 2.5em;
		width: 100%;
		min-height: auto;
		border-bottom: 1px solid #ccd0d4;
		border-radius: 6px 6px 0 0;
	}

	.bp-help-content {
		padding: 2.5em;
		width: 100%;
	}

	.bp-help-content th:last-child {
		display: none;
	}
}

@media screen and (max-width: 550px) {

	.bp-help-card-grid {

		.bp-help-card {
			flex: 0 0 calc(100% - 20px);
		}
	}

}

/*------------------------------------------------------------------------------
 * 7.0 Plugins page
 *----------------------------------------------------------------------------*/
#buddypress-update.not-shiny .update-message {
	border-left: 0;
	padding-left: 36px;
}

#buddypress-update.not-shiny .update-message:before {
	content: "\f534";
}

/*------------------------------------------------------------------------------
 * 8.0 BuddyBoss App page
 *----------------------------------------------------------------------------*/
.bp_buddyboss_app_wrap {
	min-width: 255px;
	max-width: 1150px;
}

.bp_buddyboss_app_wrap .buddyboss-app-featured {
	margin: 20px 2px 0;
}

.bp_buddyboss_app_wrap .buddyboss-app-banner-block,
.bp_buddyboss_app_wrap .buddyboss-app-learndash-banner-block {
	background: #fff;
	margin: 0 0 20px;
	padding: 2em 2.8em;
	border-radius: 6px;
	border: 1px solid #e5e5e5;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.bp_buddyboss_app_wrap .buddyboss-app-banner-block p {
	margin: 0 0 20px;
}

.bp_buddyboss_app_wrap .buddyboss-app-banner-block-items {
	display: -webkit-box;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	flex-direction: row;
	flex-wrap: wrap;
	justify-content: space-around;
	margin: 0 -10px;
}

.bp_buddyboss_app_wrap .buddyboss-app-banner-block-item {
	-webkit-box-flex: 1;
	flex: 1;
	margin: 2em 1em;
	min-width: 200px;
	width: 30%;
}

.bp_buddyboss_app_wrap .buddyboss-app-column-block-item {
	border-top: 2px solid #f9f9f9;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	flex-direction: row;
	flex-wrap: wrap;
	-webkit-box-pack: justify;
	justify-content: space-between;
	margin: 0 -2em;
	padding: 20px 2em;
}

.bp_buddyboss_app_wrap .buddyboss-app-banner-block-item,
.bp_buddyboss_app_wrap .buddyboss-app-column-block-item {
	display: none;
}

.bp_buddyboss_app_wrap .buddyboss-app-column-block-item:nth-of-type(-n+3) {
	display: flex;
}

.bp_buddyboss_app_wrap .buddyboss-app-banner-block-item:nth-child(-n+3) {
	display: block;
}

.bp_buddyboss_app_wrap .buddyboss-app-banner-block-item-icon,
.bp_buddyboss_app_wrap .buddyboss-app-column-block-item-icon {
	-webkit-box-align: center;
	align-items: center;
	display: -webkit-box;
	display: flex;
	-webkit-box-pack: center;
	justify-content: center;
}

.bp_buddyboss_app_wrap .buddyboss-app-banner-block-item-icon {
	justify-content: left;
}

.bp_buddyboss_app_wrap .buddyboss-app-banner-block img {
	height: 62px;
}

.bp_buddyboss_app_wrap .buddyboss-app-banner-block-item-content {
	display: -webkit-box;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	flex-direction: column;
	-webkit-box-pack: justify;
	justify-content: space-between;
	padding: 24px 0 0;
}

.bp_buddyboss_app_wrap .buddyboss-app-banner-block-item-content h3 {
	margin-top: 0;
}

.bp_buddyboss_app_wrap .buddyboss-app-banner-block p {
	margin: 0 0 20px;
}

.bp_buddyboss_app_wrap .buddyboss-app-banner-block-item-content p {
	margin: 0 0 auto;
}

.bp_buddyboss_app_wrap .buddyboss-app-column-block-item-icon {
	background: #f7f7f7;
	border: 1px solid #e6e6e6;
	height: 100px;
	margin: 0 20px 0 0;
	width: 100px;
}

.bp_buddyboss_app_wrap .buddyboss-app-column-block img {
	max-height: 50px;
	max-width: 50px;
}

.bp_buddyboss_app_wrap .buddyboss-app-column-block-item-content {
	display: -webkit-box;
	display: flex;
	-webkit-box-flex: 1;
	flex: 1;
	flex-wrap: wrap;
	height: 20%;
	-webkit-box-pack: justify;
	justify-content: space-between;
	min-width: 200px;
}

.bp_buddyboss_app_wrap .buddyboss-app-column-block-item-content h2 {
	float: left;
	margin-top: 10px;
}

.bp_buddyboss_app_wrap .buddyboss-app-column-block-item-content p {
	float: left;
	margin-top: 0;
}

.bp_buddyboss_app_wrap .buddyboss-app-button {
	border-radius: 3px;
	cursor: pointer;
	display: block;
	height: 37px;
	line-height: 37px;
	text-align: center;
	text-decoration: none;
	background-color: #00aae1;
	color: #fff;
	width: 124px;
}

.bp_buddyboss_app_wrap .buddyboss-app-learndash-banner-block {
	display: -webkit-box;
	display: flex;
	-webkit-box-align: center;
	align-items: center;
	padding: 2em;
}

.bp_buddyboss_app_wrap .buddyboss-app-learndash-banner-block-image {
	margin-right: 2em;
	padding: 1em;
}

.bp_buddyboss_app_wrap .buddyboss-app-learndash-banner-block-image .buddyboss-app-img {
	max-height: 130px;
	max-width: 160px;
}

.bp_buddyboss_app_wrap .buddyboss-app-learndash-banner-block-content {
	display: -webkit-box;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	flex-direction: column;
	justify-content: space-around;
	align-self: stretch;
	padding: 1em 0;
}

.bp_buddyboss_app_wrap .buddyboss-app-learndash-banner-block-content h1 {
	padding-bottom: 0;
}

.bp_buddyboss_app_wrap .buddyboss-app-column-section {
	display: -webkit-box;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	flex-direction: row;
	flex-wrap: wrap;
	justify-content: space-around;
}

.bp_buddyboss_app_wrap .buddyboss-app-column {
	-webkit-box-flex: 1;
	flex: 1;
	min-width: 0;
	width: 50%;
	padding-right: 20px;
}

.bp_buddyboss_app_wrap .buddyboss-app-column:last-child {
	padding-right: 0;
}

.bp_buddyboss_app_wrap .buddyboss-app-column-block,
.bp_buddyboss_app_wrap .buddyboss-app-small-dark-block,
.bp_buddyboss_app_wrap .buddyboss-app-small-light-block {
	box-sizing: border-box;
	border-radius: 6px;
	border: 1px solid #e5e5e5;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
	margin: 0 0 20px;
	padding: 2em;
}

.bp_buddyboss_app_wrap .buddyboss-app-column-block,
.bp_buddyboss_app_wrap .buddyboss-app-small-light-block {
	background: #fff;
}

.bp_buddyboss_app_wrap .buddyboss-app-small-light-block {
	display: -webkit-box;
	display: flex;
	flex-wrap: wrap;
}

.bp_buddyboss_app_wrap .buddyboss-app-small-light-block img {
	height: 225px;
	margin: 0 2em 0 -2em;
}

.bp_buddyboss_app_wrap .buddyboss-app-small-light-block-content {
	display: -webkit-box;
	display: flex;
	-webkit-box-flex: 1;
	flex: 1 1 100px;
	min-width: 0;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	flex-direction: column;
	justify-content: space-around;
}

.bp_buddyboss_app_wrap .buddyboss-app-small-light-block h1 {
	margin-top: -12px;
}

.bp_buddyboss_app_wrap .buddyboss-app-small-light-block p {
	margin-top: 0;
}

.bp_buddyboss_app_wrap .buddyboss-app-small-light-block-buttons {
	display: -webkit-box;
	display: flex;
	-webkit-box-pack: justify;
	justify-content: space-between;
}

.bp_buddyboss_app_wrap .buddyboss-app-small-light-block-content a {
	width: 48%;
}

.bp_buddyboss_app_wrap .buddyboss-app-small-dark-block {
	background-color: #00aae1;
	text-align: center;
}

.bp_buddyboss_app_wrap .buddyboss-app-small-dark-block h1 {
	color: #fff;
}

.bp_buddyboss_app_wrap .buddyboss-app-small-dark-block p {
	color: #fafafa;
}

.bp_buddyboss_app_wrap .buddyboss-app-small-dark-block img {
	margin: 10px 0;
	max-width: 100%;
}

/*------------------------------------------------------------------------------
 * 9.0 Miscellaneous
 *----------------------------------------------------------------------------*/
body.post-type-bp_ps_form .page-title-action {
	display: none;
}

/* Notices */
.buddypress .wrap div#message,
.buddypress .wrap div.notice {
	margin: 25px 0 15px;
}

tr.child-no-padding th {
	padding: 0 10px 0 0;
}

tr.child-no-padding td {
	padding: 5px 10px;
}

tr.child-no-padding-first th {
	padding: 20px 10px 0 0;
}

tr.child-no-padding-first td {
	padding: 15px 10px 5px;
}

@media screen and (max-width: 782px) {

	.form-table {

		tr.child-no-padding,
		tr.child-no-padding-first {

			td {
				padding-left: 0;
			}
		}

	}
}

td.inviter.column-inviter strong img {
	float: left;
	margin-right: 10px;
	margin-top: 1px;
}

td label.platform-activity {
	clear: left;
	display: block;
	line-height: 1.5;
	margin: 0 0 1em;
}

/* Customize tabs */
.nav-tab-wrapper {

	.bp-user-customizer,
	.bp-group-customizer,
	.bp-emails-customizer {
		padding-right: 6px;
	}

	.bp-user-customizer:after,
	.bp-group-customizer:after,
	.bp-emails-customizer:after {
		font-family: dashicons;
		content: "\f345";
		margin-left: 3px;
		display: inline-block;
		vertical-align: top;
	}
}

.bb-nickname-hide-last-name {
	margin-top: -21px;

	input[type="checkbox"] {
		margin-top: 0;
	}
}

.bb-nickname-hide-first-name {
	margin-top: -8px;

	input[type="checkbox"] {
		margin-top: 0;
	}
}

.loader-repair-tools {
	border: 4px solid #f3f3f3;
	border-top: 4px solid #3498db;
	border-right: 4px solid #008000;
	border-left: 4px solid #f00;
	border-bottom: 4px solid #ffc0cb;
	border-radius: 50%;
	width: 10px;
	height: 10px;
	animation: spin 2s linear infinite;
	display: inline-block;
	margin-left: 8px;
	vertical-align: middle;
}

@keyframes spin {

	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

body .section-repair_community .settings fieldset .submit a.disable-btn {
	pointer-events: none;
}

body .section-repair_community .settings fieldset .checkbox label code {
	margin-left: 10px;
}

@media screen and (max-width: 782px) {

	.bb-nickname-hide-last-name {
		margin-top: -2px;
	}

	.bb-nickname-hide-first-name {
		margin-top: 0;
	}
}

.animate-spin {
	animation: spin 2s infinite linear;
	display: inline-block;
}

@-moz-keyframes spin {

	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(359deg);
	}
}

@-webkit-keyframes spin {

	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(359deg);
	}
}

@-o-keyframes spin {

	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(359deg);
	}
}

@-ms-keyframes spin {

	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(359deg);
	}
}

@keyframes spin {

	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(359deg);
	}
}

.wp-block-embed {
	margin: 0;

	iframe {
		max-width: 100%;
	}
}

#bp-help-content-area.loading,
#bp-help-main-menu-wrap.loading,
.bp-help-sidebar.loading {
	display: flex;
	justify-content: center;
	align-items: center;
}

.content-loader {
	display: inline-block;
	position: relative;
	width: 80px;
	height: 80px;

	div {
		display: inline-block;
		position: absolute;
		left: 5px;
		width: 9px;
		background: #dadada;
		animation: contentLoader 1.2s cubic-bezier(0, 0.5, 0.5, 1) infinite;

		&:nth-child(1) {
			left: 8px;
			animation-delay: -0.24s;
		}

		&:nth-child(2) {
			left: 28px;
			animation-delay: -0.12s;
		}

		&:nth-child(3) {
			left: 48px;
			animation-delay: 0;
		}
	}
}

#the-moderation-request-list {

	.blocked_member > img,
	.content_owner > img {
		vertical-align: middle;
		margin-right: 5px;
	}

	[data-colname] a {
		outline: 0;
		box-shadow: none;

		img {
			display: inline-block;
			vertical-align: middle;
		}

	}

	.content_type strong {
		color: #1d2327;

		> span {
			color: #808080;
		}
	}

}

@keyframes contentLoader {

	0% {
		top: 8px;
		height: 50px;
	}

	50%,
	100% {
		top: 24px;
		height: 28px;
	}
}

/*Moderation Reporting Block*/
#bp_moderation_settings_reporting label.bpm_reporting_content_content_label {
	min-width: 27%;
}

#bp_moderation_settings_reporting label[for="bpm_reporting_content_reporting"] {
	margin-top: 5px;
	display: block;
}

#bp_moderation_settings_reporting .small-text {
	width: 48px;
	min-height: 24px;
	height: 24px;
}

#bp_moderation_settings_reporting label.bpm_reporting_content_content_label,
#bp_moderation_settings_reporting label.bpm_reporting_content_content_label + label {
	display: inline-block;
	margin-bottom: 30px;
}

#bp_moderation_settings_blocking label.is_disabled,
#bp_moderation_settings_reporting label.is_disabled,
#bp_moderation_settings_reporting label.bpm_reporting_content_content_label + label.is_disabled {
	pointer-events: none;
	opacity: 0.5;

	input[type="checkbox"]:disabled {
		opacity: 1;
		border-color: inherit;
	}

}


/*Deactivation Popup*/
#bp-hello-container.deactivation-popup,
#bb-hello-container.deactivation-popup {
	top: 50%;
	bottom: initial;
	transform: translateY(-50%);
	overflow: auto;
	max-height: 90%;
	max-width: 640px;
	margin: 0 auto;

	.bp-hello-footer,
	.bb-hello-footer {
		position: static;
		padding: 10px 23px;

		button + button {
			margin-left: 10px;
		}

	}

	.bp-hello-content,
	.bb-hello-content {
		height: auto;
		padding: 20px 23px;

		h4 {
			margin-bottom: 0;
		}

		ul {
			margin-left: 17px;
			list-style-type: disc;
		}

		> p:first-child {
			margin-top: 0;
		}

	}

}

#bp_moderation_action td a {
	text-decoration: none;
}

.row-actions .suspend a,
.row-actions .suspend a:hover {
	color: #dc3232;
}

.users tr.suspended-user {
	background-color: #f5dada;
}

h2.has_tutorial_btn {
	position: relative;

	.bbapp-tutorial-btn {
		position: absolute;
		right: 0;
		left: initial;
		top: 5px;
		font-weight: 400;

		p {
			margin: 0;
		}

		.bb-button_filled {
			color: #fff;
			background: #2271b1;

			&:hover {
				background: #135e96;
				border-color: transparent;
			}
		}

	}

	@media screen and (max-width: 580px) {
		margin-bottom: 65px;

		.bbapp-tutorial-btn {
			top: inherit;
			bottom: -55px;
		}

	}

}


/*------------------------------------------------------------------------------
 * 10.0 On-screen Notifications
 *----------------------------------------------------------------------------*/

.bb-screen-position-outer {
	display: flex;
	width: 180px;
	justify-content: space-between;

	.bb-screen-position {
		display: flex;
		width: 85px;
		flex-direction: column;
		text-align: center;

		input[type="radio"] {
			opacity: 0;
			visibility: hidden;
			height: 0;
		}

		.option {
			position: relative;
			display: flex;
			flex-direction: column;

			&:before {
				content: " ";
				height: 80px;
				width: 80px;
				background: #f0f0f1;
				display: flex;
				border-radius: 5px;
				border: 2px solid #ccd0d4;
				transition: all 0.3s ease;
			}

			&:after {
				content: " ";
				width: 40px;
				height: 20px;
				border-radius: 2px;
				background: #c3c4c7;
				position: absolute;
				top: 55px;
			}

			&.opt-right {

				&:after {
					right: 8px;
				}

			}

			&.opt-left {

				&:after {
					left: 8px;
				}

			}

			span {
				font-size: 14px;
				color: #999;
				line-height: 2;
			}

		}

		#bp_on_screen_notifications_position_left:checked:checked + .option,
		#bp_on_screen_notifications_position_right:checked:checked + .option {

			&:before {
				border-color: #2370b1;
			}

		}
	}
}

/*------------------------------------------------------------------------------
 * 11.0 Notices UI
 *----------------------------------------------------------------------------*/

.bp-messages-feedback .bp-feedback.error {
	margin: 0;
	border: 0;
	padding: 0;
}

#bp_web_push_notification_settings,
#bp_media_settings_symlinks {

	.bp-feedback {
		box-shadow: none;
		padding-top: 0;
	}

	.bp-feedback .bp-icon {
		border-radius: 4px 0 0 4px;
		margin: 0;
	}

	.bp-feedback > p {
		color: #666 !important;
		margin-top: 0 !important;
		padding: 10px 8px;
		border: 1px solid #ccc;
		border-radius: 0 4px 4px 0;
		border-left: 0;
		width: 100%;
	}
}

.admin-notice .bp-feedback,
#bp_web_push_notification_settings .bp-feedback,
#bp_media_settings_symlinks .bp-feedback {
	box-shadow: none;
	padding-top: 0;
}

.admin-notice .bp-feedback .bp-icon,
#bp_web_push_notification_settings .bp-feedback .bp-icon,
#bp_media_settings_symlinks .bp-feedback .bp-icon {
	border-radius: 4px 0 0 4px;
	margin: 0;
}

.admin-notice .bp-feedback > p,
#bp_web_push_notification_settings .bp-feedback > p,
#bp_media_settings_symlinks .bp-feedback > p {
	color: #666 !important;
	margin-top: 0 !important;
	padding: 10px;
	border: 1px solid #ccc;
	border-radius: 0 4px 4px 0;
	border-left: 0;
	width: 100%;
}

.bp-messages-feedback .bp-feedback.warning .bp-icon {
	background-color: #f7ba45;

	&:before {
		font-family: "bb-icons"; /* stylelint-disable-line */
		content: "\e855";
	}

}

/* Errors / Waring Styling*/
.bb-error-section {
	padding: 10px;
	margin-left: -210px;
	background: rgb(255, 220, 220);
	color: rgb(212, 41, 41);
	line-height: 1.5;
	border: 1px solid rgb(255, 197, 202);
	border-radius: 4px;

	@media screen and (max-width: 782px) {
		margin-left: 0;
	}
}

.bb-warning-section-wrap.hidden-header > th {
	opacity: 0;
	visibility: hidden;
	padding: 0;
	margin: 0;

	+ td {
		padding-left: 0;
		padding-right: 0;
	}
}

.bb-warning-section {
	padding: 10px;
	margin-left: -210px;
	background: #fff3c8;
	color: #8b6300;
	line-height: 1.5;
	border: 1px solid #ffeeb3;
	border-radius: 4px;

	@media screen and (max-width: 782px) {
		margin-left: 0;
	}
}

/* Status Block */
.status-line {
	display: inline-block;
	font-size: 14px;
	padding: 10px 15px;
	background: #f0f0f1;
	border-radius: 5px;
	color: #808080;

	&:before {
		content: "";
		display: inline-block;
		width: 10px;
		height: 10px;
		border-radius: 100%;
		background: #808080;
		margin-right: 10px;
		vertical-align: baseline;
	}

	&.error-connected {
		color: #ce1010;

		&:before {
			background: #ce1010;
		}
	}

	&.connected {
		color: #858585;

		&:before {
			background: #8fd14f;
		}
	}

	&.warn-connected {
		color: #858585;

		&:before {
			background: #fac710;
		}
	}

}

/*------------------------------------------------------------------------------
 * 12.0 Profile Photo UI
 *----------------------------------------------------------------------------*/

.buddyboss_page_bp-settings {

	.bp-avatar-nav {
		display: none;
	}

}

.avatar-options {

	.avatar-custom-input {
		display: inline-block;
		margin: 0 7px 0 0;

		+ p.description {
			margin-top: 15px;
		}

		> label {
			display: inline-block;
			vertical-align: top;
		}

		input[type="radio"] {
			opacity: 0;
			visibility: hidden;
			height: 0;
			margin: 0;
			padding: 0 !important;
			border: 0;
			display: block;

			&:checked + label .img-block img {
				border-color: #2271b1;
				box-shadow: 0 0 0 1px #2271b1;
			}

			&:checked:before {
				position: absolute;
			}

		}

		.img-block {
			display: flex;
			border-radius: 5px;
			margin-bottom: 5px;
		}

		img {
			width: 80px;
			height: 80px;
			border: 1px solid #ccd0d4;
			border-radius: 5px;
			object-fit: cover;
		}

		span {
			display: block;
			text-align: center;
			color: #999;
		}

		.description {
			margin-top: 15px;
		}

	}

	.bb-default-custom-upload-file {

		.bb-upload-container img {
			margin-bottom: 10px;
			max-width: 120px;
		}

		.button.delete {
			border-color: #d33;
			color: #d33;
			margin-left: 5px;

			&.bp-hide {
				display: none;
			}
		}

		&.cover-uploader .bb-upload-container img {
			max-width: 300px;
			width: 100%;
		}

	}

	.cover-uploader-label {
		position: relative;
		display: inline-block;
		overflow: hidden;
		cursor: pointer;

		.cover-uploader-hide {
			border: 0;
			opacity: 0;
			position: absolute;
			right: 0;
			top: 0;
			bottom: 0;
		}
	}

	.image-width-height {

		> label {
			min-width: 80px;
			display: inline-block;
		}

		.description {
			margin-left: 85px;
			margin-bottom: 15px;
		}

		@media screen and (max-width: 782px) {

			> label {
				min-width: auto;
				margin-bottom: 10px;
			}

			.description {
				margin-left: 0;
			}

		}

	}

	.no-field-notice {

		.bp-admin-card & {
			margin: 15px 0;
			padding: 10px;
		}
	}

}

.buddyboss_page_bp-settings #TB_window #TB_ajaxContent .bp-avatar-status .bp-uploader-progress div.error {
	margin: 5px 0 2px;
}

.buddyboss_page_bp-settings #TB_window #TB_ajaxContent .bp-avatar-status .bp-progress {
	margin-right: 0;
}

.preview_avatar_cover {

	.preview-switcher {
		margin-bottom: 25px;

		.button:focus {
			box-shadow: none;
		}
	}

	.preview-block {
		max-width: 260px;
		border: 1px solid #ccd0d4;
		border-radius: 5px;
		overflow: hidden;
		margin-bottom: 25px;
		display: none;

		&.active {
			display: block;
		}

	}

	.app-preview-wrap {
		max-width: 200px;

		.preview-item-avatar {
			left: 50%;
			border-radius: 50%;
			-webkit-transform: translateX(-50%);
			-ms-transform: translateX(-50%);
			transform: translateX(-50%);
			overflow: hidden;
		}

	}

	.preview-item-cover {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: stretch;
		-ms-flex-align: stretch;
		align-items: stretch;
		min-height: 80px;
		max-height: 80px;
		background-color: #ccc;
		overflow: hidden;

		&.large-image {
			min-height: 130px;
			max-height: 130px;
		}
	}

	.preview-item-avatar {
		height: 75px;
		width: 75px;
		position: relative;
		top: -35px;
		left: 25px;
		border: 3px solid #fff;
		border-radius: 5px;

		&.centered-image {
			left: 50%;
			transform: translateX(-50%);
		}

		img {
			background-color: #f9f9f9;
		}

	}

	.preview-item-cover img,
	.preview-item-avatar img {
		object-fit: cover;
		width: 100%;
	}

	.preview-item-cover img[src=""],
	.preview-item-avatar img[src=""] {
		display: none;
	}

	&:not(.has-avatar) .preview-item-avatar {
		display: none;
	}

	&:not(.has-cover) {

		.preview-item-cover {
			display: none;
		}

		.preview-item-avatar {
			top: 0;
			left: 50%;
			-webkit-transform: translateX(-50%);
			-ms-transform: translateX(-50%);
			transform: translateX(-50%);
		}

	}

	&:not(.has-cover):not(.has-avatar) {
		display: none;
	}

	.button-group > .button:last-child:first-child {
		border-radius: 3px;
	}

}

.profile-cover-options .preview_avatar_cover .web-preview-wrap .preview-item-avatar {
	border-radius: 50%;

	img {
		border-radius: 50%;
	}

}

.group-cover-options .preview_avatar_cover .app-preview-wrap {
	position: relative;

	.preview-item-cover {
		min-height: 120px;
		max-height: 120px;
		margin-bottom: 45px;
	}

	.preview-item-avatar {
		top: 25px;
		position: absolute;
		border-radius: 12px;
		border: 0;
		height: 70px;
		width: 70px;
	}

}

body.users_page_bp-profile-edit,
.buddyboss_page_bp-settings {

	#TB_window {
		height: auto !important;
		width: 820px !important;
		max-width: 90%;
		top: 50% !important;
		transform: translateY(-50%);
		max-height: 90%;
		overflow: auto;

		.bp-avatar #avatar-to-crop {
			margin-bottom: 15px;
		}
	}

	#TB_ajaxContent {
		height: auto !important;
		width: auto !important;
		padding: 15px;
	}
}

table tbody td .bb-description {
	color: #999;
	margin-top: 6px;
	margin-bottom: 0;
}

/*------------------------------------------------------------------------------
 * 13.0 Group Directories Enhancement
 *----------------------------------------------------------------------------*/
.bb-group-element,
.bb-group-headers-element,
.bb-profile-header-element,
.bb-member-directory-element,
.bb-member-directory-profile-action {
	margin-bottom: 10px;

	@media screen and (max-width: 782px) {

		input[type="checkbox"] {
			width: auto;
		}

	}

}

.bb-header-style-outer {
	display: flex;

	.bb-header-style {
		display: flex;
		flex-direction: column;
		margin-right: 15px;
		text-align: center;
		width: 110px;

		input[type="radio"] {
			height: 0;
			opacity: 0;
			visibility: hidden;
		}

		.option {
			display: flex;
			color: #999;
			flex-direction: column;
			position: relative;

			&:before {
				background: #f9f9f9;
				border: 2px solid #ccd0d4;
				border-radius: 4px;
				box-sizing: border-box;
				content: "";
				display: flex;
				height: 110px;
				margin-bottom: 5px;
				transition: all 0.3s ease;
				width: 100%;
			}

			&:after {
				background-size: cover !important;
				content: "";
				border-radius: 3px;
				bottom: 30px;
				left: 6px;
				position: absolute;
				right: 6px;
				top: 6px;
				image-rendering: -webkit-optimize-contrast;
			}

			&.opt-left:after {
				background: url(../../images/bb-group-header-left.png) no-repeat center;

				html[dir="rtl"] & {
					background: url(../../images/bb-group-header-right.png) no-repeat center;
				}

			}

			&.opt-centered:after {
				background: url(../../images/bb-group-header-center.png) no-repeat center;
			}

			span {
				color: #999;
				font-size: 14px;
				line-height: 2;
			}

		}

		#bb-group-header-style-left:checked:checked + .option,
		#bb-group-header-style-centered:checked:checked + .option {

			&:before {
				border-color: #2370b1;
			}
		}
	}
}

.bb-grid-style-outer {
	display: flex;

	.bb-grid-style {
		display: flex;
		flex-direction: column;
		margin-right: 15px;
		text-align: center;
		width: 110px;

		input[type="radio"] {
			height: 0;
			opacity: 0;
			visibility: hidden;
		}

		.option {
			display: flex;
			color: #999;
			flex-direction: column;
			position: relative;

			&:before {
				background: #f9f9f9;
				border: 2px solid #ccd0d4;
				border-radius: 4px;
				box-sizing: border-box;
				content: "";
				display: flex;
				height: 110px;
				margin-bottom: 5px;
				transition: all 0.3s ease;
				width: 100%;
			}

			&:after {
				background-size: cover !important;
				content: "";
				border-radius: 3px;
				bottom: 30px;
				left: 6px;
				position: absolute;
				right: 6px;
				top: 6px;
				image-rendering: -webkit-optimize-contrast;
			}

			&.opt-left:after {
				background: url(../../images/bb-group-directory-left.png) no-repeat center;

				html[dir="rtl"] & {
					background: url(../../images/bb-group-directory-right.png) no-repeat center;
				}

			}

			&.opt-centered:after {
				background: url(../../images/bb-group-directory-center.png) no-repeat center;
			}

			span {
				color: #999;
				font-size: 14px;
				line-height: 2;
			}

		}

		#bb-group-directory-layout-grid-style-left:checked:checked + .option,
		#bb-group-directory-layout-grid-style-centered:checked:checked + .option {

			&:before {
				border-color: #2370b1;
			}

		}

	}

	.bb-header-style {
		display: flex;
		flex-direction: column;
		margin-right: 15px;
		text-align: center;
		width: 110px;

		input[type="radio"] {
			height: 0;
			opacity: 0;
			visibility: hidden;
		}

		.option {
			display: flex;
			color: #999;
			flex-direction: column;
			position: relative;

			&:before {
				background: #f9f9f9;
				border: 2px solid #ccd0d4;
				border-radius: 4px;
				box-sizing: border-box;
				content: "";
				display: flex;
				height: 110px;
				margin-bottom: 5px;
				transition: all 0.3s ease;
				width: 100%;
			}

			&:after {
				background-size: cover !important;
				content: "";
				border-radius: 3px;
				bottom: 30px;
				left: 6px;
				position: absolute;
				right: 6px;
				top: 6px;
				image-rendering: -webkit-optimize-contrast;
			}

			&.opt-left:after {
				background: url(../../images/bb-profile-header-left.png) no-repeat center;

				html[dir="rtl"] & {
					background: url(../../images/bb-profile-header-right.png) no-repeat center;
				}

			}

			&.opt-centered:after {
				background: url(../../images/bb-profile-header-center.png) no-repeat center;
			}

			span {
				color: #999;
				font-size: 14px;
				line-height: 2;
			}

		}

		#bb-profile-headers-layout-styleleft:checked:checked + .option,
		#bb-profile-headers-layout-stylecentered:checked:checked + .option {

			&:before {
				border-color: #2370b1;
			}

		}

	}

}

/*------------------------------------------------------------------------------
 * 14.0 BB Pro Active/Inactive Notices
 *----------------------------------------------------------------------------*/

.bb-head-notice {
	background-color: #f6f6f6;
	border-radius: 4px;
	color: #999;
	display: inline-block;
	font-size: 12px;
	font-weight: 400;
	line-height: 1.5;
	margin-top: 10px;
	max-width: 140px;
	padding: 5px 10px;
	width: auto;

	a {
		color: inherit;
		font-weight: 700;
		text-decoration: none;
	}

}

.bb-inactive-field,
.bb-pro-inactive {

	> th:before {
		content: "\eecc";
		font-family: "bb-icons";/* stylelint-disable-line */
		margin-right: 5px;
		vertical-align: top;
		display: inline-block;
		line-height: 1;
		font-weight: 300;
		font-size: 15px;
	}

	> td {
		opacity: 0.3;
	}

	> td,
	> td * {
		pointer-events: none;
		user-select: none;
	}

}

.bb-active-field,
.bb-pro-active {

	> th:before {
		display: none;
	}

	.bb-head-notice {
		display: none;
	}

}

// missing email button UI
.bp-hello-email .bp-hello-title .count,
.btn-open-missing-email .count {
	display: inline-block;
	background-color: #2271b1;
	color: #fff;
	padding: 1px 6px;
	border-radius: 4px;
	line-height: 1.3;
	font-size: smaller;
}

.btn-open-missing-email + #bp-hello-backdrop {
	display: block;
}

// missing email popup UI
.bp-hello-email {

	.bp-hello-title .count {
		font-size: inherit;
		padding: 2px 10px;
		margin-right: 10px;
	}

	.bp-hello-content {
		height: auto;
		padding: 0 23px;
		min-height: 180px;
		max-height: calc(100vh - 320px);
		margin-bottom: 62px;
	}

	.missing-email-list ul {
		list-style: disc;
		margin: 25px 0 25px 20px;
	}

	.bb-popup-buttons {
		position: absolute;
		bottom: 0;
		padding: 15px;
		text-align: right;
		width: calc(100% - 30px);
		margin: 0 -23px;
		border-top: 1px solid var(--bp-hello-color-secondary);
		background-color: #f0f0f1;
	}

	@media screen and (orientation: landscape) and (max-height: 480px) {

		.bp-hello-content {
			min-height: 110px;
		}

	}

}

#bp-hello-container.bp-hello-email {
	top: 50%;
	bottom: initial;
	transform: translateY(-50%);
	margin-top: 32px;

	.button-primary {
		color: #fff;
		margin-left: 10px;

		@media screen and (max-width: 480px) {
			margin-left: 0;
		}

	}

}

#bp_notification_settings_automatic {

	table.render-dynamic-notification,
	table.render-dynamic-notification tbody,
	table.render-dynamic-notification tr,
	table.render-dynamic-notification th,
	table.render-dynamic-notification td {
		display: block;
	}

	table.form-table {

		.notes-hidden-header {

			> th {
				display: none;

				+ td {
					padding-left: 0;
					padding-right: 0;
				}
			}
		}

		.no-padding {
			padding: 0;
		}

		tbody tr {
			margin-bottom: 20px;

			th {
				margin-bottom: 15px;
				width: 100%;
			}

		}
	}

	.field-set {
		margin-top: 10px;
		background-color: #f6f6f8;
		border: 1px solid #ccd0d4;
		border-radius: 3px;

		.field-block {
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			align-items: flex-start;
			padding: 15px;

			+ .field-block {
				border-top: 1px solid #ccd0d4;
			}

			.no-email-info {
				margin-right: 15px;
				margin-left: auto;
				background-color: #ffdcdc;
				border-radius: 5px;
				padding: 5px 10px;
				color: #8c0202;
				font-size: 11px;
				text-decoration: none;
			}

			.email-info {
				margin-right: 15px;
				margin-left: auto;
				border-radius: 5px;
				padding: 5px 10px;
				font-size: 11px;
				text-decoration: none;
				background-color: rgb(223, 229, 234);
			}

			.notification-defaults {
				padding: 3px 0;
				cursor: pointer;
			}

			input[type="checkbox"] {
				margin-top: 0;
			}

			.manage-defaults {
				margin-top: 15px;
				justify-content: flex-end;
				width: 100%;
				display: flex;

				.field-wrap {
					margin-left: 15px;
					min-width: 58px;
					text-align: center;

					&.disabled {
						pointer-events: none;

						input[type="checkbox"] {
							opacity: 0.7;
							background: rgba(255, 255, 255, 0.5);
							border-color: rgba(220, 220, 222, 0.75);
							color: rgba(44, 51, 56, 0.5);

							&::before {
								opacity: 0.7;
							}
						}
					}
				}
			}

			.manage-defaults-hide {
				display: none;
			}
		}
	}
}

#bp_notification_settings_automatic table.form-table.dynamic-notification-after tbody tr {

	th {
		min-width: 200px;
		padding: 15px 20px 0 0;
		width: auto;
	}

	td {
		padding: 15px 10px 15px 0;
	}

}

.bb-bottom-notice {
	margin: 2.2em -2.8em -2.2em;
	background: #f6f6f8;
	padding: 2.2em 2.8em;
	border-radius: 0 0 5px 5px;
	border-top: 1px solid #ccd0d4;
}

.bp-admin-card .form-table .bb-lab-notice {
	background: #f6f6f8;
	padding: 1.2em;
	margin-top: 1.2em;
	border-radius: 3px;
	border: 1px solid #ccd0d4;
}

.bp-admin-card .form-table .bp-new-notice-panel-notice {
	color: #8b6300;
	background-color: #fff3c8;
	padding: 1.2em;
	font-size: 14px;
	line-height: 1.5;
	border-radius: 3px;
	margin: 20px 0;

	a {
		color: inherit;
	}

	+ p.description {
		margin-bottom: 15px;
	}

}

.bb-lab-notice.notification-information {

	&:before {
		content: "\eebc";
		display: inline-block;
		font-family: "bb-icons"; /* stylelint-disable-line */
		font-size: 16px;
		font-weight: 100;
		margin-right: 10px;
		line-height: 1;
	}

	&:first-child {
		margin-top: 0;
	}

}

#bp_messaging_notification_settings {

	table.form-table {

		.notes-hidden-header {

			> th {
				display: none;

				+ td {
					padding-left: 0;
					padding-right: 0;
				}
			}
		}

		.no-padding {
			padding: 0;
		}
	}
}

/*------------------------------------------------------------------------------
 * 15.0 Admin bar Notification
 *----------------------------------------------------------------------------*/

#wpadminbar #wp-admin-bar-bp-notifications-default {
	width: 300px;
	max-height: calc(90vh - 50px);
	overflow: auto;

	> li {
		display: inline-block;
		width: 100%;
		padding: 5px 0;

		&#wp-admin-bar-notification-view-all {
			text-align: center;
		}

	}

	.notification-avatar {
		float: left;
		width: 40px;
		margin-top: 4px;

		.member-status {
			display: none;
		}

		> a {
			height: auto;
			padding: 0;

			img {
				max-width: 36px;
			}

			> i {
				display: none;
			}

		}

	}

	.notification-content {
		float: right;
		width: calc(100% - 60px);

		.bb-full-link > a {
			opacity: 1;
			padding: 0;
			height: auto;
			position: static;
			white-space: normal;
			line-height: 1.5;
		}

		.posted {
			line-height: 1.4;
		}

	}

}

/*------------------------------------------------------------------------------
 * 16.0 Moderation
 *----------------------------------------------------------------------------*/

table.moderations {

	.column-content_type,
	.column-member {

		a[data-action="unhide"],
		a[data-action="unsuspend"] {
			color: #5f9a22;
		}
	}

	thead tr th,
	tfoot tr th {
		font-weight: 600;
	}

	tbody {

		tr {

			.column-suspend {

				.dashicons {
					color: #000;
					font-size: 24px;
				}

			}

		}

	}

	.bp-block-user {

		&.disabled {
			cursor: default;
		}

		&[data-bp-tooltip] {
			display: inline-block;

			&:after {
				white-space: normal;
				width: 210px;
				text-align: center;
			}
		}
	}

	&.wp-list-table tr:not(.inline-edit-row):not(.no-items) td:not(.column-primary):before {
		display: none;
	}
}

.buddyboss_page_bp-moderation {

	.dashicons {
		font-weight: 700;
	}

	.bb-back {
		display: inline-block;
		margin-top: 15px;
		text-decoration: none;

		[class*="bb-icon"] {
			display: inline-block;
			font-size: 17px;
			margin: -1px 3px 0 0;
			vertical-align: middle;
		}

	}

	table.reports {

		thead tr th,
		tfoot tr th {
			font-weight: 600;
		}
	}
}

table.moderations .content_owner,
table.reports .column-reporter,
table.moderations .column-member {

	img {
		vertical-align: middle;
		margin-right: 5px;
		border-radius: 50%;
	}

}

#bp_moderation_action {

	> .inside {
		margin: 0;
		padding: 0 20px;

		.tablenav.bottom {
			height: auto;
			min-height: 20px;
			margin: 0;
			padding-top: 0;

			.tablenav-pages {
				margin: 20px 0;
			}

		}

	}

	.report-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid #dfdfdf;
		margin: 0 -20px;
		padding: 20px;
	}

	.report-header_content_id {
		font-size: 20px;
		font-weight: 600;
		color: #1d2327;
		margin-bottom: 10px;

		span {
			color: #808080;
			margin-left: 5px;
		}

		a {
			text-decoration: none;

			i {
				font-weight: 300;
				font-size: 18px;
				transform: translateY(-1px);
				display: inline-block;
			}
		}
	}

	.report-header_user {
		display: flex;
		align-items: center;

		strong a {
			text-decoration: none;
			font-size: 16px;
			box-shadow: none;
		}

		img {
			border-radius: 50%;
			max-width: 35px;
			height: auto;
			margin-right: 10px;
			vertical-align: middle;
		}
	}

	.report-header_content {
		text-align: center;
		font-weight: 600;

		strong {
			display: block;
			font-size: 24px;
		}
	}

	.report-header_action {

		.report-header_button {
			color: #b32d2e;
			border-color: #b32d2e;

			&.green {
				color: #5f9a22;
				border-color: #afcc90;
			}

			+ .report-header_button {
				margin-left: 10px;
			}

		}

	}
}

.moderation-table-heading.form-table td {
	padding: 15px 0;
}

#the-moderation-report-list .reporter a {
	box-shadow: none;
}

.taxonomy-bpm_category .form-field.term-slug-wrap {
	display: none;
}

#bp_labs_settings,
#bp_web_push_notification_settings {

	table.form-table {

		.notes-hidden-header {

			> th {
				display: none;

				+ td {
					padding-left: 0;
					padding-right: 0;
				}
			}
		}

		.no-padding {
			padding: 0;
		}
	}
}

.no-field-notice {
	background-color: #f6f6f8;
	border: 1px solid #ccd0d4;
	border-radius: 3px;
	padding: 15px;
}

.notification-settings-input {

	&[data-bp-tooltip] {
		display: inline-block;

		&::after {
			min-width: 140px;
			white-space: normal;
			padding: 7px 12px;
			text-align: center;
		}
	}
}

.bp-admin-card {

	.password-toggle {

		position: relative;
		display: inline-block;
		width: 50%;

		> input {
			padding-right: 30px;
			width: 100% !important;
		}

		.bb-hide-pw {
			position: absolute;
			right: 0;
			top: 0;
			height: 100%;
			width: 30px;
			border: 0;
			padding: 0;
			background: none !important;
			box-shadow: none;

			.bb-icon {
				font-size: 16px;
				line-height: 1;
			}
		}

		.bb-hide-pw.bb-show-pass .bb-icon::before {
			content: "\ee6a";
		}
	}
}

table.users {

	.suspend,
	.unsuspend {

		a.disabled {
			cursor: default;
		}

		a.disabled[data-bp-tooltip] {
			display: inline-block;

			&:after {
				white-space: normal;
				width: 210px;
				text-align: center;
				z-index: 9999;
			}
		}
	}
}

/*------------------------------------------------------------------------------
 * 17.0 Domain restriction
 *----------------------------------------------------------------------------*/

.registration-restrictions-rule-list {
	margin: 15px 0;
	max-width: 560px;

	&.bb-sortable .registration-restrictions-rule {
		padding-left: 40px;

		&:before {
			cursor: grab;
			content: "\e858";
			font-family: "bb-icons";/* stylelint-disable-line */
			font-weight: 400;
			font-size: 22px;
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			left: 10px;
		}

		&.ui-sortable-helper:before {
			cursor: grabbing;
		}
	}

	.registration-restrictions-rule {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		gap: 15px;
		background-color: #f6f7f7;
		border: 1px solid #c3c4c7;
		position: relative;
		border-radius: 4px;
		padding: 15px 40px 15px 15px;
		margin-bottom: 15px;

		&.error {
			background-color: rgba(255, 0, 0, 0.1);
			border-color: #ffc5ca;
		}

		input,
		select {
			width: 100%;
		}

		> div {
			flex: 1 0 130px;

			&.registration-restrictions-input-tld {
				flex-basis: 80px;
				display: flex;
				align-items: baseline;
				margin-left: -5px;

				&:before {
					content: ".";
					margin-right: 5px;
				}
			}
		}

		.registration-restrictions-priority {
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			left: 15px;
		}

		.registration-restrictions-remove {
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			right: 10px;

			button {
				color: #3c434a;
				font-size: 20px;
				cursor: pointer;
				border: 0;
				background-color: transparent;
				padding: 0;
			}
		}
	}
}

.section-bb_registration_restrictions .registration-restrictions-listing {
	position: relative;

	.restrictions-error {
		position: absolute;
		right: 0;
		top: 0;
		font-size: 13px;
		max-width: 200px;
		min-width: 200px;
		padding: 10px 12px;
		border-radius: 4px;
		color: #d42929;
		border: 1px solid #ffc5ca;
		background: #ffdcdc;

		p {
			color: inherit;
			margin: 0 0 5px 0;

			&:last-child {
				margin-bottom: 0;
			}
		}

		&:empty {
			display: none;
		}
	}

	@media screen and (max-width: 1320px) {

		.restrictions-error {
			position: static;
			max-width: inherit;
			margin: 20px 0;
		}

	}

}


/*------------------------------------------------------------------------------
 * 18.0 Redirection Settings
 *----------------------------------------------------------------------------*/

.custom-select2 {
	font-size: 14px;
	line-height: 2;
	color: #2c3338;
	border: 1px solid #8c8f94;
	box-shadow: none;
	border-radius: 3px;
	padding: 0 24px 0 8px;
	min-height: 30px;
	max-width: 25rem;
	-webkit-appearance: none;
	background: #fff url(data:image/svg+xml;charset=US-ASCII,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M5%206l5%205%205-5%202%201-7%207-7-7%202-1z%22%20fill%3D%22%23555%22%2F%3E%3C%2Fsvg%3E) no-repeat right 5px top 55%;
	background-size: 16px 16px;
	cursor: pointer;
	vertical-align: middle;

	.select2-selection__arrow {
		display: none;
	}

	.select2-container--open & {
		border-radius: 3px 3px 0 0;
	}
}

.custom-dropdown-select2 {

	.select2-search__field {
		min-height: 25px;
		line-height: 1;
	}

	.select2-results__options {
		max-height: 240px;
		overflow: auto;

		li {
			padding: 6px 10px;

			&.select2-results__option--highlighted {
				background-color: rgba(170, 170, 170, 0.5);
				color: inherit;
			}
		}
	}
}

/*------------------------------------------------------------------------------
 * 19.0 Reactions Settings
 *----------------------------------------------------------------------------*/

.section-bp_reaction_settings_section {

	> h2 {
		width: 100%;
	}

	.bb-reactions-setting-field {
		margin-top: 8px;
	}

	label[for="bb_reaction_mode_likes"] {
		margin-right: 15px;
	}

	.bb-reaction-mode-description {
		margin-top: 10px;
	}

	.bb-reaction-button-label {
		display: flex !important; // Override WP default css
		flex-wrap: wrap;
		align-items: center;
		gap: 5px;
		cursor: default;

		> p {
			width: 100%;
			cursor: text;
		}

		.bb-reaction-button-text-limit {
			display: none;

			&.active {
				display: inline-block;
			}
		}

		#bb-reaction-button-chooser {
			padding: 3px;
			border-color: #8c8f94;
			vertical-align: middle;
			border-radius: 4px;
			min-height: auto;
			margin: 0;

			> i {
				color: #9b9f9b;
				font-size: 24px;
				display: block;
				line-height: 1;

				&:before {
					margin: 0;
				}
			}

			&:focus {
				border-color: #2271b1;
				box-shadow: 0 0 0 1px #2271b1;
				outline: 2px solid transparent;
			}
		}

		input#bb-reaction-button-text {
			color: #2c3338;
			font-size: 14px;
			vertical-align: middle;
			border-radius: 4px;
			width: auto;
			min-height: 30px;
			padding: 3px 8px;
			line-height: 25px;
		}
	}
}

/*------------------------------------------------------------------------------
 * Telemetry Settings
 *----------------------------------------------------------------------------*/

.bb-telemetry-notice.notice-info {
	display: flex;
	border-left-color: #e0623d;
	border-radius: 4px;
	padding: 0;

	.bb-telemetry-notice_content {
		padding-left: 20px;
	}

	.bb-telemetry-notice_logo {
		background-color: rgba(224, 98, 61, 0.1);
		padding: 15px 10px;
		color: #e0623d;
		font-size: 28px;
	}

	.bb-telemetry-notice_heading {
		font-size: 16px;
		margin: 1.2em 0 0.5em;
	}

	p {
		margin-bottom: 15px;
	}

	a.button {
		padding: 2px 16px;
		display: inline-block;
		margin-bottom: 5px;
		border-radius: 5px;

		&:after {
			font-size: 16px;
			content: "\ee68";
			font-family: bb-icons;
			font-weight: 400;
			margin-left: 5px;
		}
	}

	.notice-dismiss:before {
		font-size: 20px;
		content: "\e828";
		font-family: bb-icons;
	}

	#loco-admin.wrap & {
		border: 1px solid #c3c4c7;
		border-left: 4px solid #e0623d;
		padding: 0;
	}
}

.section-bb_advanced_telemetry {

	label[for="complete_reporting"] {
		margin-right: 15px;
	}

	.bb-telemetry-tutorial-link:after {
		content: "\ee68";
		font-family: bb-icons;
		font-size: 14px;
		margin-left: 5px;
	}
}

// Integrations Listing
.bb-integrations-section h1.bb-advance-heading {
	max-width: 900px;
}

.bb-integrations_filters_section {
	display: flex;
	justify-content: space-between;
	padding-bottom: 24px;
	margin-bottom: 24px;
	border-bottom: 1px solid rgba(0, 0, 0, 0.1);

	.bb-integrations_search {
		width: 100%;
		max-width: 290px;
		position: relative;

		span {
			font-size: 15px;
			font-weight: 400;
			color: #000;
			position: absolute;
			right: 12px;
			top: 50%;
			transform: translateY(-50%);
			pointer-events: none;

			&.clear-search {
				font-size: 18px;
				pointer-events: all;
				cursor: pointer;

				&:before {
					content: "\e828";
				}
			}
		}

		input {
			font-size: 12px;
			font-weight: 500;
			color: #000;
			padding: 6px 32px 6px 16px;
			border: 1px solid rgba(0, 0, 0, 0.3);
			border-radius: 5px;
			width: 100%;

			&::placeholder {
				color: #000;
			}

			&::-webkit-search-cancel-button {
				display: none;
			}

			&::-moz-search-cancel-button {
				display: none;
			}
		}

		&.loading {
			pointer-events: none;

			&:after {
				content: "\ef30";
				font-size: 20px;
				font-family: bb-icons;
				display: inline-block;
				animation: spin 2s infinite linear;
				position: absolute;
				top: 10px;
				right: -30px;
			}
		}
	}

	.bb-integrations_filters {
		display: flex;
		gap: 25px;

		select {
			border: 1px solid rgba(0, 0, 0, 0.1);
			border-radius: 5px;
			padding: 5px 16px 5px 16px;

			/* background-color: rgba(0, 0, 0, 0.05);
			appearance: none;
			background-image: url(data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23131313%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E);
			background-repeat: no-repeat;
			background-position: right 0.8rem top 50%;
			background-size: 0.65rem auto; */
		}

		&.loading {
			pointer-events: none;
			position: relative;

			&:after {
				content: "\ef30";
				font-size: 20px;
				font-family: bb-icons;
				display: inline-block;
				animation: spin 2s infinite linear;
				position: absolute;
				top: 10px;
				left: -30px;
			}
		}
	}

	.integrations_collection-sub {
		display: flex;
		margin: 0;

		li {
			display: flex;
			align-items: center;
			cursor: pointer;
			position: relative;
			margin: 0;

			&:hover span {
				color: #fff;
				background-color: #e0613c;
			}

			span {
				font-size: 12px;
				font-weight: 500;
				color: #000;
				border: 1px solid rgba(0, 0, 0, 0.1);
				border-left-width: 0;
				padding: 8px 16px;
			}

			input {
				opacity: 0;
				position: absolute;
				inset: 0;
				width: auto;
				height: auto;
				margin: 0;

				&:checked + span {
					color: #fff;
					background-color: #e0613c;
				}
			}

			&:first-child span {
				border-left: 1px solid rgba(0, 0, 0, 0.1);
				border-radius: 3px 0 0 3px;
			}

			&:last-child span {
				border-radius: 0 3px 3px 0;
			}
		}
	}
}

.bb-integrations-listing {
	display: flex;
	flex-wrap: wrap;
	gap: 34px;
	margin-bottom: 60px;

	.integration_cat_title {
		width: 100%;
	}

	.cat_title {
		font-size: 18px;
		font-weight: 600;
		color: #000;
		margin: 25px 0 0;
	}

	.integrations_single_holder {
		display: inline-block;
		background-color: #fff;
		border: 1px solid rgba(0, 0, 0, 0.1);
		border-radius: 10px;
		padding: 24px 24px 42px;
		width: 100%;
		max-width: 256px;
		position: relative;

		.holder_integrations_img {
			display: flex;
			justify-content: space-between;
			align-items: flex-start;
			margin-bottom: 24px;

			img {
				height: 56px;
				width: 56px;
				object-fit: contain;
				padding: 8px;
				border: 1px solid rgba(0, 0, 0, 0.1);
				border-radius: 50%;
			}

			.type_integrations_text {
				font-size: 12px;
				line-height: 22px;
				color: rgba(0, 0, 0, 0.8);
				border: 1px solid rgba(0, 0, 0, 0.1);
				padding: 0 8px;
				border-radius: 19px;
			}
		}

		.logo_title {
			color: #000;
			font-size: 14px;
			font-weight: 600;
			line-height: 20px;
			margin-bottom: 8px;
		}

		.short_desc p {
			font-size: 12px;
			color: rgba(0, 0, 0, 0.6);
			min-height: 45px;
			margin: 0 0 15px;
		}

		.integration_readmore {
			font-size: 12px;
			font-weight: 500;
			color: #640398;
			text-decoration: none;
			position: absolute;
			bottom: 24px;
			left: 24px;

			i {
				font-size: 15px;
				transform: rotate(-45deg);
				display: inline-block;
			}

			&:active,
			&:focus {
				box-shadow: none;
			}
		}

		&.loading {
			border: 0;

			.integrations_single_holder_avatar {
				height: 70px;
				width: 70px;
				border-radius: 50%;
				margin-bottom: 35px;
			}

			.integrations_single_holder_block {
				height: 18px;
				border-radius: 6px;
				width: 100%;
				margin-bottom: 15px;
			}
		}
	}

	.bb-integrations-no-results {
		display: flex;
		align-items: center;
		flex-direction: column;
		background-color: #fff;
		border: 1px solid rgba(0, 0, 0, 0.1);
		border-radius: 10px;
		padding: 40px 20px;
		width: 100%;

		h2 {
			font-size: 22px;
			margin-top: 20px;
			margin-bottom: 0;
		}

		p {
			font-size: 14px;
			color: #9b9c9f;
			margin-top: 10px;
		}

		> i {
			color: #9b9c9f;
			font-size: 38px;
			opacity: 0.9;
		}
	}
}

.bb-integrations-listing_loadmore {
	display: flex;
	justify-content: center;
}

.bb-integrations_loadmore {
	display: flex;
	align-items: center;
	gap: 5px;
	justify-content: center;
	color: #fff;
	cursor: pointer;
	font-size: 14px;
	font-weight: 500;
	background-color: #e0613c;
	border: 0;
	border-radius: 5px;
	padding: 16px 20px;
	min-width: 150px;

	&:hover {
		color: rgba(255, 255, 255, 0.8);
	}

	&.loading {
		pointer-events: none;
		opacity: 0.8;

		&:after {
			content: "\ef30";
			font-family: bb-icons;
			font-size: 18px;
			font-weight: 400;
			display: inline-block;
			animation: spin 2s infinite linear;
		}
	}
}

.bb-get-platform {
	display: flex;
	flex-direction: row-reverse;
	justify-content: space-between;
	background-color: #fff;
	border: 0.5px solid #e0613c;
	border-radius: 10px;
	box-shadow: 0 4px 32px 0 rgba(0, 0, 0, 0.1);
	padding: 40px 48px;
	max-width: 990px;
	margin: 80px auto 0;

	.bb-get-platform_details {

		h3 {
			color: #000;
			font-size: 32px;
			font-weight: 600;
			line-height: 1.25;
			margin: 0 0 16px;
		}

		p {
			color: rgba(0, 0, 0, 0.8);
			font-size: 14px;
			line-height: 20px;
			margin-bottom: 32px;
		}
	}

	.guarantee-img {
		width: 180px;
		height: 180px;
		object-fit: contain;
	}


	.bb-upgrade-btn {
		color: #fff;
		background-color: #e0613c;
		font-size: 14px;
		font-weight: 500;
		line-height: 22px;
		border-radius: 5px;
		padding: 8px 16px;
		text-decoration: none;
	}
}

.bb-integrations-loader {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	min-height: 250px;

	i {
		font-size: 26px;
		display: inline-block;
	}
}

@media screen and (max-width: 2600px) {

	.bb-integrations-listing .integrations_single_holder {
		max-width: calc(20% - 78px);
	}

}

@media screen and (max-width: 1600px) {

	.bb-integrations-listing .integrations_single_holder {
		max-width: calc(25% - 78px);
	}

}

@media screen and (max-width: 1200px) {

	.bb-integrations-listing .integrations_single_holder {
		max-width: calc(33.3333% - 78px);
	}

}

@media screen and (max-width: 1080px) {

	.bb-integrations_filters_section {
		flex-direction: column;
		gap: 20px;

		.bb-integrations_filters {
			display: flex;
			gap: 25px;
			flex-wrap: wrap;

			&.loading:after {
				left: -20px;
			}
		}
	}

	.bb-get-platform {
		flex-direction: column;

		.guarantee-img {
			margin-bottom: 24px;
		}
	}
}

@media screen and (max-width: 900px) {

	.bb-integrations-listing {
		gap: 20px;

		.integrations_single_holder {
			max-width: calc(50% - 60px);
		}
	}

}

@media screen and (max-width: 560px) {

	.bb-integrations_filters_section {

		.bb-integrations_search {
			margin: 0 25px;
			max-width: calc(100% - 50px);
		}

		.integrations_collection-sub li span {
			font-size: 11px;
			padding: 5px 10px;
		}
	}

	.bb-integrations_filters_section .bb-integrations_filters {
		justify-content: center;

		select {
			font-size: 13px;
			margin: 0 25px;
			width: calc(100% - 50px);
			max-width: inherit;
		}

		&.loading:after {
			left: inherit;
			right: -4px;
		}
	}

	.bb-integrations-listing .integrations_single_holder {
		max-width: calc(100% - 40px);
	}

}

/*------------------------------------------------------------------------------
 * 20.0 Upgrade
 *----------------------------------------------------------------------------*/

.buddyboss_page_bb-upgrade {

	.nav-tab-wrapper {
		display: none;
	}
}

.bb-upgrade-nav-tag,
.bb-rl-nav-tag {
	display: inline-block;
	vertical-align: top;
	box-sizing: border-box;
	margin: 0 0 -1px 5px;
	padding-left: 8px;
	padding-right: 8px;
	min-width: 18px;
	height: 20px;
	line-height: 20px;
	border-radius: 15px;
	background-color: #d63638;
	color: #fff;
	font-size: 11px;
	text-align: center;

	&.color-green {
		background-color: #5f9a22;
		padding-left: 6px;
		padding-right: 6px;
	}

	@media screen and (max-width: 760px) {
		margin-top: 2px;
	}
}

.advance-performance-wrapper {

	.wrap--performance {
		width: 100%;
	}
}

.wrap.wrap--upgrade {
	width: calc(100% - 22px);
	margin-left: 0;

	@media screen and (max-width: 782px) {
		width: calc(100% - 34px);
		margin-left: 12px;
	}

	> .wrap {
		margin-left: 0;
		margin-right: 0;
	}
}

.advance-tab-header {
	margin: 16px 0 0;
	padding: 0 0 16px;
	border-bottom: 1px solid rgba(0, 0, 0, 0.1);
	display: flex;
	align-items: center;

	@media screen and (max-width: 700px) {
		flex-direction: column;

		.advance-brand {
			margin: 0 auto 16px;
		}

		.nav-settings-subsubsub {
			margin-bottom: 16px;
		}

		.adv-sep {
			display: none;
		}

		.advance-actions {
			flex-direction: column;

			.advance-nav-action {
				margin-left: 0;
				margin-top: 8px;
			}
		}
	}

	.nav-settings-subsubsub {
		padding: 0;
	}

	.subsubsub {
		margin: 0;
	}

	.subsubsub > li {
		margin-right: 24px;

		&:last-of-type {
			margin-right: 0;
		}
	}

	.subsubsub a {
		color: rgba(0, 0, 0, 0.6);
		font-weight: 500;
		font-size: 14px;

		&:focus,
		&:active {
			box-shadow: none;
		}

		&.current {
			color: rgba(224, 97, 60, 1);
		}
	}

	.advance-nav-action {
		margin-left: 24px;
	}
}

.advance-brand {
	margin-right: auto;

	.upgrade-brand {
		border-radius: 50%;
		max-width: 40px;
	}
}

.adv-sep {
	width: 1px;
	height: 24px;
	background-color: rgba(0, 0, 0, 0.2);
	margin: 0 24px;
}

.advance-actions {
	display: flex;
	align-items: center;

	.advance-action-link {
		font-size: 14px;
	}
}

.advance-nav-action {
	font-size: 14px;
	font-weight: 500;
	background: #e0613c;
	color: #fff;
	border-radius: 5px;
	padding: 8px 16px;
	display: inline-flex;
	align-items: center;
	text-decoration: none;

	&:hover {
		color: rgba(255, 255, 255, 0.8);

		svg path {
			fill: rgba(255, 255, 255, 0.8);
		}
	}

	&:focus,
	&:active {
		color: #fff;
		box-shadow: none;
	}

	svg {
		margin-right: 8px;
	}
}

h1.bb-advance-heading {
	font-size: 40px;
	font-weight: 600;
	margin-bottom: 72px;
	max-width: 45%;
	margin-top: 48px;

	@media screen and (max-width: 1400px) {
		max-width: 75%;
	}

	@media screen and (max-width: 1080px) {
		font-size: 28px;
	}

	@media screen and (max-width: 760px) {
		max-width: 95%;
		margin-top: 24px;
		margin-bottom: 42px;
	}

	@media screen and (max-width: 560px) {
		font-size: 20px;
		margin-bottom: 30px;
		text-align: center;
	}
}

.bb-upgrade-wrap {
	position: relative;
	font-size: 15px;
	max-width: 1400px;
	margin-top: 25px;
	display: flex;
	align-items: flex-start;
	flex-wrap: wrap;
	justify-content: space-between;
}

.bb-advance-card {
	flex: 0 0 48%;
	max-width: 48%;
	margin: 0;
	padding: 0;
	border-radius: 9px;
	overflow: hidden;
	border: 1px solid #ccd0d4;
	box-shadow: 0 4px 28px 0 rgba(0, 0, 0, 0.08);
	background: #fff;

	@media screen and (max-width: 760px) {
		flex: 0 0 100%;
		max-width: 100%;
		margin-bottom: 24px;

		&.bb-advance-card--hero {
			margin-bottom: 24px;
		}
	}

	h2 {
		font-weight: 600;
		font-size: 1.6em;
		line-height: 1.2;
		margin: 0 0 11px;
	}

	.card-subtitle {
		font-size: 1em;
		line-height: 1.4;
	}
}

.card-data {
	flex: 1;
	padding: 2.3em 2.3em;
	display: flex;
	flex-direction: column;
}

.card-figure {
	width: 100%;
	height: 100%;
	position: relative;
	padding-top: 52.56%;
	display: block;
	background-color: #faf9f7;
	border-bottom: 1px solid rgba(0, 0, 0, 0.1);

	.upgrade-figure {
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		margin: 24px auto 0;
		z-index: 0;
		max-height: calc(100% - 24px);
		width: auto;
		max-width: 100%;
		object-fit: cover;
	}
}

.bb-advance-card--hero {
	width: 100%;
	flex: 0 0 100%;
	max-width: 100%;
	margin-bottom: 48px;

	.card-inner-wrap {
		display: flex;

		@media screen and (max-width: 760px) {
			flex-direction: column;

			.card-figure-wrapper {
				width: 100%;
			}

			.card-figure {
				padding-top: 52.56%;

				.upgrade-figure {
					max-height: 100%;
				}
			}

			.card-data {
				padding: 2.3em 2.3em;
			}
		}
	}

	.card-figure-wrapper {
		width: 42.417%;
	}

	.card-figure {
		width: 100%;
		padding-top: 0;
		border: 0;
		background-color: #1a1830;

		.upgrade-figure {
			max-height: none;
			margin: auto;
		}
	}

	.card-data {
		flex: 1;
		padding: 4.2em 2.8em;
		display: flex;
		flex-direction: column;
	}
}

.advance-card-note {
	margin: 24px 0;

	p {
		margin: 0;

		&.wp-upgrade-description {
			color: rgba(0, 0, 0, 0.6);
		}
	}
}

.advance-card-action {
	display: flex;
	align-items: center;
	margin-top: auto;

	@media screen and (max-width: 900px) {
		flex-direction: column;
	}
}

.advance-action-button,
.wp-performance-check input[type="submit"] {
	border: 1px solid #e0613c;
	color: #e0613c;
	font-weight: 500;
	background-color: #fff;
	border-radius: 5px;
	padding: 8px 16px;
	margin-right: 16px;
	cursor: pointer;
	text-decoration: none;
	outline: none;
	box-shadow: none;
	font-size: 15px;

	@media screen and (max-width: 900px) {
		margin: 0 0 16px;
	}

	&:hover {
		color: #e0613c;
	}

	&:active,
	&:focus {
		background-color: rgba(224, 97, 60, 0.1);
		color: #e0613c;
		outline: none;
		box-shadow: none;
	}

	.advance-action-success & {
		border-color: #019701;
		color: #019701;
	}

	&.advance-action-button--idle {
		pointer-events: none;
		cursor: default;
	}
}

.advance-action-link {
	color: #640398;
	font-weight: 500;
	text-decoration: none;
	outline: none;
	box-shadow: none;
	border-bottom: 1px solid transparent;
	padding: 2px 0 1px;

	i {
		display: inline-block;
		transform: rotate(45deg);
		line-height: 1;
	}

	&:hover {
		color: #640398;
		border-bottom: 1px solid #640398;
	}

	&:active,
	&:focus {
		outline: none;
		box-shadow: none;
		color: #640398;
	}

	&.advance-action-link--back {
		display: inline-block;
		margin: 12px 0;

		i {
			transform: none;
		}
	}
}

.advance-list {
	display: flex;
	flex-wrap: wrap;
	margin: 0 33.33% 0 0;

	.bb-advance-card--theme & {
		margin-right: 16%;
	}

	@media screen and (max-width: 1500px) {
		margin-right: 0;

		.bb-advance-card--theme & {
			margin-right: 0;
		}
	}

	li {
		max-width: 50%;
		flex: 0 0 50%;
		margin: 0 0 11px;
		font-weight: 600;
		font-size: 95%;

		@media screen and (max-width: 1300px) {
			max-width: 100%;
			flex: 0 0 100%;
		}

		&.advance-list__expand {
			max-width: 100%;
			flex: 0 0 100%;
		}

		&:before {
			content: "\e876";
			display: inline-block;
			font-family: bb-icons;
			font-size: 16px;
			font-weight: 400;
			margin-right: 8px;
			line-height: 1;
			color: #e0613c;
		}
	}
}

.bb-upgrade-notice {
	background-color: #f6efe8;
	padding: 8px;
	text-align: center;
	border-bottom: 1px solid #c5bfba;
	margin-left: -20px;
	position: relative;

	.bb-upgrade-point {
		font-size: 1rem;
		font-weight: 500;
		color: #000;
		display: flex;
		align-items: center;
		justify-content: center;

		i {
			font-size: 24px;
			margin-right: 4px;
		}

		.bb-upgrade-notice__link {
			font-weight: 600;
			color: inherit;
			margin-left: 16px;

			&:focus {
				box-shadow: none;
			}
		}
	}

	.bb-dismiss-upgrade-notice {
		display: inline-block;
		width: 20px;
		height: 20px;
		position: absolute;
		color: rgba(0, 0, 0, 0.6);
		right: 8px;
		top: 50%;
		transform: translateY(-50%);
		border: 0;
		padding: 0;
		outline: none;
		cursor: pointer;

		&:before {
			content: "\e828";
			font-family: bb-icons;
			font-style: normal;
			display: inline-block;
			width: 20px;
			height: 20px;
			font-size: 20px;
			text-decoration: none;
		}

		&:hover {
			color: rgba(0, 0, 0, 1);
		}

		&:focus,
		&:active {
			outline: 0;
			box-shadow: none;
		}
	}

	@media screen and (max-width: 768px) {
		margin: 0 0 0 -10px;
		padding-right: 35px;
	}
}

.wrap.wrap--performance {

	> h2:first-child {
		font-weight: 500;
		font-size: 24px;
	}

	> p {
		color: rgba(0, 0, 0, 0.6);
		font-size: 14px;
	}
}

.wp-performance-check {

	input[type="submit"] {
		margin: 24px 0 32px;
	}
}

.wrap--performance {
	max-width: 600px;

	#resultTable {

		table {
			width: 100%;
		}
	}
}

#customize-theme-controls {

	.accordion-section-title button.accordion-trigger {
		height: auto;
	}
}

/*------------------------------------------------------------------------------
 * 21.0 Activity Filter & Sorting
 *----------------------------------------------------------------------------*/

.bb-activity-sorting-list.ui-sortable {

	.bb-activity-sorting-item {
		margin-bottom: 10px;

		&:before {
			content: "\edb2";
			color: #000;
			font-family: bb-icons;
			font-size: 16px;
			font-weight: 400;
			cursor: move;
			margin-right: 8px;
		}
	}
}

// Activity Topics Settings Styles.

.bb-modal-panel--activity-topic,
.bb-modal-panel--activity-topic-migrate {

	&:has(.bb-hello-content > .bb-hello-error) .bb-hello-header {
		border-bottom: 0 !important;
	}
}

.bb-activity-topics-wrapper {
	margin-top: 15px;

	.activity-topic-label {
		margin: 0 0 15px;
		font-size: 14px;
		color: #1d2327;
	}

	.bb-activity-topics-content {
		margin-bottom: 10px;

		&.bb-has-topics {
			background: #f0f0f1;
			border-radius: 6px;
			padding: 16px;
		}

		.bb-activity-topics-list {
			margin-bottom: 18px;

			.bb-activity-topic-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 8px 10px;
				border: 1px solid #e1e3e5;
				border-radius: 6px;
				margin-bottom: 8px;
				background: #fff;

				&:hover {
					border-color: #2271b1;
				}

				.bb-topic-left {
					display: flex;
					align-items: center;
					gap: 8px;
					margin-right: 8px;

					.bb-topic-drag {
						font-size: 16px;
						color: #9b9f9b;
						font-weight: 300;
						cursor: move;

						i:before {
							margin: 0;
						}
					}

					.bb-topic-title {
						color: #101517;
						font-size: 15px;
						font-weight: 500;
						word-break: break-all;
					}
				}

				.bb-topic-right {
					display: flex;
					align-items: center;
					gap: 15px;

					.bb-topic-access {
						color: #a7aaad;
						font-size: 13px;
					}

					.bb-topic-actions_button {
						display: inline-block;
						color: #1e2132;
						font-size: 16px;
						font-weight: 300;
						padding: 4px;
						border-radius: 4px;
						opacity: 0.4;
					}
				}

				&.ui-sortable-placeholder {
					border: 1px dashed #e1e3e5;
					visibility: visible !important;
				}
			}

			&.is-loading {
				pointer-events: none;
				position: relative;

				> * {
					opacity: 0.5;
				}

				&:after {
					content: "";
					display: inline-block;
					box-sizing: border-box;
					width: 20px;
					height: 20px;
					border-radius: 50%;
					border: 2px solid rgba(155, 156, 159, 0.2);
					border-top-color: #9b9c9f;
					animation: spin 2s infinite linear;
					position: absolute;
					left: 50%;
					top: 50%;
				}
			}
		}

		.bb-add-topic {
			display: inline-flex;
			align-items: center;
			gap: 4px;
			padding: 5px 8px;
			font-size: 13px;
			line-height: 1.5;
			border-radius: 4px;

			.bb-icon-plus {
				font-size: 16px;
				line-height: 1;

				&:before {
					margin: 0;
				}
			}
		}

		.description {
			color: #646970;
			font-size: 12px;
			margin: 10px 0 0;
		}

		.bb-topics-sort-success {
			padding: 0 10px;
			border-radius: 0 5px 5px 0;
			margin: 0 0 20px !important;
		}
	}

	p.description {
		color: #8c8f94;
		font-size: 14px;
		font-weight: 400;
	}
}

.bb-activity-topics-wrapper {

	.bb-topic-actions-wrapper {
		position: relative;

		.bb-topic-more-dropdown {
			position: absolute;
			top: 100%;
			right: 0;
			background-color: #fff;
			border: 1px solid #e1e3e5;
			border-radius: 6px;
			padding: 8px;
			box-shadow: 0 2px 7px 1px rgba(0, 0, 0, 0.05), 0 6px 32px 0 rgba(18, 43, 70, 0.1);
			min-width: 150px;
			z-index: 100;
			display: none;

			a {
				display: block;
				border: 0;
				background: transparent;
				color: #3c434a;
				font-size: 13px;
				font-weight: 400;
				line-height: 1;
				cursor: pointer;
				border-radius: 6px;
				padding: 8px 10px;

				&:before {
					font-family: bb-icons;
					font-size: 16px;
					font-weight: 400;
					margin-right: 5px;
				}

				&.bb-edit-topic:before {
					content: "\eeec";
				}

				&.bb-delete-topic:before {
					content: "\ef48";
				}

				&:hover {
					background-color: #f0f0f1;
				}
			}
		}

		&.active {

			.bb-topic-more-dropdown {
				display: block;
			}
		}
	}
}

// Add topic modal
.bb-modal-panel--activity-topic.bb-modal-panel,
.bb-modal-panel--activity-topic-migrate.bb-modal-panel {
	width: 100%;
	max-width: 700px;

	.bb-hello-header {
		position: relative;
		padding: 15px;
		background-color: #f6f7f7;
		border-bottom: 1px solid #dcdcde;
	}

	#bb-hello-title {
		margin: 0;
		padding: 0;
		border: 0;
		font-weight: 500;
	}

	.bb-hello-close {
		top: 50%;
		transform: translateY(-50%);

		button {
			border: 0;
			padding: 0;
			background: transparent;
			line-height: 1;
			font-size: 28px;
			color: #a7aaad;
			min-height: auto;

			i:before {
				margin: 0;
			}
		}
	}

	.bb-hello-content {
		padding: 15px 20px;

		.form-fields {
			margin: 0;
		}

		div.bb-hello-error {
			color: #d63638;
			border-color: #ffabaf;
			padding: 8px 20px;
			margin: -15px -20px 18px -20px;
			align-items: center;
		}
	}

	.form-fields {

		.form-field {
			margin-bottom: 15px;
		}

		.field-label {
			margin-bottom: 12px;

			label {
				font-size: 14px;
				font-weight: 500;
				color: #1d2327;
				margin-bottom: 10px;
			}
		}

		.bb-topic-who-can-post-option {
			margin-bottom: 10px;
		}
	}

	.bb-popup-buttons {
		background-color: #f6f7f7;
		border-top: 1px solid #dcdcde;
		margin: 0 -20px;
		padding: 12px 15px;
		display: flex;
		justify-content: flex-end;
		gap: 10px;
		box-sizing: border-box;
	}

	&.is-loading {

		.bb-hello-header,
		.bb-popup-buttons {
			z-index: 1;
		}

		&:before {
			content: "";
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background-color: #fff;
			z-index: 1;
		}

		&:after {
			content: "\ef30";
			font-family: bb-icons;
			font-size: 24px;
			font-weight: 400;
			line-height: 1;
			margin: -6px 0 0 -6px;
			position: absolute;
			top: 50%;
			left: 50%;
			text-align: center;
			display: inline-block;
			animation: spin 3s infinite linear;
			z-index: 1;
		}
	}
}

// Topic migrate modal
.bb-hello-backdrop-activity-topic-migrate {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9998;
	background-color: rgba(0, 0, 0, 0.8);
	transition: opacity 0.15s ease-out;
}

.bb-hello-activity-topic-migrate.bb-modal-panel {
	position: fixed;
	z-index: 9999;
	border-radius: 3px;
	bottom: initial;
	top: 50%;
	left: 50%;
	right: unset;
	transform: translate(-50%, -50%);
	max-width: 500px;

	#bb-hello-title {
		font-size: 18px;
		line-height: 1.2;
		color: #1d2327;
		width: calc(100% - 35px);
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		display: block;
	}

	div.bb-hello-content {
		min-height: 130px;
		margin-bottom: 55px;

		.form-fields {

			.form-field {
				display: flex;
				align-items: center;
				gap: 15px;
				margin-bottom: 8px;
			}

			.field-input select {
				min-width: 150px;
				max-width: 150px;
			}

			.field-label {
				display: flex;
				align-items: center;
				gap: 6px;
				margin-bottom: 0;

				input[type="radio"] {
					margin: 0;
				}

				label {
					font-size: 14px;
					font-weight: 400;
					color: #50575e;
					margin-bottom: 0;
				}
			}
		}

		#bb_topic_cancel {
			border-color: #646970;
			color: #2c3338;
		}

		#bb_topic_migrate {
			color: #fff;
			border-color: #d63638;
			background-color: #d63638;

			&.is-loading {

				&:after {
					content: "\ef30";
					font-family: bb-icons;
					font-size: 18px;
					font-weight: 400;
					line-height: 1;
					margin: 0 0 3px 5px;
					text-align: center;
					display: inline-block;
					vertical-align: middle;
					animation: spin 3s infinite linear;
				}
			}
		}
	}

	.bb-hello-content-description {
		color: #1d2327;
		font-size: 14px;
		font-weight: 400;
		margin: 0 0 24px;
	}
}

#bb-hello-container.bb-modal-panel,
.admin-bar #bb-hello-container.bb-modal-panel,
#bb-hello-topic-migrate-container.bb-modal-panel,
.admin-bar #bb-hello-topic-migrate-container.bb-modal-panel {
	top: 50%;
	left: 50%;
	right: unset;
	transform: translate(-50%, -50%);

	@media screen and (max-width: 768px) {
		width: 90%;
	}
}

.bb-modal-panel {

	.bb-hello-header {
		background-color: #f6f7f7;
		border-bottom-color: var(--bb-hello-color-secondary);
	}

	.bb-hello-title h2 {
		color: #1d2327;
		font-size: 1.5em;
		margin: 0.5em 0;
		padding-bottom: 0;
		border-bottom: none;
		font-weight: 600;
	}

	.bb-hello-content {
		height: auto;
		padding: 0 23px;
		min-height: 180px;
		max-height: calc(100vh - 220px);
		margin-bottom: 56px;

		.form-fields {
			margin: 10px 0;
		}

		.form-field .bb-hello-error:first-of-type {
			margin-top: -10px;
		}

		.form-field .bb-hello-error + .field-label {
			margin-top: 20px;
		}

		> .bb-hello-error:first-of-type {
			margin-top: 0;
		}

		.bb-hello-error {
			margin: -1px -23px 0;
			background-color: rgba(252, 240, 241, 1);
			border-top: 1px solid rgba(214, 54, 56, 1);
			border-bottom: 1px solid rgba(214, 54, 56, 1);
			padding: 10px 23px;
			display: flex;

			> i {
				color: rgba(214, 54, 56, 1);
				font-size: 16px;
				margin-right: 8px;

				&::before {
					margin: 0;
				}
			}
		}

		.bb-hello-error + .bb-hello-error {
			border-top: 0;
		}

		.form-field .bb-hello-success:first-of-type {
			margin-top: -10px;
		}

		.form-field .bb-hello-success + .field-label {
			margin-top: 20px;
		}

		> .bb-hello-success:first-of-type {
			margin-top: 0;
		}

		.form-field .bb-hello-success + .field-label {
			margin-top: 20px;
		}

		.bb-hello-success {
			margin: -1px -23px 0;
			background-color: rgb(230, 252, 229);
			border-top: 1px solid rgb(102, 214, 54);
			border-bottom: 1px solid rgb(102, 214, 54);
			padding: 10px 23px;
			display: flex;

			> i {
				color: rgb(102, 214, 54);
				font-size: 16px;
				margin-right: 8px;

				&::before {
					margin: 0;
				}
			}
		}

		.bb-hello-success + .bb-hello-success {
			border-top: 0;
		}
	}

	.form-fields {

		.field-label {
			margin-bottom: 5px;

			label {
				line-height: 22px;
				font-size: 13px;
				font-weight: 600;
			}
		}

		.field-input {

			input[type="text"],
			textarea {
				width: 100%;
			}
		}

		.form-field {
			margin-bottom: 5px;
		}
	}

	.bb-popup-buttons {
		position: absolute;
		bottom: 0;
		padding: 12px 15px;
		width: 100%;
		margin: 0 -23px;
		border-top: 1px solid var(--bb-hello-color-secondary);
		background-color: #f6f7f7;
	}

	@media screen and (orientation: landscape) and (max-height: 480px) {

		.bb-hello-content {
			min-height: 110px;
		}
	}

	.form-field {

		.description {
			margin: 13px 0 20px 0;
			line-height: 22px;
		}
	}
}

#bb-hello-container.bb-modal-panel,
#bb-hello-topic-migrate-container.bb-modal-panel {
	top: 50%;
	bottom: initial;
	transform: translateY(-50%);

	.button-primary {
		color: #fff;
		margin-right: 5px;

		@media screen and (max-width: 480px) {
			margin-right: 0;
		}
	}
}
