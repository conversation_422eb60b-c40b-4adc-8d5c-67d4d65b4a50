<?php
// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

return array(
	'textarea'  => array(
		'Keep your face to the sunshine and you cannot see a shadow.',
		"Once you replace negative thoughts with positive ones, you'll start having positive results.",
		'Yesterday is not ours to recover, but tomorrow is ours to win or lose.',
		'In order to carry a positive action we must develop here a positive vision.',
		"I always like to look on the optimistic side of life, but I'm realistic enough to know that life is a complex matter.",
		'Positive thinking will let you do everything better than negative thinking will.',
		'You cannot make positive choices for the rest of your life without an environment that makes those choices easy, natural, and enjoyable.',
		'The thing that lies at the foundation of positive change, the way I see it, is service to a fellow human being.',
		'Positive thinking is more than just a tagline. It changes the way we behave. And I firmly believe that when I am positive, it not only makes me better, but it also makes those around me better.',
		'In every day, there are 1,440 minutes. That means we have 1,440 daily opportunities to make a positive impact.',
		"I'm a very positive thinker, and I think that is what helps me the most in difficult moments.",
		'Perpetual optimism is a force multiplier.',
		'Attitude is a little thing that makes a big difference.',
	),
	'number'    => array(
		'2000',
		'2002',
		'2004',
		'2006',
		'2008',
		'2010',
		'2012',
		'2014',
	),
	'checkbox'  => array(
		'Checkbox 1',
		'Checkbox 2',
		'Checkbox 1,Checkbox 2',
		'Checkbox 2,Checkbox 4',
		'Checkbox 2,Checkbox 3',
		'Checkbox 4,Checkbox 1',
		'Checkbox 3,Checkbox 4',
		'Checkbox 4,Checkbox 1,Checkbox 3',
		'Checkbox 4,Checkbox 2,Checkbox 3',
		'Checkbox 4,Checkbox 1,Checkbox 2,Checkbox 3',
	),
	'selectbox' => array(
		'Option 1',
		'Option 2',
		'Option 3',
	),
	'radio'     => array(
		'Radio 1',
		'Radio 2',
		'Radio 3',
	),
);
