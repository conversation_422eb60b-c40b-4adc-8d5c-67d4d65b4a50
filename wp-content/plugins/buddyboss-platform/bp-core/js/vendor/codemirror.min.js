!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e=e||self).CodeMirror=t()}(this,function(){"use strict";var e,t,r,n,i,o,l,s,a,u,c=navigator.userAgent,f=navigator.platform,h=/gecko\/\d/i.test(c),d=/MSIE \d/.test(c),p=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(c),g=/Edge\/(\d+)/.exec(c),v=d||p||g,m=v&&(d?document.documentMode||6:+(g||p)[1]),$=!g&&/WebKit\//.test(c),y=$&&/Qt\/\d+\.\d+/.test(c),_=!g&&/Chrome\/(\d+)/.exec(c),b=_&&+_[1],x=/Opera\//.test(c),w=/Apple Computer/.test(navigator.vendor),C=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(c),S=/PhantomJS/.test(c),L=w&&(/Mobile\/\w+/.test(c)||navigator.maxTouchPoints>2),k=/Android/.test(c),T=L||k||/webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(c),N=L||/Mac/.test(f),O=/\bCrOS\b/.test(c),M=/win/i.test(f),A=x&&c.match(/Version\/(\d*\.\d*)/);A&&(A=Number(A[1])),A&&A>=15&&(x=!1,$=!0);var D=N&&(y||x&&(null==A||A<12.11)),W=h||v&&m>=9;function H(e){return RegExp("(^|\\s)"+e+"(?:$|\\s)\\s*")}var F=function(e,t){var r=e.className,n=H(t).exec(r);if(n){var i=r.slice(n.index+n[0].length);e.className=r.slice(0,n.index)+(i?n[1]+i:"")}};function P(e){for(var t=e.childNodes.length;t>0;--t)e.removeChild(e.firstChild);return e}function E(e,t){return P(e).appendChild(t)}function z(e,t,r,n){var i=document.createElement(e);if(r&&(i.className=r),n&&(i.style.cssText=n),"string"==typeof t)i.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)i.appendChild(t[o]);return i}function R(e,t,r,n){var i=z(e,t,r,n);return i.setAttribute("role","presentation"),i}function I(e,t){if(3==t.nodeType&&(t=t.parentNode),e.contains)return e.contains(t);do if(11==t.nodeType&&(t=t.host),t==e)return!0;while(t=t.parentNode)}function B(e){var t;try{t=e.activeElement}catch(r){t=e.body||null}for(;t&&t.shadowRoot&&t.shadowRoot.activeElement;)t=t.shadowRoot.activeElement;return t}function G(e,t){var r=e.className;H(t).test(r)||(e.className+=(r?" ":"")+t)}function U(e,t){for(var r=e.split(" "),n=0;n<r.length;n++)r[n]&&!H(r[n]).test(t)&&(t+=" "+r[n]);return t}i=document.createRange?function(e,t,r,n){var i=document.createRange();return i.setEnd(n||e,r),i.setStart(e,t),i}:function(e,t,r){var n=document.body.createTextRange();try{n.moveToElementText(e.parentNode)}catch(i){return n}return n.collapse(!0),n.moveEnd("character",r),n.moveStart("character",t),n};var V=function(e){e.select()};function K(e){return e.display.wrapper.ownerDocument}function X(e){return K(e).defaultView}function j(e){var t=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,t)}}function Y(e,t,r){for(var n in t||(t={}),e)e.hasOwnProperty(n)&&(!1!==r||!t.hasOwnProperty(n))&&(t[n]=e[n]);return t}function q(e,t,r,n,i){null==t&&-1==(t=e.search(/[^\s\u00a0]/))&&(t=e.length);for(var o=n||0,l=i||0;;){var s=e.indexOf("	",o);if(s<0||s>=t)return l+(t-o);l+=s-o,l+=r-l%r,o=s+1}}L?V=function(e){e.selectionStart=0,e.selectionEnd=e.value.length}:v&&(V=function(e){try{e.select()}catch(t){}});var Z=function(){this.id=null,this.f=null,this.time=0,this.handler=j(this.onTimeout,this)};function Q(e,t){for(var r=0;r<e.length;++r)if(e[r]==t)return r;return -1}Z.prototype.onTimeout=function(e){e.id=0,e.time<=+new Date?e.f():setTimeout(e.handler,e.time-+new Date)},Z.prototype.set=function(e,t){this.f=t;var r=+new Date+e;(!this.id||r<this.time)&&(clearTimeout(this.id),this.id=setTimeout(this.handler,e),this.time=r)};var J={toString:function(){return"CodeMirror.Pass"}},ee={scroll:!1},et={origin:"*mouse"},er={origin:"+move"};function en(e,t,r){for(var n=0,i=0;;){var o=e.indexOf("	",n);-1==o&&(o=e.length);var l=o-n;if(o==e.length||i+l>=t)return n+Math.min(l,t-i);if(i+=o-n,i+=r-i%r,n=o+1,i>=t)return n}}var ei=[""];function eo(e){for(;ei.length<=e;)ei.push(el(ei)+" ");return ei[e]}function el(e){return e[e.length-1]}function es(e,t){for(var r=[],n=0;n<e.length;n++)r[n]=t(e[n],n);return r}function ea(){}function eu(e,t){var r;return Object.create?r=Object.create(e):(ea.prototype=e,r=new ea),t&&Y(t,r),r}var ec=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;function ef(e){return/\w/.test(e)||e>"\x80"&&(e.toUpperCase()!=e.toLowerCase()||ec.test(e))}function eh(e,t){return t?!!(t.source.indexOf("\\w")>-1&&ef(e))||t.test(e):ef(e)}function ed(e){for(var t in e)if(e.hasOwnProperty(t)&&e[t])return!1;return!0}var ep=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;function eg(e){return e.charCodeAt(0)>=768&&ep.test(e)}function ev(e,t,r){for(;(r<0?t>0:t<e.length)&&eg(e.charAt(t));)t+=r;return t}function em(e,t,r){for(var n=t>r?-1:1;;){if(t==r)return t;var i=(t+r)/2,o=n<0?Math.ceil(i):Math.floor(i);if(o==t)return e(o)?t:r;e(o)?r=o:t=o+n}}var e$=null;function ey(e,t,r){var n;e$=null;for(var i=0;i<e.length;++i){var o=e[i];if(o.from<t&&o.to>t)return i;o.to==t&&(o.from!=o.to&&"before"==r?n=i:e$=i),o.from==t&&(o.from!=o.to&&"before"!=r?n=i:e$=i)}return null!=n?n:e$}var e_=function(){function e(e){if(e<=247)return"bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN".charAt(e);if(1424<=e&&e<=1524)return"R";if(1536<=e&&e<=1785)return"nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111".charAt(e-1536);if(1774<=e&&e<=2220)return"r";if(8192<=e&&e<=8203)return"w";else if(8204==e)return"b";else return"L"}var t=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,r=/[stwN]/,n=/[LRr]/,i=/[Lb1n]/,o=/[1n]/;function l(e,t,r){this.level=e,this.from=t,this.to=r}return function(s,a){var u="ltr"==a?"L":"R";if(0==s.length||"ltr"==a&&!t.test(s))return!1;for(var c=s.length,f=[],h=0;h<c;++h)f.push(e(s.charCodeAt(h)));for(var d=0,p=u;d<c;++d){var g=f[d];"m"==g?f[d]=p:p=g}for(var v=0,m=u;v<c;++v){var $=f[v];"1"==$&&"r"==m?f[v]="n":n.test($)&&(m=$,"r"==$&&(f[v]="R"))}for(var y=1,_=f[0];y<c-1;++y){var b=f[y];"+"==b&&"1"==_&&"1"==f[y+1]?f[y]="1":","==b&&_==f[y+1]&&("1"==_||"n"==_)&&(f[y]=_),_=b}for(var x=0;x<c;++x){var w=f[x];if(","==w)f[x]="N";else if("%"==w){var C=void 0;for(C=x+1;C<c&&"%"==f[C];++C);for(var S=x&&"!"==f[x-1]||C<c&&"1"==f[C]?"1":"N",L=x;L<C;++L)f[L]=S;x=C-1}}for(var k=0,T=u;k<c;++k){var N=f[k];"L"==T&&"1"==N?f[k]="L":n.test(N)&&(T=N)}for(var O=0;O<c;++O)if(r.test(f[O])){var M=void 0;for(M=O+1;M<c&&r.test(f[M]);++M);for(var A=(O?f[O-1]:u)=="L",D=(M<c?f[M]:u)=="L",W=A==D?A?"L":"R":u,H=O;H<M;++H)f[H]=W;O=M-1}for(var F,P=[],E=0;E<c;)if(i.test(f[E])){var z=E;for(++E;E<c&&i.test(f[E]);++E);P.push(new l(0,z,E))}else{var R=E,I=P.length,B="rtl"==a?1:0;for(++E;E<c&&"L"!=f[E];++E);for(var G=R;G<E;)if(o.test(f[G])){R<G&&(P.splice(I,0,new l(1,R,G)),I+=B);var U=G;for(++G;G<E&&o.test(f[G]);++G);P.splice(I,0,new l(2,U,G)),I+=B,R=G}else++G;R<E&&P.splice(I,0,new l(1,R,E))}return"ltr"==a&&(1==P[0].level&&(F=s.match(/^\s+/))&&(P[0].from=F[0].length,P.unshift(new l(0,0,F[0].length))),1==el(P).level&&(F=s.match(/\s+$/))&&(el(P).to-=F[0].length,P.push(new l(0,c-F[0].length,c)))),"rtl"==a?P.reverse():P}}();function eb(e,t){var r=e.order;return null==r&&(r=e.order=e_(e.text,t)),r}var ex=[],ew=function(e,t,r){if(e.addEventListener)e.addEventListener(t,r,!1);else if(e.attachEvent)e.attachEvent("on"+t,r);else{var n=e._handlers||(e._handlers={});n[t]=(n[t]||ex).concat(r)}};function eC(e,t){return e._handlers&&e._handlers[t]||ex}function eS(e,t,r){if(e.removeEventListener)e.removeEventListener(t,r,!1);else if(e.detachEvent)e.detachEvent("on"+t,r);else{var n=e._handlers,i=n&&n[t];if(i){var o=Q(i,r);o>-1&&(n[t]=i.slice(0,o).concat(i.slice(o+1)))}}}function eL(e,t){var r=eC(e,t);if(r.length)for(var n=Array.prototype.slice.call(arguments,2),i=0;i<r.length;++i)r[i].apply(null,n)}function ek(e,t,r){return"string"==typeof t&&(t={type:t,preventDefault:function(){this.defaultPrevented=!0}}),eL(e,r||t.type,e,t),e0(t)||t.codemirrorIgnore}function eT(e){var t=e._handlers&&e._handlers.cursorActivity;if(t)for(var r=e.curOp.cursorActivityHandlers||(e.curOp.cursorActivityHandlers=[]),n=0;n<t.length;++n)-1==Q(r,t[n])&&r.push(t[n])}function eN(e,t){return eC(e,t).length>0}function eO(e){e.prototype.on=function(e,t){ew(this,e,t)},e.prototype.off=function(e,t){eS(this,e,t)}}function eM(e){e.preventDefault?e.preventDefault():e.returnValue=!1}function eA(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}function e0(e){return null!=e.defaultPrevented?e.defaultPrevented:!1==e.returnValue}function eD(e){eM(e),eA(e)}function eW(e){return e.target||e.srcElement}function eH(e){var t=e.which;return null==t&&(1&e.button?t=1:2&e.button?t=3:4&e.button&&(t=2)),N&&e.ctrlKey&&1==t&&(t=3),t}var eF=function(){if(v&&m<9)return!1;var e=z("div");return"draggable"in e||"dragDrop"in e}();function eP(e){if(null==o){var t=z("span","​");E(e,z("span",[t,document.createTextNode("x")])),0!=e.firstChild.offsetHeight&&(o=t.offsetWidth<=1&&t.offsetHeight>2&&!(v&&m<8))}var r=o?z("span","​"):z("span","\xa0",null,"display: inline-block; width: 1px; margin-right: -1px");return r.setAttribute("cm-text",""),r}function e1(e){if(null!=l)return l;var t=E(e,document.createTextNode("AخA")),r=i(t,0,1).getBoundingClientRect(),n=i(t,1,2).getBoundingClientRect();return P(e),!!r&&r.left!=r.right&&(l=n.right-r.right<3)}var eE,ez=3!="\n\nb".split(/\n/).length?function(e){for(var t=0,r=[],n=e.length;t<=n;){var i=e.indexOf("\n",t);-1==i&&(i=e.length);var o=e.slice(t,"\r"==e.charAt(i-1)?i-1:i),l=o.indexOf("\r");-1!=l?(r.push(o.slice(0,l)),t+=l+1):(r.push(o),t=i+1)}return r}:function(e){return e.split(/\r\n?|\n/)},eR=window.getSelection?function(e){try{return e.selectionStart!=e.selectionEnd}catch(t){return!1}}:function(e){var t;try{t=e.ownerDocument.selection.createRange()}catch(r){}return!!t&&t.parentElement()==e&&0!=t.compareEndPoints("StartToEnd",t)},eI="oncopy"in(eE=z("div"))||(eE.setAttribute("oncopy","return;"),"function"==typeof eE.oncopy),e3=null,eB={},e7={};function e4(e,t){arguments.length>2&&(t.dependencies=Array.prototype.slice.call(arguments,2)),eB[e]=t}function e6(e,t){e7[e]=t}function e5(e){if("string"==typeof e&&e7.hasOwnProperty(e))e=e7[e];else if(e&&"string"==typeof e.name&&e7.hasOwnProperty(e.name)){var t=e7[e.name];"string"==typeof t&&(t={name:t}),(e=eu(t,e)).name=t.name}else if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+xml$/.test(e))return e5("application/xml");else if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+json$/.test(e))return e5("application/json");return"string"==typeof e?{name:e}:e||{name:"null"}}function e2(e,t){var r=eB[(t=e5(t)).name];if(!r)return e2(e,"text/plain");var n=r(e,t);if(eG.hasOwnProperty(t.name)){var i=eG[t.name];for(var o in i)i.hasOwnProperty(o)&&(n.hasOwnProperty(o)&&(n["_"+o]=n[o]),n[o]=i[o])}if(n.name=t.name,t.helperType&&(n.helperType=t.helperType),t.modeProps)for(var l in t.modeProps)n[l]=t.modeProps[l];return n}var eG={};function eU(e,t){if(!0===t)return t;if(e.copyState)return e.copyState(t);var r={};for(var n in t){var i=t[n];i instanceof Array&&(i=i.concat([])),r[n]=i}return r}function eV(e,t){for(var r;e.innerMode&&(r=e.innerMode(t))&&r.mode!=e;)t=r.state,e=r.mode;return r||{mode:e,state:t}}function eK(e,t,r){return!e.startState||e.startState(t,r)}var eX=function(e,t,r){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0,this.lineOracle=r};function ej(e,t){if((t-=e.first)<0||t>=e.size)throw Error("There is no line "+(t+e.first)+" in the document.");for(var r=e;!r.lines;)for(var n=0;;++n){var i=r.children[n],o=i.chunkSize();if(t<o){r=i;break}t-=o}return r.lines[t]}function eY(e,t,r){var n=[],i=t.line;return e.iter(t.line,r.line+1,function(e){var o=e.text;i==r.line&&(o=o.slice(0,r.ch)),i==t.line&&(o=o.slice(t.ch)),n.push(o),++i}),n}function e8(e,t,r){var n=[];return e.iter(t,r,function(e){n.push(e.text)}),n}function e9(e,t){var r=t-e.height;if(r)for(var n=e;n;n=n.parent)n.height+=r}function eq(e){if(null==e.parent)return null;for(var t=e.parent,r=Q(t.lines,e),n=t.parent;n;t=n,n=n.parent)for(var i=0;n.children[i]!=t;++i)r+=n.children[i].chunkSize();return r+t.first}function eZ(e,t){var r=e.first;outer:do{for(var n=0;n<e.children.length;++n){var i=e.children[n],o=i.height;if(t<o){e=i;continue outer}t-=o,r+=i.chunkSize()}return r}while(!e.lines);for(var l=0;l<e.lines.length;++l){var s=e.lines[l].height;if(t<s)break;t-=s}return r+l}function eQ(e,t){return t>=e.first&&t<e.first+e.size}function eJ(e,t){return String(e.lineNumberFormatter(t+e.firstLineNumber))}function te(e,t,r){if(void 0===r&&(r=null),!(this instanceof te))return new te(e,t,r);this.line=e,this.ch=t,this.sticky=r}function tt(e,t){return e.line-t.line||e.ch-t.ch}function tr(e,t){return e.sticky==t.sticky&&0==tt(e,t)}function tn(e){return te(e.line,e.ch)}function ti(e,t){return 0>tt(e,t)?t:e}function to(e,t){return 0>tt(e,t)?e:t}function tl(e,t){return Math.max(e.first,Math.min(t,e.first+e.size-1))}function ts(e,t){if(t.line<e.first)return te(e.first,0);var r,n,i,o=e.first+e.size-1;return t.line>o?te(o,ej(e,o).text.length):(r=t,n=ej(e,t.line).text.length,i=r.ch,null==i||i>n?te(r.line,n):i<0?te(r.line,0):r)}function ta(e,t){for(var r=[],n=0;n<t.length;n++)r[n]=ts(e,t[n]);return r}eX.prototype.eol=function(){return this.pos>=this.string.length},eX.prototype.sol=function(){return this.pos==this.lineStart},eX.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},eX.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},eX.prototype.eat=function(e){var t,r=this.string.charAt(this.pos);if(t="string"==typeof e?r==e:r&&(e.test?e.test(r):e(r)))return++this.pos,r},eX.prototype.eatWhile=function(e){for(var t=this.pos;this.eat(e););return this.pos>t},eX.prototype.eatSpace=function(){for(var e=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>e},eX.prototype.skipToEnd=function(){this.pos=this.string.length},eX.prototype.skipTo=function(e){var t=this.string.indexOf(e,this.pos);if(t>-1)return this.pos=t,!0},eX.prototype.backUp=function(e){this.pos-=e},eX.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=q(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?q(this.string,this.lineStart,this.tabSize):0)},eX.prototype.indentation=function(){return q(this.string,null,this.tabSize)-(this.lineStart?q(this.string,this.lineStart,this.tabSize):0)},eX.prototype.match=function(e,t,r){if("string"==typeof e){var n=function(e){return r?e.toLowerCase():e};if(n(this.string.substr(this.pos,e.length))==n(e))return!1!==t&&(this.pos+=e.length),!0}else{var i=this.string.slice(this.pos).match(e);return i&&i.index>0?null:(i&&!1!==t&&(this.pos+=i[0].length),i)}},eX.prototype.current=function(){return this.string.slice(this.start,this.pos)},eX.prototype.hideFirstChars=function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}},eX.prototype.lookAhead=function(e){var t=this.lineOracle;return t&&t.lookAhead(e)},eX.prototype.baseToken=function(){var e=this.lineOracle;return e&&e.baseToken(this.pos)};var tu=function(e,t){this.state=e,this.lookAhead=t},tc=function(e,t,r,n){this.state=t,this.doc=e,this.line=r,this.maxLookAhead=n||0,this.baseTokens=null,this.baseTokenPos=1};function tf(e,t,r,n){var i=[e.state.modeGen],o={};t_(e,t.text,e.doc.mode,r,function(e,t){return i.push(e,t)},o,n);for(var l=r.state,s=0;s<e.state.overlays.length;++s)!function(n){r.baseTokens=i;var s=e.state.overlays[n],a=1,u=0;r.state=!0,t_(e,t.text,s.mode,r,function(e,t){for(var r=a;u<e;){var n=i[a];n>e&&i.splice(a,1,e,i[a+1],n),a+=2,u=Math.min(e,n)}if(t){if(s.opaque)i.splice(r,a-r,e,"overlay "+t),a=r+2;else for(;r<a;r+=2){var o=i[r+1];i[r+1]=(o?o+" ":"")+"overlay "+t}}},o),r.state=l,r.baseTokens=null,r.baseTokenPos=1}(s);return{styles:i,classes:o.bgClass||o.textClass?o:null}}function th(e,t,r){if(!t.styles||t.styles[0]!=e.state.modeGen){var n=td(e,eq(t)),i=t.text.length>e.options.maxHighlightLength&&eU(e.doc.mode,n.state),o=tf(e,t,n);i&&(n.state=i),t.stateAfter=n.save(!i),t.styles=o.styles,o.classes?t.styleClasses=o.classes:t.styleClasses&&(t.styleClasses=null),r===e.doc.highlightFrontier&&(e.doc.modeFrontier=Math.max(e.doc.modeFrontier,++e.doc.highlightFrontier))}return t.styles}function td(e,t,r){var n=e.doc,i=e.display;if(!n.mode.startState)return new tc(n,!0,t);var o=function e(t,r,n){for(var i,o,l=t.doc,s=n?-1:r-(t.doc.mode.innerMode?1e3:100),a=r;a>s;--a){if(a<=l.first)return l.first;var u=ej(l,a-1),c=u.stateAfter;if(c&&(!n||a+(c instanceof tu?c.lookAhead:0)<=l.modeFrontier))return a;var f=q(u.text,null,t.options.tabSize);(null==o||i>f)&&(o=a-1,i=f)}return o}(e,t,r),l=o>n.first&&ej(n,o-1).stateAfter,s=l?tc.fromSaved(n,l,o):new tc(n,eK(n.mode),o);return n.iter(o,t,function(r){tp(e,r.text,s);var n=s.line;r.stateAfter=n==t-1||n%5==0||n>=i.viewFrom&&n<i.viewTo?s.save():null,s.nextLine()}),r&&(n.modeFrontier=s.line),s}function tp(e,t,r,n){var i=e.doc.mode,o=new eX(t,e.options.tabSize,r);for(o.start=o.pos=n||0,""==t&&tg(i,r.state);!o.eol();)tv(i,o,r.state),o.start=o.pos}function tg(e,t){if(e.blankLine)return e.blankLine(t);if(e.innerMode){var r=eV(e,t);if(r.mode.blankLine)return r.mode.blankLine(r.state)}}function tv(e,t,r,n){for(var i=0;i<10;i++){n&&(n[0]=eV(e,r).mode);var o=e.token(t,r);if(t.pos>t.start)return o}throw Error("Mode "+e.name+" failed to advance stream.")}tc.prototype.lookAhead=function(e){var t=this.doc.getLine(this.line+e);return null!=t&&e>this.maxLookAhead&&(this.maxLookAhead=e),t},tc.prototype.baseToken=function(e){if(!this.baseTokens)return null;for(;this.baseTokens[this.baseTokenPos]<=e;)this.baseTokenPos+=2;var t=this.baseTokens[this.baseTokenPos+1];return{type:t&&t.replace(/( |^)overlay .*/,""),size:this.baseTokens[this.baseTokenPos]-e}},tc.prototype.nextLine=function(){this.line++,this.maxLookAhead>0&&this.maxLookAhead--},tc.fromSaved=function(e,t,r){return t instanceof tu?new tc(e,eU(e.mode,t.state),r,t.lookAhead):new tc(e,eU(e.mode,t),r)},tc.prototype.save=function(e){var t=!1!==e?eU(this.doc.mode,this.state):this.state;return this.maxLookAhead>0?new tu(t,this.maxLookAhead):t};var tm=function(e,t,r){this.start=e.start,this.end=e.pos,this.string=e.current(),this.type=t||null,this.state=r};function t$(e,t,r,n){var i,o=e.doc,l=o.mode;t=ts(o,t);var s,a=ej(o,t.line),u=td(e,t.line,r),c=new eX(a.text,e.options.tabSize,u);for(n&&(s=[]);(n||c.pos<t.ch)&&!c.eol();)c.start=c.pos,i=tv(l,c,u.state),n&&s.push(new tm(c,i,eU(o.mode,u.state)));return n?s:new tm(c,i,u.state)}function ty(e,t){if(e)for(;;){var r=e.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!r)break;e=e.slice(0,r.index)+e.slice(r.index+r[0].length);var n=r[1]?"bgClass":"textClass";null==t[n]?t[n]=r[2]:RegExp("(?:^|\\s)"+r[2]+"(?:$|\\s)").test(t[n])||(t[n]+=" "+r[2])}return e}function t_(e,t,r,n,i,o,l){var s=r.flattenSpans;null==s&&(s=e.options.flattenSpans);var a,u=0,c=null,f=new eX(t,e.options.tabSize,n),h=e.options.addModeClass&&[null];for(""==t&&ty(tg(r,n.state),o);!f.eol();){if(f.pos>e.options.maxHighlightLength?(s=!1,l&&tp(e,t,n,f.pos),f.pos=t.length,a=null):a=ty(tv(r,f,n.state,h),o),h){var d=h[0].name;d&&(a="m-"+(a?d+" "+a:d))}if(!s||c!=a){for(;u<f.start;)i(u=Math.min(f.start,u+5e3),c);c=a}f.start=f.pos}for(;u<f.pos;){var p=Math.min(f.pos,u+5e3);i(p,c),u=p}}var tb=!1,tx=!1;function tw(e,t,r){this.marker=e,this.from=t,this.to=r}function tC(e,t){if(e)for(var r=0;r<e.length;++r){var n=e[r];if(n.marker==t)return n}}function tS(e,t){for(var r,n=0;n<e.length;++n)e[n]!=t&&(r||(r=[])).push(e[n]);return r}function tL(e,t){if(t.full)return null;var r=eQ(e,t.from.line)&&ej(e,t.from.line).markedSpans,n=eQ(e,t.to.line)&&ej(e,t.to.line).markedSpans;if(!r&&!n)return null;var i=t.from.ch,o=t.to.ch,l=0==tt(t.from,t.to),s=function e(t,r,n){var i;if(t)for(var o=0;o<t.length;++o){var l=t[o],s=l.marker;if(null==l.from||(s.inclusiveLeft?l.from<=r:l.from<r)||l.from==r&&"bookmark"==s.type&&(!n||!l.marker.insertLeft)){var a=null==l.to||(s.inclusiveRight?l.to>=r:l.to>r);(i||(i=[])).push(new tw(s,l.from,a?null:l.to))}}return i}(r,i,l),a=function e(t,r,n){var i;if(t)for(var o=0;o<t.length;++o){var l=t[o],s=l.marker;if(null==l.to||(s.inclusiveRight?l.to>=r:l.to>r)||l.from==r&&"bookmark"==s.type&&(!n||l.marker.insertLeft)){var a=null==l.from||(s.inclusiveLeft?l.from<=r:l.from<r);(i||(i=[])).push(new tw(s,a?null:l.from-r,null==l.to?null:l.to-r))}}return i}(n,o,l),u=1==t.text.length,c=el(t.text).length+(u?i:0);if(s)for(var f=0;f<s.length;++f){var h=s[f];if(null==h.to){var d=tC(a,h.marker);d?u&&(h.to=null==d.to?null:d.to+c):h.to=i}}if(a)for(var p=0;p<a.length;++p){var g=a[p];(null!=g.to&&(g.to+=c),null==g.from)?!tC(s,g.marker)&&(g.from=c,u&&(s||(s=[])).push(g)):(g.from+=c,u&&(s||(s=[])).push(g))}s&&(s=tk(s)),a&&a!=s&&(a=tk(a));var v=[s];if(!u){var m,$=t.text.length-2;if($>0&&s)for(var y=0;y<s.length;++y)null==s[y].to&&(m||(m=[])).push(new tw(s[y].marker,null,null));for(var _=0;_<$;++_)v.push(m);v.push(a)}return v}function tk(e){for(var t=0;t<e.length;++t){var r=e[t];null!=r.from&&r.from==r.to&&!1!==r.marker.clearWhenEmpty&&e.splice(t--,1)}return e.length?e:null}function tT(e){var t=e.markedSpans;if(t){for(var r=0;r<t.length;++r)t[r].marker.detachLine(e);e.markedSpans=null}}function tN(e,t){if(t){for(var r=0;r<t.length;++r)t[r].marker.attachLine(e);e.markedSpans=t}}function tO(e){return e.inclusiveLeft?-1:0}function tM(e){return e.inclusiveRight?1:0}function tA(e,t){var r=e.lines.length-t.lines.length;if(0!=r)return r;var n=e.find(),i=t.find(),o=tt(n.from,i.from)||tO(e)-tO(t);if(o)return-o;var l=tt(n.to,i.to)||tM(e)-tM(t);return l||t.id-e.id}function t0(e,t){var r,n=tx&&e.markedSpans;if(n)for(var i=void 0,o=0;o<n.length;++o)(i=n[o]).marker.collapsed&&(t?i.from:i.to)==null&&(!r||0>tA(r,i.marker))&&(r=i.marker);return r}function tD(e){return t0(e,!0)}function tW(e){return t0(e,!1)}function tH(e,t){var r,n=tx&&e.markedSpans;if(n)for(var i=0;i<n.length;++i){var o=n[i];o.marker.collapsed&&(null==o.from||o.from<t)&&(null==o.to||o.to>t)&&(!r||0>tA(r,o.marker))&&(r=o.marker)}return r}function tF(e,t,r,n,i){var o=ej(e,t),l=tx&&o.markedSpans;if(l)for(var s=0;s<l.length;++s){var a=l[s];if(a.marker.collapsed){var u=a.marker.find(0),c=tt(u.from,r)||tO(a.marker)-tO(i),f=tt(u.to,n)||tM(a.marker)-tM(i);if((!(c>=0)||!(f<=0))&&(!(c<=0)||!(f>=0))&&(c<=0&&(a.marker.inclusiveRight&&i.inclusiveLeft?tt(u.to,r)>=0:tt(u.to,r)>0)||c>=0&&(a.marker.inclusiveRight&&i.inclusiveLeft?0>=tt(u.from,n):0>tt(u.from,n))))return!0}}}function tP(e){for(var t;t=tD(e);)e=t.find(-1,!0).line;return e}function t1(e,t){var r=ej(e,t),n=tP(r);return r==n?t:eq(n)}function tE(e,t){if(t>e.lastLine())return t;var r,n=ej(e,t);if(!tz(e,n))return t;for(;r=tW(n);)n=r.find(1,!0).line;return eq(n)+1}function tz(e,t){var r=tx&&t.markedSpans;if(r)for(var n=void 0,i=0;i<r.length;++i){if((n=r[i]).marker.collapsed){if(null==n.from||!n.marker.widgetNode&&0==n.from&&n.marker.inclusiveLeft&&tR(e,t,n))return!0}}}function tR(e,t,r){if(null==r.to){var n=r.marker.find(1,!0);return tR(e,n.line,tC(n.line.markedSpans,r.marker))}if(r.marker.inclusiveRight&&r.to==t.text.length)return!0;for(var i=void 0,o=0;o<t.markedSpans.length;++o)if((i=t.markedSpans[o]).marker.collapsed&&!i.marker.widgetNode&&i.from==r.to&&(null==i.to||i.to!=r.from)&&(i.marker.inclusiveLeft||r.marker.inclusiveRight)&&tR(e,t,i))return!0}function tI(e){e=tP(e);for(var t=0,r=e.parent,n=0;n<r.lines.length;++n){var i=r.lines[n];if(i==e)break;t+=i.height}for(var o=r.parent;o;o=(r=o).parent)for(var l=0;l<o.children.length;++l){var s=o.children[l];if(s==r)break;t+=s.height}return t}function t3(e){if(0==e.height)return 0;for(var t,r=e.text.length,n=e;t=tD(n);){var i=t.find(0,!0);n=i.from.line,r+=i.from.ch-i.to.ch}for(n=e;t=tW(n);){var o=t.find(0,!0);r-=n.text.length-o.from.ch,r+=(n=o.to.line).text.length-o.to.ch}return r}function tB(e){var t=e.display,r=e.doc;t.maxLine=ej(r,r.first),t.maxLineLength=t3(t.maxLine),t.maxLineChanged=!0,r.iter(function(e){var r=t3(e);r>t.maxLineLength&&(t.maxLineLength=r,t.maxLine=e)})}var t7=function(e,t,r){this.text=e,tN(this,t),this.height=r?r(this):1};function t4(e){e.parent=null,tT(e)}t7.prototype.lineNo=function(){return eq(this)},eO(t7);var t6={},t5={};function t2(e,t){if(!e||/^\s*$/.test(e))return null;var r=t.addModeClass?t5:t6;return r[e]||(r[e]=e.replace(/\S+/g,"cm-$&"))}function tG(e,t){var r=R("span",null,null,$?"padding-right: .1px":null),n={pre:R("pre",[r],"CodeMirror-line"),content:r,col:0,pos:0,cm:e,trailingSpace:!1,splitSpaces:e.getOption("lineWrapping")};t.measure={};for(var i=0;i<=(t.rest?t.rest.length:0);i++){var o=i?t.rest[i-1]:t.line,l=void 0;n.pos=0,n.addToken=tV,e1(e.display.measure)&&(l=eb(o,e.doc.direction))&&(n.addToken=tK(n.addToken,l)),n.map=[];var s=t!=e.display.externalMeasured&&eq(o);tj(o,n,th(e,o,s)),o.styleClasses&&(o.styleClasses.bgClass&&(n.bgClass=U(o.styleClasses.bgClass,n.bgClass||"")),o.styleClasses.textClass&&(n.textClass=U(o.styleClasses.textClass,n.textClass||""))),0==n.map.length&&n.map.push(0,0,n.content.appendChild(eP(e.display.measure))),0==i?(t.measure.map=n.map,t.measure.cache={}):((t.measure.maps||(t.measure.maps=[])).push(n.map),(t.measure.caches||(t.measure.caches=[])).push({}))}if($){var a=n.content.lastChild;(/\bcm-tab\b/.test(a.className)||a.querySelector&&a.querySelector(".cm-tab"))&&(n.content.className="cm-tab-wrap-hack")}return eL(e,"renderLine",e,t.line,n.pre),n.pre.className&&(n.textClass=U(n.pre.className,n.textClass||"")),n}function tU(e){var t=z("span","•","cm-invalidchar");return t.title="\\u"+e.charCodeAt(0).toString(16),t.setAttribute("aria-label",t.title),t}function tV(e,t,r,n,i,o,l){if(t){var s,a=e.splitSpaces?function e(t,r){if(t.length>1&&!/  /.test(t))return t;for(var n=r,i="",o=0;o<t.length;o++){var l=t.charAt(o);" "==l&&n&&(o==t.length-1||32==t.charCodeAt(o+1))&&(l="\xa0"),i+=l,n=" "==l}return i}(t,e.trailingSpace):t,u=e.cm.state.specialChars,c=!1;if(u.test(t)){s=document.createDocumentFragment();for(var f=0;;){u.lastIndex=f;var h=u.exec(t),d=h?h.index-f:t.length-f;if(d){var p=document.createTextNode(a.slice(f,f+d));v&&m<9?s.appendChild(z("span",[p])):s.appendChild(p),e.map.push(e.pos,e.pos+d,p),e.col+=d,e.pos+=d}if(!h)break;f+=d+1;var g=void 0;if("	"==h[0]){var $=e.cm.options.tabSize,y=$-e.col%$;(g=s.appendChild(z("span",eo(y),"cm-tab"))).setAttribute("role","presentation"),g.setAttribute("cm-text","	"),e.col+=y}else"\r"==h[0]||"\n"==h[0]?((g=s.appendChild(z("span","\r"==h[0]?"␍":"␤","cm-invalidchar"))).setAttribute("cm-text",h[0]),e.col+=1):((g=e.cm.options.specialCharPlaceholder(h[0])).setAttribute("cm-text",h[0]),v&&m<9?s.appendChild(z("span",[g])):s.appendChild(g),e.col+=1);e.map.push(e.pos,e.pos+1,g),e.pos++}}else e.col+=t.length,s=document.createTextNode(a),e.map.push(e.pos,e.pos+t.length,s),v&&m<9&&(c=!0),e.pos+=t.length;if(e.trailingSpace=32==a.charCodeAt(t.length-1),r||n||i||c||o||l){var _=r||"";n&&(_+=n),i&&(_+=i);var b=z("span",[s],_,o);if(l)for(var x in l)l.hasOwnProperty(x)&&"style"!=x&&"class"!=x&&b.setAttribute(x,l[x]);return e.content.appendChild(b)}e.content.appendChild(s)}}function tK(e,t){return function(r,n,i,o,l,s,a){i=i?i+" cm-force-border":"cm-force-border";for(var u=r.pos,c=u+n.length;;){for(var f=void 0,h=0;h<t.length&&(!((f=t[h]).to>u)||!(f.from<=u));h++);if(f.to>=c)return e(r,n,i,o,l,s,a);e(r,n.slice(0,f.to-u),i,o,null,s,a),o=null,n=n.slice(f.to-u),u=f.to}}}function tX(e,t,r,n){var i=!n&&r.widgetNode;i&&e.map.push(e.pos,e.pos+t,i),!n&&e.cm.display.input.needsContentAttribute&&(i||(i=e.content.appendChild(document.createElement("span"))),i.setAttribute("cm-marker",r.id)),i&&(e.cm.display.input.setUneditable(i),e.content.appendChild(i)),e.pos+=t,e.trailingSpace=!1}function tj(e,t,r){var n=e.markedSpans,i=e.text,o=0;if(!n){for(var l=1;l<r.length;l+=2)t.addToken(t,i.slice(o,o=r[l]),t2(r[l+1],t.cm.options));return}for(var s,a,u,c,f,h,d,p=i.length,g=0,v=1,m="",$=0;;){if($==g){u=c=f=a="",d=null,h=null,$=1/0;for(var y=[],_=void 0,b=0;b<n.length;++b){var x=n[b],w=x.marker;if("bookmark"==w.type&&x.from==g&&w.widgetNode)y.push(w);else if(x.from<=g&&(null==x.to||x.to>g||w.collapsed&&x.to==g&&x.from==g)){if(null!=x.to&&x.to!=g&&$>x.to&&($=x.to,c=""),w.className&&(u+=" "+w.className),w.css&&(a=(a?a+";":"")+w.css),w.startStyle&&x.from==g&&(f+=" "+w.startStyle),w.endStyle&&x.to==$&&(_||(_=[])).push(w.endStyle,x.to),w.title&&((d||(d={})).title=w.title),w.attributes)for(var C in w.attributes)(d||(d={}))[C]=w.attributes[C];w.collapsed&&(!h||0>tA(h.marker,w))&&(h=x)}else x.from>g&&$>x.from&&($=x.from)}if(_)for(var S=0;S<_.length;S+=2)_[S+1]==$&&(c+=" "+_[S]);if(!h||h.from==g)for(var L=0;L<y.length;++L)tX(t,0,y[L]);if(h&&(h.from||0)==g){if(tX(t,(null==h.to?p+1:h.to)-g,h.marker,null==h.from),null==h.to)return;h.to==g&&(h=!1)}}if(g>=p)break;for(var k=Math.min(p,$);;){if(m){var T=g+m.length;if(!h){var N=T>k?m.slice(0,k-g):m;t.addToken(t,N,s?s+u:u,f,g+N.length==$?c:"",a,d)}if(T>=k){m=m.slice(k-g),g=k;break}g=T,f=""}m=i.slice(o,o=r[v++]),s=t2(r[v++],t.cm.options)}}}function tY(e,t,r){this.line=t,this.rest=function e(t){for(var r,n;r=tW(t);)t=r.find(1,!0).line,(n||(n=[])).push(t);return n}(t),this.size=this.rest?eq(el(this.rest))-r+1:1,this.node=this.text=null,this.hidden=tz(e,t)}function t8(e,t,r){for(var n,i=[],o=t;o<r;o=n){var l=new tY(e.doc,ej(e.doc,o),o);n=o+l.size,i.push(l)}return i}var t9=null,tq=null;function tZ(e,t){var r=eC(e,t);if(r.length){var n,i=Array.prototype.slice.call(arguments,2);t9?n=t9.delayedCallbacks:tq?n=tq:(n=tq=[],setTimeout(tQ,0));for(var o=function(e){n.push(function(){return r[e].apply(null,i)})},l=0;l<r.length;++l)o(l)}}function tQ(){var e=tq;tq=null;for(var t=0;t<e.length;++t)e[t]()}function tJ(e,t,r,n){for(var i=0;i<t.changes.length;i++){var o=t.changes[i];"text"==o?rr(e,t):"gutter"==o?ri(e,t,r,n):"class"==o?rn(e,t):"widget"==o&&ro(e,t,n)}t.changes=null}function re(e){return e.node==e.text&&(e.node=z("div",null,null,"position: relative"),e.text.parentNode&&e.text.parentNode.replaceChild(e.node,e.text),e.node.appendChild(e.text),v&&m<8&&(e.node.style.zIndex=2)),e.node}function rt(e,t){var r=e.display.externalMeasured;return r&&r.line==t.line?(e.display.externalMeasured=null,t.measure=r.measure,r.built):tG(e,t)}function rr(e,t){var r=t.text.className,n=rt(e,t);t.text==t.node&&(t.node=n.pre),t.text.parentNode.replaceChild(n.pre,t.text),t.text=n.pre,n.bgClass!=t.bgClass||n.textClass!=t.textClass?(t.bgClass=n.bgClass,t.textClass=n.textClass,rn(e,t)):r&&(t.text.className=r)}function rn(e,t){!function e(t,r){var n=r.bgClass?r.bgClass+" "+(r.line.bgClass||""):r.line.bgClass;if(n&&(n+=" CodeMirror-linebackground"),r.background)n?r.background.className=n:(r.background.parentNode.removeChild(r.background),r.background=null);else if(n){var i=re(r);r.background=i.insertBefore(z("div",null,n),i.firstChild),t.display.input.setUneditable(r.background)}}(e,t),t.line.wrapClass?re(t).className=t.line.wrapClass:t.node!=t.text&&(t.node.className="");var r=t.textClass?t.textClass+" "+(t.line.textClass||""):t.line.textClass;t.text.className=r||""}function ri(e,t,r,n){if(t.gutter&&(t.node.removeChild(t.gutter),t.gutter=null),t.gutterBackground&&(t.node.removeChild(t.gutterBackground),t.gutterBackground=null),t.line.gutterClass){var i=re(t);t.gutterBackground=z("div",null,"CodeMirror-gutter-background "+t.line.gutterClass,"left: "+(e.options.fixedGutter?n.fixedPos:-n.gutterTotalWidth)+"px; width: "+n.gutterTotalWidth+"px"),e.display.input.setUneditable(t.gutterBackground),i.insertBefore(t.gutterBackground,t.text)}var o=t.line.gutterMarkers;if(e.options.lineNumbers||o){var l=re(t),s=t.gutter=z("div",null,"CodeMirror-gutter-wrapper","left: "+(e.options.fixedGutter?n.fixedPos:-n.gutterTotalWidth)+"px");if(s.setAttribute("aria-hidden","true"),e.display.input.setUneditable(s),l.insertBefore(s,t.text),t.line.gutterClass&&(s.className+=" "+t.line.gutterClass),!e.options.lineNumbers||o&&o["CodeMirror-linenumbers"]||(t.lineNumber=s.appendChild(z("div",eJ(e.options,r),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+n.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+e.display.lineNumInnerWidth+"px"))),o)for(var a=0;a<e.display.gutterSpecs.length;++a){var u=e.display.gutterSpecs[a].className,c=o.hasOwnProperty(u)&&o[u];c&&s.appendChild(z("div",[c],"CodeMirror-gutter-elt","left: "+n.gutterLeft[u]+"px; width: "+n.gutterWidth[u]+"px"))}}}function ro(e,t,r){t.alignable&&(t.alignable=null);for(var n=H("CodeMirror-linewidget"),i=t.node.firstChild,o=void 0;i;i=o)o=i.nextSibling,n.test(i.className)&&t.node.removeChild(i);rs(e,t,r)}function rl(e,t,r,n){var i=rt(e,t);return t.text=t.node=i.pre,i.bgClass&&(t.bgClass=i.bgClass),i.textClass&&(t.textClass=i.textClass),rn(e,t),ri(e,t,r,n),rs(e,t,n),t.node}function rs(e,t,r){if(ra(e,t.line,t,r,!0),t.rest)for(var n=0;n<t.rest.length;n++)ra(e,t.rest[n],t,r,!1)}function ra(e,t,r,n,i){if(t.widgets)for(var o=re(r),l=0,s=t.widgets;l<s.length;++l){var a=s[l],u=z("div",[a.node],"CodeMirror-linewidget"+(a.className?" "+a.className:""));a.handleMouseEvents||u.setAttribute("cm-ignore-events","true"),ru(a,u,r,n),e.display.input.setUneditable(u),i&&a.above?o.insertBefore(u,r.gutter||r.text):o.appendChild(u),tZ(a,"redraw")}}function ru(e,t,r,n){if(e.noHScroll){(r.alignable||(r.alignable=[])).push(t);var i=n.wrapperWidth;t.style.left=n.fixedPos+"px",e.coverGutter||(i-=n.gutterTotalWidth,t.style.paddingLeft=n.gutterTotalWidth+"px"),t.style.width=i+"px"}e.coverGutter&&(t.style.zIndex=5,t.style.position="relative",e.noHScroll||(t.style.marginLeft=-n.gutterTotalWidth+"px"))}function rc(e){if(null!=e.height)return e.height;var t=e.doc.cm;if(!t)return 0;if(!I(document.body,e.node)){var r="position: relative;";e.coverGutter&&(r+="margin-left: -"+t.display.gutters.offsetWidth+"px;"),e.noHScroll&&(r+="width: "+t.display.wrapper.clientWidth+"px;"),E(t.display.measure,z("div",[e.node],null,r))}return e.height=e.node.parentNode.offsetHeight}function rf(e,t){for(var r=eW(t);r!=e.wrapper;r=r.parentNode)if(!r||1==r.nodeType&&"true"==r.getAttribute("cm-ignore-events")||r.parentNode==e.sizer&&r!=e.mover)return!0}function rh(e){return e.lineSpace.offsetTop}function rd(e){return e.mover.offsetHeight-e.lineSpace.offsetHeight}function rp(e){if(e.cachedPaddingH)return e.cachedPaddingH;var t=E(e.measure,z("pre","x","CodeMirror-line-like")),r=window.getComputedStyle?window.getComputedStyle(t):t.currentStyle,n={left:parseInt(r.paddingLeft),right:parseInt(r.paddingRight)};return isNaN(n.left)||isNaN(n.right)||(e.cachedPaddingH=n),n}function rg(e){return 50-e.display.nativeBarWidth}function rv(e){return e.display.scroller.clientWidth-rg(e)-e.display.barWidth}function rm(e){return e.display.scroller.clientHeight-rg(e)-e.display.barHeight}function r$(e,t,r){if(e.line==t)return{map:e.measure.map,cache:e.measure.cache};if(e.rest){for(var n=0;n<e.rest.length;n++)if(e.rest[n]==t)return{map:e.measure.maps[n],cache:e.measure.caches[n]};for(var i=0;i<e.rest.length;i++)if(eq(e.rest[i])>r)return{map:e.measure.maps[i],cache:e.measure.caches[i],before:!0}}}function ry(e,t,r,n){return rx(e,rb(e,t),r,n)}function r_(e,t){if(t>=e.display.viewFrom&&t<e.display.viewTo)return e.display.view[rU(e,t)];var r=e.display.externalMeasured;if(r&&t>=r.lineN&&t<r.lineN+r.size)return r}function rb(e,t){var r,n,i,o,l,s=eq(t),a=r_(e,s);a&&!a.text?a=null:a&&a.changes&&(tJ(e,a,s,r4(e)),e.curOp.forceUpdate=!0),!a&&(a=(r=e,n=t,n=tP(n),i=eq(n),(o=r.display.externalMeasured=new tY(r.doc,n,i)).lineN=i,l=o.built=tG(r,o),o.text=l.pre,E(r.display.lineMeasure,l.pre),o));var u=r$(a,t,s);return{line:t,view:a,rect:null,map:u.map,cache:u.cache,before:u.before,hasHeights:!1}}function rx(e,t,r,n,o){t.before&&(r=-1);var l,s=r+(n||"");return t.cache.hasOwnProperty(s)?l=t.cache[s]:(t.rect||(t.rect=t.view.text.getBoundingClientRect()),t.hasHeights||(function e(t,r,n){var i=t.options.lineWrapping,o=i&&rv(t);if(!r.measure.heights||i&&r.measure.width!=o){var l=r.measure.heights=[];if(i){r.measure.width=o;for(var s=r.text.firstChild.getClientRects(),a=0;a<s.length-1;a++){var u=s[a],c=s[a+1];Math.abs(u.bottom-c.bottom)>2&&l.push((u.bottom+c.top)/2-n.top)}}l.push(n.bottom-n.top)}}(e,t.view,t.rect),t.hasHeights=!0),(l=function e(t,r,n,o){var l,s,a=rC(r.map,n,o),u=a.node,c=a.start,f=a.end,h=a.collapse;if(3==u.nodeType){for(var d=0;d<4;d++){for(;c&&eg(r.line.text.charAt(a.coverStart+c));)--c;for(;a.coverStart+f<a.coverEnd&&eg(r.line.text.charAt(a.coverStart+f));)++f;if((l=v&&m<9&&0==c&&f==a.coverEnd-a.coverStart?u.parentNode.getBoundingClientRect():rS(i(u,c,f).getClientRects(),o)).left||l.right||0==c)break;f=c,c-=1,h="right"}v&&m<11&&(l=function e(t,r){if(!window.screen||null==screen.logicalXDPI||screen.logicalXDPI==screen.deviceXDPI||!function e(t){if(null!=e3)return e3;var r=E(t,z("span","x")),n=r.getBoundingClientRect(),o=i(r,0,1).getBoundingClientRect();return e3=Math.abs(n.left-o.left)>1}(t))return r;var n=screen.logicalXDPI/screen.deviceXDPI,o=screen.logicalYDPI/screen.deviceYDPI;return{left:r.left*n,right:r.right*n,top:r.top*o,bottom:r.bottom*o}}(t.display.measure,l))}else c>0&&(h=o="right"),l=t.options.lineWrapping&&(s=u.getClientRects()).length>1?s["right"==o?s.length-1:0]:u.getBoundingClientRect();if(v&&m<9&&!c&&(!l||!l.left&&!l.right)){var p=u.parentNode.getClientRects()[0];l=p?{left:p.left,right:p.left+r7(t.display),top:p.top,bottom:p.bottom}:rw}for(var g=l.top-r.rect.top,$=l.bottom-r.rect.top,y=(g+$)/2,_=r.view.measure.heights,b=0;b<_.length-1&&!(y<_[b]);b++);var x=b?_[b-1]:0,w=_[b],C={left:("right"==h?l.right:l.left)-r.rect.left,right:("left"==h?l.left:l.right)-r.rect.left,top:x,bottom:w};return l.left||l.right||(C.bogus=!0),t.options.singleCursorHeightPerLine||(C.rtop=g,C.rbottom=$),C}(e,t,r,n)).bogus||(t.cache[s]=l)),{left:l.left,right:l.right,top:o?l.rtop:l.top,bottom:o?l.rbottom:l.bottom}}var rw={left:0,right:0,top:0,bottom:0};function rC(e,t,r){for(var n,i,o,l,s,a,u=0;u<e.length;u+=3)if(s=e[u],a=e[u+1],t<s?(i=0,o=1,l="left"):t<a?o=(i=t-s)+1:(u==e.length-3||t==a&&e[u+3]>t)&&(i=(o=a-s)-1,t>=a&&(l="right")),null!=i){if(n=e[u+2],s==a&&r==(n.insertLeft?"left":"right")&&(l=r),"left"==r&&0==i)for(;u&&e[u-2]==e[u-3]&&e[u-1].insertLeft;)n=e[(u-=3)+2],l="left";if("right"==r&&i==a-s)for(;u<e.length-3&&e[u+3]==e[u+4]&&!e[u+5].insertLeft;)n=e[(u+=3)+2],l="right";break}return{node:n,start:i,end:o,collapse:l,coverStart:s,coverEnd:a}}function rS(e,t){var r=rw;if("left"==t)for(var n=0;n<e.length&&(r=e[n]).left==r.right;n++);else for(var i=e.length-1;i>=0&&(r=e[i]).left==r.right;i--);return r}function rL(e){if(e.measure&&(e.measure.cache={},e.measure.heights=null,e.rest))for(var t=0;t<e.rest.length;t++)e.measure.caches[t]={}}function rk(e){e.display.externalMeasure=null,P(e.display.lineMeasure);for(var t=0;t<e.display.view.length;t++)rL(e.display.view[t])}function rT(e){rk(e),e.display.cachedCharWidth=e.display.cachedTextHeight=e.display.cachedPaddingH=null,e.options.lineWrapping||(e.display.maxLineChanged=!0),e.display.lineNumChars=null}function rN(e){return _&&k?-(e.body.getBoundingClientRect().left-parseInt(getComputedStyle(e.body).marginLeft)):e.defaultView.pageXOffset||(e.documentElement||e.body).scrollLeft}function rO(e){return _&&k?-(e.body.getBoundingClientRect().top-parseInt(getComputedStyle(e.body).marginTop)):e.defaultView.pageYOffset||(e.documentElement||e.body).scrollTop}function rM(e){var t=tP(e).widgets,r=0;if(t)for(var n=0;n<t.length;++n)t[n].above&&(r+=rc(t[n]));return r}function rA(e,t,r,n,i){if(!i){var o=rM(t);r.top+=o,r.bottom+=o}if("line"==n)return r;n||(n="local");var l=tI(t);if("local"==n?l+=rh(e.display):l-=e.display.viewOffset,"page"==n||"window"==n){var s=e.display.lineSpace.getBoundingClientRect();l+=s.top+("window"==n?0:rO(K(e)));var a=s.left+("window"==n?0:rN(K(e)));r.left+=a,r.right+=a}return r.top+=l,r.bottom+=l,r}function r0(e,t,r){if("div"==r)return t;var n=t.left,i=t.top;if("page"==r)n-=rN(K(e)),i-=rO(K(e));else if("local"==r||!r){var o=e.display.sizer.getBoundingClientRect();n+=o.left,i+=o.top}var l=e.display.lineSpace.getBoundingClientRect();return{left:n-l.left,top:i-l.top}}function rD(e,t,r,n,i){return n||(n=ej(e.doc,t.line)),rA(e,n,ry(e,n,t.ch,i),r)}function rW(e,t,r,n,i,o){function l(t,l){var s=rx(e,i,t,l?"right":"left",o);return l?s.left=s.right:s.right=s.left,rA(e,n,s,r)}n=n||ej(e.doc,t.line),i||(i=rb(e,n));var s=eb(n,e.doc.direction),a=t.ch,u=t.sticky;if(a>=n.text.length?(a=n.text.length,u="before"):a<=0&&(a=0,u="after"),!s)return l("before"==u?a-1:a,"before"==u);function c(e,t,r){return l(r?e-1:e,1==s[t].level!=r)}var f=ey(s,a,u),h=e$,d=c(a,f,"before"==u);return null!=h&&(d.other=c(a,h,"before"!=u)),d}function rH(e,t){var r=0;t=ts(e.doc,t),e.options.lineWrapping||(r=r7(e.display)*t.ch);var n=ej(e.doc,t.line),i=tI(n)+rh(e.display);return{left:r,right:r,top:i,bottom:i+n.height}}function rF(e,t,r,n,i){var o=te(e,t,r);return o.xRel=i,n&&(o.outside=n),o}function rP(e,t,r){var n=e.doc;if((r+=e.display.viewOffset)<0)return rF(n.first,0,null,-1,-1);var i=eZ(n,r),o=n.first+n.size-1;if(i>o)return rF(n.first+n.size-1,ej(n,o).text.length,null,1,1);t<0&&(t=0);for(var l=ej(n,i);;){var s=rR(e,l,i,t,r),a=tH(l,s.ch+(s.xRel>0||s.outside>0?1:0));if(!a)return s;var u=a.find(1);if(u.line==i)return u;l=ej(n,i=u.line)}}function r1(e,t,r,n){n-=rM(t);var i=t.text.length,o=em(function(t){return rx(e,r,t-1).bottom<=n},i,0);return i=em(function(t){return rx(e,r,t).top>n},o,i),{begin:o,end:i}}function rE(e,t,r,n){r||(r=rb(e,t));var i=rA(e,t,rx(e,r,n),"line").top;return r1(e,t,r,i)}function rz(e,t,r,n){return!(e.bottom<=r)&&(e.top>r||(n?e.left:e.right)>t)}function rR(e,t,r,n,i){i-=tI(t);var o=rb(e,t),l=rM(t),s=0,a=t.text.length,u=!0,c=eb(t,e.doc.direction);if(c){var f=(e.options.lineWrapping?r3:rI)(e,t,r,o,c,n,i);s=(u=1!=f.level)?f.from:f.to-1,a=u?f.to:f.from-1}var h,d,p=null,g=null,v=em(function(t){var r=rx(e,o,t);return r.top+=l,r.bottom+=l,!!rz(r,n,i,!1)&&(r.top<=i&&r.left<=n&&(p=t,g=r),!0)},s,a),m=!1;if(g){var $=n-g.left<g.right-n,y=$==u;v=p+(y?0:1),d=y?"after":"before",h=$?g.left:g.right}else{!u&&(v==a||v==s)&&v++,d=0==v?"after":v==t.text.length?"before":rx(e,o,v-(u?1:0)).bottom+l<=i==u?"after":"before";var _=rW(e,te(r,v,d),"line",t,o);h=_.left,m=i<_.top?-1:i>=_.bottom?1:0}return v=ev(t.text,v,1),rF(r,v,d,m,n-h)}function rI(e,t,r,n,i,o,l){var s=em(function(s){var a=i[s],u=1!=a.level;return rz(rW(e,te(r,u?a.to:a.from,u?"before":"after"),"line",t,n),o,l,!0)},0,i.length-1),a=i[s];if(s>0){var u=1!=a.level,c=rW(e,te(r,u?a.from:a.to,u?"after":"before"),"line",t,n);rz(c,o,l,!0)&&c.top>l&&(a=i[s-1])}return a}function r3(e,t,r,n,i,o,l){var s=r1(e,t,n,l),a=s.begin,u=s.end;/\s/.test(t.text.charAt(u-1))&&u--;for(var c=null,f=null,h=0;h<i.length;h++){var d=i[h];if(!(d.from>=u)&&!(d.to<=a)){var p=rx(e,n,1!=d.level?Math.min(u,d.to)-1:Math.max(a,d.from)).right,g=p<o?o-p+1e9:p-o;(!c||f>g)&&(c=d,f=g)}}return c||(c=i[i.length-1]),c.from<a&&(c={from:a,to:c.to,level:c.level}),c.to>u&&(c={from:c.from,to:u,level:c.level}),c}function rB(e){if(null!=e.cachedTextHeight)return e.cachedTextHeight;if(null==s){s=z("pre",null,"CodeMirror-line-like");for(var t=0;t<49;++t)s.appendChild(document.createTextNode("x")),s.appendChild(z("br"));s.appendChild(document.createTextNode("x"))}E(e.measure,s);var r=s.offsetHeight/50;return r>3&&(e.cachedTextHeight=r),P(e.measure),r||1}function r7(e){if(null!=e.cachedCharWidth)return e.cachedCharWidth;var t=z("span","xxxxxxxxxx"),r=z("pre",[t],"CodeMirror-line-like");E(e.measure,r);var n=t.getBoundingClientRect(),i=(n.right-n.left)/10;return i>2&&(e.cachedCharWidth=i),i||10}function r4(e){for(var t=e.display,r={},n={},i=t.gutters.clientLeft,o=t.gutters.firstChild,l=0;o;o=o.nextSibling,++l){var s=e.display.gutterSpecs[l].className;r[s]=o.offsetLeft+o.clientLeft+i,n[s]=o.clientWidth}return{fixedPos:r6(t),gutterTotalWidth:t.gutters.offsetWidth,gutterLeft:r,gutterWidth:n,wrapperWidth:t.wrapper.clientWidth}}function r6(e){return e.scroller.getBoundingClientRect().left-e.sizer.getBoundingClientRect().left}function r5(e){var t=rB(e.display),r=e.options.lineWrapping,n=r&&Math.max(5,e.display.scroller.clientWidth/r7(e.display)-3);return function(i){if(tz(e.doc,i))return 0;var o=0;if(i.widgets)for(var l=0;l<i.widgets.length;l++)i.widgets[l].height&&(o+=i.widgets[l].height);return r?o+(Math.ceil(i.text.length/n)||1)*t:o+t}}function r2(e){var t=e.doc,r=r5(e);t.iter(function(e){var t=r(e);t!=e.height&&e9(e,t)})}function rG(e,t,r,n){var i=e.display;if(!r&&"true"==eW(t).getAttribute("cm-not-content"))return null;var o,l,s=i.lineSpace.getBoundingClientRect();try{o=t.clientX-s.left,l=t.clientY-s.top}catch(a){return null}var u,c=rP(e,o,l);if(n&&c.xRel>0&&(u=ej(e.doc,c.line).text).length==c.ch){var f=q(u,u.length,e.options.tabSize)-u.length;c=te(c.line,Math.max(0,Math.round((o-rp(e.display).left)/r7(e.display))-f))}return c}function rU(e,t){if(t>=e.display.viewTo||(t-=e.display.viewFrom)<0)return null;for(var r=e.display.view,n=0;n<r.length;n++)if((t-=r[n].size)<0)return n}function rV(e,t,r,n){null==t&&(t=e.doc.first),null==r&&(r=e.doc.first+e.doc.size),n||(n=0);var i=e.display;if(n&&r<i.viewTo&&(null==i.updateLineNumbers||i.updateLineNumbers>t)&&(i.updateLineNumbers=t),e.curOp.viewChanged=!0,t>=i.viewTo)tx&&t1(e.doc,t)<i.viewTo&&rX(e);else if(r<=i.viewFrom)tx&&tE(e.doc,r+n)>i.viewFrom?rX(e):(i.viewFrom+=n,i.viewTo+=n);else if(t<=i.viewFrom&&r>=i.viewTo)rX(e);else if(t<=i.viewFrom){var o=rj(e,r,r+n,1);o?(i.view=i.view.slice(o.index),i.viewFrom=o.lineN,i.viewTo+=n):rX(e)}else if(r>=i.viewTo){var l=rj(e,t,t,-1);l?(i.view=i.view.slice(0,l.index),i.viewTo=l.lineN):rX(e)}else{var s=rj(e,t,t,-1),a=rj(e,r,r+n,1);s&&a?(i.view=i.view.slice(0,s.index).concat(t8(e,s.lineN,a.lineN)).concat(i.view.slice(a.index)),i.viewTo+=n):rX(e)}var u=i.externalMeasured;u&&(r<u.lineN?u.lineN+=n:t<u.lineN+u.size&&(i.externalMeasured=null))}function rK(e,t,r){e.curOp.viewChanged=!0;var n=e.display,i=e.display.externalMeasured;if(i&&t>=i.lineN&&t<i.lineN+i.size&&(n.externalMeasured=null),!(t<n.viewFrom)&&!(t>=n.viewTo)){var o=n.view[rU(e,t)];if(null!=o.node){var l=o.changes||(o.changes=[]);-1==Q(l,r)&&l.push(r)}}}function rX(e){e.display.viewFrom=e.display.viewTo=e.doc.first,e.display.view=[],e.display.viewOffset=0}function rj(e,t,r,n){var i,o=rU(e,t),l=e.display.view;if(!tx||r==e.doc.first+e.doc.size)return{index:o,lineN:r};for(var s=e.display.viewFrom,a=0;a<o;a++)s+=l[a].size;if(s!=t){if(n>0){if(o==l.length-1)return null;i=s+l[o].size-t,o++}else i=s-t;t+=i,r+=i}for(;t1(e.doc,r)!=r;){if(o==(n<0?0:l.length-1))return null;r+=n*l[o-(n<0?1:0)].size,o+=n}return{index:o,lineN:r}}function rY(e){for(var t=e.display.view,r=0,n=0;n<t.length;n++){var i=t[n];!i.hidden&&(!i.node||i.changes)&&++r}return r}function r8(e){e.display.input.showSelection(e.display.input.prepareSelection())}function r9(e,t){void 0===t&&(t=!0);var r=e.doc,n={},i=n.cursors=document.createDocumentFragment(),o=n.selection=document.createDocumentFragment(),l=e.options.$customCursor;l&&(t=!0);for(var s=0;s<r.sel.ranges.length;s++){if(t||s!=r.sel.primIndex){var a=r.sel.ranges[s];if(!(a.from().line>=e.display.viewTo)&&!(a.to().line<e.display.viewFrom)){var u=a.empty();if(l){var c=l(e,a);c&&rq(e,c,i)}else(u||e.options.showCursorWhenSelecting)&&rq(e,a.head,i);u||rQ(e,a,o)}}}return n}function rq(e,t,r){var n=rW(e,t,"div",null,null,!e.options.singleCursorHeightPerLine),i=r.appendChild(z("div","\xa0","CodeMirror-cursor"));if(i.style.left=n.left+"px",i.style.top=n.top+"px",i.style.height=Math.max(0,n.bottom-n.top)*e.options.cursorHeight+"px",/\bcm-fat-cursor\b/.test(e.getWrapperElement().className)){var o=rD(e,t,"div",null,null),l=o.right-o.left;i.style.width=(l>0?l:e.defaultCharWidth())+"px"}if(n.other){var s=r.appendChild(z("div","\xa0","CodeMirror-cursor CodeMirror-secondarycursor"));s.style.display="",s.style.left=n.other.left+"px",s.style.top=n.other.top+"px",s.style.height=(n.other.bottom-n.other.top)*.85+"px"}}function rZ(e,t){return e.top-t.top||e.left-t.left}function rQ(e,t,r){var n=e.display,i=e.doc,o=document.createDocumentFragment(),l=rp(e.display),s=l.left,a=Math.max(n.sizerWidth,rv(e)-n.sizer.offsetLeft)-l.right,u="ltr"==i.direction;function c(e,t,r,n){t<0&&(t=0),t=Math.round(t),n=Math.round(n),o.appendChild(z("div",null,"CodeMirror-selected","position: absolute; left: "+e+"px;\n                             top: "+t+"px; width: "+(null==r?a-e:r)+"px;\n                             height: "+(n-t)+"px"))}function f(t,r,n){var o,l,f=ej(i,t),h=f.text.length;function d(r,n){return rD(e,te(t,r),"div",f,n)}function p(t,r,n){var i=rE(e,f,null,t),o="ltr"==r==("after"==n)?"left":"right";return d("after"==n?i.begin:i.end-(/\s/.test(f.text.charAt(i.end-1))?2:1),o)[o]}var g=eb(f,i.direction);return!function e(t,r,n,i){if(!t)return i(r,n,"ltr",0);for(var o=!1,l=0;l<t.length;++l){var s=t[l];(s.from<n&&s.to>r||r==n&&s.to==r)&&(i(Math.max(s.from,r),Math.min(s.to,n),1==s.level?"rtl":"ltr",l),o=!0)}o||i(r,n,"ltr")}(g,r||0,null==n?h:n,function(e,t,i,f){var v,m,$,y,_="ltr"==i,b=d(e,_?"left":"right"),x=d(t-1,_?"right":"left"),w=null==r&&0==e,C=null==n&&t==h,S=0==f,L=!g||f==g.length-1;if(x.top-b.top<=3){var k=(u?w:C)&&S?s:(_?b:x).left,T=(u?C:w)&&L?a:(_?x:b).right;c(k,b.top,T-k,b.bottom)}else _?(v=u&&w&&S?s:b.left,m=u?a:p(e,i,"before"),$=u?s:p(t,i,"after"),y=u&&C&&L?a:x.right):(v=u?p(e,i,"before"):s,m=!u&&w&&S?a:b.right,$=!u&&C&&L?s:x.left,y=u?p(t,i,"after"):a),c(v,b.top,m-v,b.bottom),b.bottom<x.top&&c(s,b.bottom,null,x.top),c($,x.top,y-$,x.bottom);(!o||0>rZ(b,o))&&(o=b),0>rZ(x,o)&&(o=x),(!l||0>rZ(b,l))&&(l=b),0>rZ(x,l)&&(l=x)}),{start:o,end:l}}var h=t.from(),d=t.to();if(h.line==d.line)f(h.line,h.ch,d.ch);else{var p=ej(i,h.line),g=ej(i,d.line),v=tP(p)==tP(g),m=f(h.line,h.ch,v?p.text.length+1:null).end,$=f(d.line,v?0:null,d.ch).start;v&&(m.top<$.top-2?(c(m.right,m.top,null,m.bottom),c(s,$.top,$.left,$.bottom)):c(m.right,m.top,$.left-m.right,m.bottom)),m.bottom<$.top&&c(s,m.bottom,null,$.top)}r.appendChild(o)}function rJ(e){if(e.state.focused){var t=e.display;clearInterval(t.blinker);var r=!0;t.cursorDiv.style.visibility="",e.options.cursorBlinkRate>0?t.blinker=setInterval(function(){e.hasFocus()||nn(e),t.cursorDiv.style.visibility=(r=!r)?"":"hidden"},e.options.cursorBlinkRate):e.options.cursorBlinkRate<0&&(t.cursorDiv.style.visibility="hidden")}}function ne(e){e.hasFocus()||(e.display.input.focus(),e.state.focused||nr(e))}function nt(e){e.state.delayingBlurEvent=!0,setTimeout(function(){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1,e.state.focused&&nn(e))},100)}function nr(e,t){e.state.delayingBlurEvent&&!e.state.draggingText&&(e.state.delayingBlurEvent=!1),"nocursor"!=e.options.readOnly&&(e.state.focused||(eL(e,"focus",e,t),e.state.focused=!0,G(e.display.wrapper,"CodeMirror-focused"),!e.curOp&&e.display.selForContextMenu!=e.doc.sel&&(e.display.input.reset(),$&&setTimeout(function(){return e.display.input.reset(!0)},20)),e.display.input.receivedFocus()),rJ(e))}function nn(e,t){!e.state.delayingBlurEvent&&(e.state.focused&&(eL(e,"blur",e,t),e.state.focused=!1,F(e.display.wrapper,"CodeMirror-focused")),clearInterval(e.display.blinker),setTimeout(function(){e.state.focused||(e.display.shift=!1)},150))}function ni(e){for(var t=e.display,r=t.lineDiv.offsetTop,n=Math.max(0,t.scroller.getBoundingClientRect().top),i=t.lineDiv.getBoundingClientRect().top,o=0,l=0;l<t.view.length;l++){var s=t.view[l],a=e.options.lineWrapping,u=void 0,c=0;if(!s.hidden){if(i+=s.line.height,v&&m<8){var f=s.node.offsetTop+s.node.offsetHeight;u=f-r,r=f}else{var h=s.node.getBoundingClientRect();u=h.bottom-h.top,!a&&s.text.firstChild&&(c=s.text.firstChild.getBoundingClientRect().right-h.left-1)}var d=s.line.height-u;if((d>.005||d<-.005)&&(i<n&&(o-=d),e9(s.line,u),no(s.line),s.rest))for(var p=0;p<s.rest.length;p++)no(s.rest[p]);if(c>e.display.sizerWidth){var g=Math.ceil(c/r7(e.display));g>e.display.maxLineLength&&(e.display.maxLineLength=g,e.display.maxLine=s.line,e.display.maxLineChanged=!0)}}}Math.abs(o)>2&&(t.scroller.scrollTop+=o)}function no(e){if(e.widgets)for(var t=0;t<e.widgets.length;++t){var r=e.widgets[t],n=r.node.parentNode;n&&(r.height=n.offsetHeight)}}function nl(e,t,r){var n=r&&null!=r.top?Math.max(0,r.top):e.scroller.scrollTop;n=Math.floor(n-rh(e));var i=r&&null!=r.bottom?r.bottom:n+e.wrapper.clientHeight,o=eZ(t,n),l=eZ(t,i);if(r&&r.ensure){var s=r.ensure.from.line,a=r.ensure.to.line;s<o?(o=s,l=eZ(t,tI(ej(t,s))+e.wrapper.clientHeight)):Math.min(a,t.lastLine())>=l&&(o=eZ(t,tI(ej(t,a))-e.wrapper.clientHeight),l=a)}return{from:o,to:Math.max(l,o+1)}}function ns(e,t){var r=e.display,n=rB(e.display);t.top<0&&(t.top=0);var i=e.curOp&&null!=e.curOp.scrollTop?e.curOp.scrollTop:r.scroller.scrollTop,o=rm(e),l={};t.bottom-t.top>o&&(t.bottom=t.top+o);var s=e.doc.height+rd(r),a=t.top<n,u=t.bottom>s-n;if(t.top<i)l.scrollTop=a?0:t.top;else if(t.bottom>i+o){var c=Math.min(t.top,(u?s:t.bottom)-o);c!=i&&(l.scrollTop=c)}var f=e.options.fixedGutter?0:r.gutters.offsetWidth,h=e.curOp&&null!=e.curOp.scrollLeft?e.curOp.scrollLeft:r.scroller.scrollLeft-f,d=rv(e)-r.gutters.offsetWidth,p=t.right-t.left>d;return p&&(t.right=t.left+d),t.left<10?l.scrollLeft=0:t.left<h?l.scrollLeft=Math.max(0,t.left+f-(p?0:10)):t.right>d+h-3&&(l.scrollLeft=t.right+(p?0:10)-d),l}function na(e,t){null!=t&&(nf(e),e.curOp.scrollTop=(null==e.curOp.scrollTop?e.doc.scrollTop:e.curOp.scrollTop)+t)}function nu(e){nf(e);var t=e.getCursor();e.curOp.scrollToPos={from:t,to:t,margin:e.options.cursorScrollMargin}}function nc(e,t,r){(null!=t||null!=r)&&nf(e),null!=t&&(e.curOp.scrollLeft=t),null!=r&&(e.curOp.scrollTop=r)}function nf(e){var t=e.curOp.scrollToPos;if(t){e.curOp.scrollToPos=null;var r=rH(e,t.from),n=rH(e,t.to);nh(e,r,n,t.margin)}}function nh(e,t,r,n){var i=ns(e,{left:Math.min(t.left,r.left),top:Math.min(t.top,r.top)-n,right:Math.max(t.right,r.right),bottom:Math.max(t.bottom,r.bottom)+n});nc(e,i.scrollLeft,i.scrollTop)}function nd(e,t){!(2>Math.abs(e.doc.scrollTop-t))&&(h||nE(e,{top:t}),np(e,t,!0),h&&nE(e),nW(e,100))}function np(e,t,r){t=Math.max(0,Math.min(e.display.scroller.scrollHeight-e.display.scroller.clientHeight,t)),(e.display.scroller.scrollTop!=t||r)&&(e.doc.scrollTop=t,e.display.scrollbars.setScrollTop(t),e.display.scroller.scrollTop!=t&&(e.display.scroller.scrollTop=t))}function ng(e,t,r,n){t=Math.max(0,Math.min(t,e.display.scroller.scrollWidth-e.display.scroller.clientWidth)),((r?t!=e.doc.scrollLeft:!(2>Math.abs(e.doc.scrollLeft-t)))||n)&&(e.doc.scrollLeft=t,nI(e),e.display.scroller.scrollLeft!=t&&(e.display.scroller.scrollLeft=t),e.display.scrollbars.setScrollLeft(t))}function nv(e){var t=e.display,r=t.gutters.offsetWidth,n=Math.round(e.doc.height+rd(e.display));return{clientHeight:t.scroller.clientHeight,viewHeight:t.wrapper.clientHeight,scrollWidth:t.scroller.scrollWidth,clientWidth:t.scroller.clientWidth,viewWidth:t.wrapper.clientWidth,barLeft:e.options.fixedGutter?r:0,docHeight:n,scrollHeight:n+rg(e)+t.barHeight,nativeBarWidth:t.nativeBarWidth,gutterWidth:r}}var nm=function(e,t,r){this.cm=r;var n=this.vert=z("div",[z("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),i=this.horiz=z("div",[z("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");n.tabIndex=i.tabIndex=-1,e(n),e(i),ew(n,"scroll",function(){n.clientHeight&&t(n.scrollTop,"vertical")}),ew(i,"scroll",function(){i.clientWidth&&t(i.scrollLeft,"horizontal")}),this.checkedZeroWidth=!1,v&&m<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")};nm.prototype.update=function(e){var t=e.scrollWidth>e.clientWidth+1,r=e.scrollHeight>e.clientHeight+1,n=e.nativeBarWidth;if(r){this.vert.style.display="block",this.vert.style.bottom=t?n+"px":"0";var i=e.viewHeight-(t?n:0);this.vert.firstChild.style.height=Math.max(0,e.scrollHeight-e.clientHeight+i)+"px"}else this.vert.scrollTop=0,this.vert.style.display="",this.vert.firstChild.style.height="0";if(t){this.horiz.style.display="block",this.horiz.style.right=r?n+"px":"0",this.horiz.style.left=e.barLeft+"px";var o=e.viewWidth-e.barLeft-(r?n:0);this.horiz.firstChild.style.width=Math.max(0,e.scrollWidth-e.clientWidth+o)+"px"}else this.horiz.style.display="",this.horiz.firstChild.style.width="0";return!this.checkedZeroWidth&&e.clientHeight>0&&(0==n&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:r?n:0,bottom:t?n:0}},nm.prototype.setScrollLeft=function(e){this.horiz.scrollLeft!=e&&(this.horiz.scrollLeft=e),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz,"horiz")},nm.prototype.setScrollTop=function(e){this.vert.scrollTop!=e&&(this.vert.scrollTop=e),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert,"vert")},nm.prototype.zeroWidthHack=function(){this.horiz.style.height=this.vert.style.width=N&&!C?"12px":"18px",this.horiz.style.visibility=this.vert.style.visibility="hidden",this.disableHoriz=new Z,this.disableVert=new Z},nm.prototype.enableZeroWidthBar=function(e,t,r){function n(){var i=e.getBoundingClientRect();("vert"==r?document.elementFromPoint(i.right-1,(i.top+i.bottom)/2):document.elementFromPoint((i.right+i.left)/2,i.bottom-1))!=e?e.style.visibility="hidden":t.set(1e3,n)}e.style.visibility="",t.set(1e3,n)},nm.prototype.clear=function(){var e=this.horiz.parentNode;e.removeChild(this.horiz),e.removeChild(this.vert)};var n$=function(){};function ny(e,t){t||(t=nv(e));var r=e.display.barWidth,n=e.display.barHeight;n_(e,t);for(var i=0;i<4&&r!=e.display.barWidth||n!=e.display.barHeight;i++)r!=e.display.barWidth&&e.options.lineWrapping&&ni(e),n_(e,nv(e)),r=e.display.barWidth,n=e.display.barHeight}function n_(e,t){var r=e.display,n=r.scrollbars.update(t);r.sizer.style.paddingRight=(r.barWidth=n.right)+"px",r.sizer.style.paddingBottom=(r.barHeight=n.bottom)+"px",r.heightForcer.style.borderBottom=n.bottom+"px solid transparent",n.right&&n.bottom?(r.scrollbarFiller.style.display="block",r.scrollbarFiller.style.height=n.bottom+"px",r.scrollbarFiller.style.width=n.right+"px"):r.scrollbarFiller.style.display="",n.bottom&&e.options.coverGutterNextToScrollbar&&e.options.fixedGutter?(r.gutterFiller.style.display="block",r.gutterFiller.style.height=n.bottom+"px",r.gutterFiller.style.width=t.gutterWidth+"px"):r.gutterFiller.style.display=""}n$.prototype.update=function(){return{bottom:0,right:0}},n$.prototype.setScrollLeft=function(){},n$.prototype.setScrollTop=function(){},n$.prototype.clear=function(){};var nb={native:nm,null:n$};function nx(e){e.display.scrollbars&&(e.display.scrollbars.clear(),e.display.scrollbars.addClass&&F(e.display.wrapper,e.display.scrollbars.addClass)),e.display.scrollbars=new nb[e.options.scrollbarStyle](function(t){e.display.wrapper.insertBefore(t,e.display.scrollbarFiller),ew(t,"mousedown",function(){e.state.focused&&setTimeout(function(){return e.display.input.focus()},0)}),t.setAttribute("cm-not-content","true")},function(t,r){"horizontal"==r?ng(e,t):nd(e,t)},e),e.display.scrollbars.addClass&&G(e.display.wrapper,e.display.scrollbars.addClass)}var nw=0;function nC(e){var t;e.curOp={cm:e,viewChanged:!1,startHeight:e.doc.height,forceUpdate:!1,updateInput:0,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++nw,markArrays:null},t=e.curOp,t9?t9.ops.push(t):t.ownsGroup=t9={ops:[t],delayedCallbacks:[]}}function nS(e){var t=e.curOp;t&&function e(t,r){var n=t.ownsGroup;if(n)try{!function e(t){var r=t.delayedCallbacks,n=0;do{for(;n<r.length;n++)r[n].call(null);for(var i=0;i<t.ops.length;i++){var o=t.ops[i];if(o.cursorActivityHandlers)for(;o.cursorActivityCalled<o.cursorActivityHandlers.length;)o.cursorActivityHandlers[o.cursorActivityCalled++].call(null,o.cm)}}while(n<r.length)}(n)}finally{t9=null,r(n)}}(t,function(e){for(var t=0;t<e.ops.length;t++)e.ops[t].cm.curOp=null;!function e(t){for(var r=t.ops,n=0;n<r.length;n++)nL(r[n]);for(var i=0;i<r.length;i++)nk(r[i]);for(var o=0;o<r.length;o++)nT(r[o]);for(var l=0;l<r.length;l++)nN(r[l]);for(var s=0;s<r.length;s++)nO(r[s])}(e)})}function nL(e){var t,r,n=e.cm,i=n.display;!(r=(t=n).display).scrollbarsClipped&&r.scroller.offsetWidth&&(r.nativeBarWidth=r.scroller.offsetWidth-r.scroller.clientWidth,r.heightForcer.style.height=rg(t)+"px",r.sizer.style.marginBottom=-r.nativeBarWidth+"px",r.sizer.style.borderRightWidth=rg(t)+"px",r.scrollbarsClipped=!0),e.updateMaxLine&&tB(n),e.mustUpdate=e.viewChanged||e.forceUpdate||null!=e.scrollTop||e.scrollToPos&&(e.scrollToPos.from.line<i.viewFrom||e.scrollToPos.to.line>=i.viewTo)||i.maxLineChanged&&n.options.lineWrapping,e.update=e.mustUpdate&&new nF(n,e.mustUpdate&&{top:e.scrollTop,ensure:e.scrollToPos},e.forceUpdate)}function nk(e){e.updatedDisplay=e.mustUpdate&&nP(e.cm,e.update)}function nT(e){var t=e.cm,r=t.display;e.updatedDisplay&&ni(t),e.barMeasure=nv(t),r.maxLineChanged&&!t.options.lineWrapping&&(e.adjustWidthTo=ry(t,r.maxLine,r.maxLine.text.length).left+3,t.display.sizerWidth=e.adjustWidthTo,e.barMeasure.scrollWidth=Math.max(r.scroller.clientWidth,r.sizer.offsetLeft+e.adjustWidthTo+rg(t)+t.display.barWidth),e.maxScrollLeft=Math.max(0,r.sizer.offsetLeft+e.adjustWidthTo-rv(t))),(e.updatedDisplay||e.selectionChanged)&&(e.preparedSelection=r.input.prepareSelection())}function nN(e){var t=e.cm;null!=e.adjustWidthTo&&(t.display.sizer.style.minWidth=e.adjustWidthTo+"px",e.maxScrollLeft<t.doc.scrollLeft&&ng(t,Math.min(t.display.scroller.scrollLeft,e.maxScrollLeft),!0),t.display.maxLineChanged=!1);var r=e.focus&&e.focus==B(K(t));e.preparedSelection&&t.display.input.showSelection(e.preparedSelection,r),(e.updatedDisplay||e.startHeight!=t.doc.height)&&ny(t,e.barMeasure),e.updatedDisplay&&nR(t,e.barMeasure),e.selectionChanged&&rJ(t),t.state.focused&&e.updateInput&&t.display.input.reset(e.typing),r&&ne(e.cm)}function nO(e){var t=e.cm,r=t.display,n=t.doc;if(e.updatedDisplay&&n1(t,e.update),null!=r.wheelStartX&&(null!=e.scrollTop||null!=e.scrollLeft||e.scrollToPos)&&(r.wheelStartX=r.wheelStartY=null),null!=e.scrollTop&&np(t,e.scrollTop,e.forceScroll),null!=e.scrollLeft&&ng(t,e.scrollLeft,!0,!0),e.scrollToPos){var i=function e(t,r,n,i){null==i&&(i=0),t.options.lineWrapping||r!=n||(n="before"==r.sticky?te(r.line,r.ch+1,"before"):r,r=r.ch?te(r.line,"before"==r.sticky?r.ch-1:r.ch,"after"):r);for(var o,l=0;l<5;l++){var s=!1,a=rW(t,r),u=n&&n!=r?rW(t,n):a,c=ns(t,o={left:Math.min(a.left,u.left),top:Math.min(a.top,u.top)-i,right:Math.max(a.left,u.left),bottom:Math.max(a.bottom,u.bottom)+i}),f=t.doc.scrollTop,h=t.doc.scrollLeft;if(null!=c.scrollTop&&(nd(t,c.scrollTop),Math.abs(t.doc.scrollTop-f)>1&&(s=!0)),null!=c.scrollLeft&&(ng(t,c.scrollLeft),Math.abs(t.doc.scrollLeft-h)>1&&(s=!0)),!s)break}return o}(t,ts(n,e.scrollToPos.from),ts(n,e.scrollToPos.to),e.scrollToPos.margin);!function e(t,r){if(!ek(t,"scrollCursorIntoView")){var n=t.display,i=n.sizer.getBoundingClientRect(),o=null,l=n.wrapper.ownerDocument;if(r.top+i.top<0?o=!0:r.bottom+i.top>(l.defaultView.innerHeight||l.documentElement.clientHeight)&&(o=!1),null!=o&&!S){var s=z("div","​",null,"position: absolute;\n                         top: "+(r.top-n.viewOffset-rh(t.display))+"px;\n                         height: "+(r.bottom-r.top+rg(t)+n.barHeight)+"px;\n                         left: "+r.left+"px; width: "+Math.max(2,r.right-r.left)+"px;");t.display.lineSpace.appendChild(s),s.scrollIntoView(o),t.display.lineSpace.removeChild(s)}}}(t,i)}var o=e.maybeHiddenMarkers,l=e.maybeUnhiddenMarkers;if(o)for(var s=0;s<o.length;++s)o[s].lines.length||eL(o[s],"hide");if(l)for(var a=0;a<l.length;++a)l[a].lines.length&&eL(l[a],"unhide");r.wrapper.offsetHeight&&(n.scrollTop=t.display.scroller.scrollTop),e.changeObjs&&eL(t,"changes",t,e.changeObjs),e.update&&e.update.finish()}function nM(e,t){if(e.curOp)return t();nC(e);try{return t()}finally{nS(e)}}function nA(e,t){return function(){if(e.curOp)return t.apply(e,arguments);nC(e);try{return t.apply(e,arguments)}finally{nS(e)}}}function n0(e){return function(){if(this.curOp)return e.apply(this,arguments);nC(this);try{return e.apply(this,arguments)}finally{nS(this)}}}function nD(e){return function(){var t=this.cm;if(!t||t.curOp)return e.apply(this,arguments);nC(t);try{return e.apply(this,arguments)}finally{nS(t)}}}function nW(e,t){e.doc.highlightFrontier<e.display.viewTo&&e.state.highlight.set(t,j(nH,e))}function nH(e){var t=e.doc;if(!(t.highlightFrontier>=e.display.viewTo)){var r=+new Date+e.options.workTime,n=td(e,t.highlightFrontier),i=[];t.iter(n.line,Math.min(t.first+t.size,e.display.viewTo+500),function(o){if(n.line>=e.display.viewFrom){var l=o.styles,s=o.text.length>e.options.maxHighlightLength?eU(t.mode,n.state):null,a=tf(e,o,n,!0);s&&(n.state=s),o.styles=a.styles;var u=o.styleClasses,c=a.classes;c?o.styleClasses=c:u&&(o.styleClasses=null);for(var f=!l||l.length!=o.styles.length||u!=c&&(!u||!c||u.bgClass!=c.bgClass||u.textClass!=c.textClass),h=0;!f&&h<l.length;++h)f=l[h]!=o.styles[h];f&&i.push(n.line),o.stateAfter=n.save(),n.nextLine()}else o.text.length<=e.options.maxHighlightLength&&tp(e,o.text,n),o.stateAfter=n.line%5==0?n.save():null,n.nextLine();if(+new Date>r)return nW(e,e.options.workDelay),!0}),t.highlightFrontier=n.line,t.modeFrontier=Math.max(t.modeFrontier,n.line),i.length&&nM(e,function(){for(var t=0;t<i.length;t++)rK(e,i[t],"text")})}}var nF=function(e,t,r){var n=e.display;this.viewport=t,this.visible=nl(n,e.doc,t),this.editorIsHidden=!n.wrapper.offsetWidth,this.wrapperHeight=n.wrapper.clientHeight,this.wrapperWidth=n.wrapper.clientWidth,this.oldDisplayWidth=rv(e),this.force=r,this.dims=r4(e),this.events=[]};function nP(e,t){var r=e.display,n=e.doc;if(t.editorIsHidden)return rX(e),!1;if(!t.force&&t.visible.from>=r.viewFrom&&t.visible.to<=r.viewTo&&(null==r.updateLineNumbers||r.updateLineNumbers>=r.viewTo)&&r.renderedView==r.view&&0==rY(e))return!1;n3(e)&&(rX(e),t.dims=r4(e));var i=n.first+n.size,o=Math.max(t.visible.from-e.options.viewportMargin,n.first),l=Math.min(i,t.visible.to+e.options.viewportMargin);r.viewFrom<o&&o-r.viewFrom<20&&(o=Math.max(n.first,r.viewFrom)),r.viewTo>l&&r.viewTo-l<20&&(l=Math.min(i,r.viewTo)),tx&&(o=t1(e.doc,o),l=tE(e.doc,l));var s,a,u,c,f=o!=r.viewFrom||l!=r.viewTo||r.lastWrapHeight!=t.wrapperHeight||r.lastWrapWidth!=t.wrapperWidth;s=e,a=o,u=l,0==(c=s.display).view.length||a>=c.viewTo||u<=c.viewFrom?(c.view=t8(s,a,u),c.viewFrom=a):(c.viewFrom>a?c.view=t8(s,a,c.viewFrom).concat(c.view):c.viewFrom<a&&(c.view=c.view.slice(rU(s,a))),c.viewFrom=a,c.viewTo<u?c.view=c.view.concat(t8(s,c.viewTo,u)):c.viewTo>u&&(c.view=c.view.slice(0,rU(s,u)))),c.viewTo=u,r.viewOffset=tI(ej(e.doc,r.viewFrom)),e.display.mover.style.top=r.viewOffset+"px";var h=rY(e);if(!f&&0==h&&!t.force&&r.renderedView==r.view&&(null==r.updateLineNumbers||r.updateLineNumbers>=r.viewTo))return!1;var d=function e(t){if(t.hasFocus())return null;var r=B(K(t));if(!r||!I(t.display.lineDiv,r))return null;var n={activeElt:r};if(window.getSelection){var i=X(t).getSelection();i.anchorNode&&i.extend&&I(t.display.lineDiv,i.anchorNode)&&(n.anchorNode=i.anchorNode,n.anchorOffset=i.anchorOffset,n.focusNode=i.focusNode,n.focusOffset=i.focusOffset)}return n}(e);return h>4&&(r.lineDiv.style.display="none"),function e(t,r,n){var i=t.display,o=t.options.lineNumbers,l=i.lineDiv,s=l.firstChild;function a(e){var r=e.nextSibling;return $&&N&&t.display.currentWheelTarget==e?e.style.display="none":e.parentNode.removeChild(e),r}for(var u=i.view,c=i.viewFrom,f=0;f<u.length;f++){var h=u[f];if(h.hidden);else if(h.node&&h.node.parentNode==l){for(;s!=h.node;)s=a(s);var d=o&&null!=r&&r<=c&&h.lineNumber;h.changes&&(Q(h.changes,"gutter")>-1&&(d=!1),tJ(t,h,c,n)),d&&(P(h.lineNumber),h.lineNumber.appendChild(document.createTextNode(eJ(t.options,c)))),s=h.node.nextSibling}else{var p=rl(t,h,c,n);l.insertBefore(p,s)}c+=h.size}for(;s;)s=a(s)}(e,r.updateLineNumbers,t.dims),h>4&&(r.lineDiv.style.display=""),r.renderedView=r.view,!function e(t){if(t&&t.activeElt&&t.activeElt!=B(t.activeElt.ownerDocument)&&(t.activeElt.focus(),!/^(INPUT|TEXTAREA)$/.test(t.activeElt.nodeName)&&t.anchorNode&&I(document.body,t.anchorNode)&&I(document.body,t.focusNode))){var r=t.activeElt.ownerDocument,n=r.defaultView.getSelection(),i=r.createRange();i.setEnd(t.anchorNode,t.anchorOffset),i.collapse(!1),n.removeAllRanges(),n.addRange(i),n.extend(t.focusNode,t.focusOffset)}}(d),P(r.cursorDiv),P(r.selectionDiv),r.gutters.style.height=r.sizer.style.minHeight=0,f&&(r.lastWrapHeight=t.wrapperHeight,r.lastWrapWidth=t.wrapperWidth,nW(e,400)),r.updateLineNumbers=null,!0}function n1(e,t){for(var r=t.viewport,n=!0;;n=!1){if(n&&e.options.lineWrapping&&t.oldDisplayWidth!=rv(e))n&&(t.visible=nl(e.display,e.doc,r));else if(r&&null!=r.top&&(r={top:Math.min(e.doc.height+rd(e.display)-rm(e),r.top)}),t.visible=nl(e.display,e.doc,r),t.visible.from>=e.display.viewFrom&&t.visible.to<=e.display.viewTo)break;if(!nP(e,t))break;ni(e);var i=nv(e);r8(e),ny(e,i),nR(e,i),t.force=!1}t.signal(e,"update",e),(e.display.viewFrom!=e.display.reportedViewFrom||e.display.viewTo!=e.display.reportedViewTo)&&(t.signal(e,"viewportChange",e,e.display.viewFrom,e.display.viewTo),e.display.reportedViewFrom=e.display.viewFrom,e.display.reportedViewTo=e.display.viewTo)}function nE(e,t){var r=new nF(e,t);if(nP(e,r)){ni(e),n1(e,r);var n=nv(e);r8(e),ny(e,n),nR(e,n),r.finish()}}function nz(e){var t=e.gutters.offsetWidth;e.sizer.style.marginLeft=t+"px",tZ(e,"gutterChanged",e)}function nR(e,t){e.display.sizer.style.minHeight=t.docHeight+"px",e.display.heightForcer.style.top=t.docHeight+"px",e.display.gutters.style.height=t.docHeight+e.display.barHeight+rg(e)+"px"}function nI(e){var t=e.display,r=t.view;if(t.alignWidgets||t.gutters.firstChild&&e.options.fixedGutter){for(var n=r6(t)-t.scroller.scrollLeft+e.doc.scrollLeft,i=t.gutters.offsetWidth,o=n+"px",l=0;l<r.length;l++)if(!r[l].hidden){e.options.fixedGutter&&(r[l].gutter&&(r[l].gutter.style.left=o),r[l].gutterBackground&&(r[l].gutterBackground.style.left=o));var s=r[l].alignable;if(s)for(var a=0;a<s.length;a++)s[a].style.left=o}e.options.fixedGutter&&(t.gutters.style.left=n+i+"px")}}function n3(e){if(!e.options.lineNumbers)return!1;var t=e.doc,r=eJ(e.options,t.first+t.size-1),n=e.display;if(r.length!=n.lineNumChars){var i=n.measure.appendChild(z("div",[z("div",r)],"CodeMirror-linenumber CodeMirror-gutter-elt")),o=i.firstChild.offsetWidth,l=i.offsetWidth-o;return n.lineGutter.style.width="",n.lineNumInnerWidth=Math.max(o,n.lineGutter.offsetWidth-l)+1,n.lineNumWidth=n.lineNumInnerWidth+l,n.lineNumChars=n.lineNumInnerWidth?r.length:-1,n.lineGutter.style.width=n.lineNumWidth+"px",nz(e.display),!0}return!1}function nB(e,t){for(var r=[],n=!1,i=0;i<e.length;i++){var o=e[i],l=null;if("string"!=typeof o&&(l=o.style,o=o.className),"CodeMirror-linenumbers"==o){if(!t)continue;n=!0}r.push({className:o,style:l})}return t&&!n&&r.push({className:"CodeMirror-linenumbers",style:null}),r}function n7(e){var t=e.gutters,r=e.gutterSpecs;P(t),e.lineGutter=null;for(var n=0;n<r.length;++n){var i=r[n],o=i.className,l=i.style,s=t.appendChild(z("div",null,"CodeMirror-gutter "+o));l&&(s.style.cssText=l),"CodeMirror-linenumbers"==o&&(e.lineGutter=s,s.style.width=(e.lineNumWidth||1)+"px")}t.style.display=r.length?"":"none",nz(e)}function n4(e){n7(e.display),rV(e),nI(e)}function n6(e,t,r,n){var i=this;this.input=r,i.scrollbarFiller=z("div",null,"CodeMirror-scrollbar-filler"),i.scrollbarFiller.setAttribute("cm-not-content","true"),i.gutterFiller=z("div",null,"CodeMirror-gutter-filler"),i.gutterFiller.setAttribute("cm-not-content","true"),i.lineDiv=R("div",null,"CodeMirror-code"),i.selectionDiv=z("div",null,null,"position: relative; z-index: 1"),i.cursorDiv=z("div",null,"CodeMirror-cursors"),i.measure=z("div",null,"CodeMirror-measure"),i.lineMeasure=z("div",null,"CodeMirror-measure"),i.lineSpace=R("div",[i.measure,i.lineMeasure,i.selectionDiv,i.cursorDiv,i.lineDiv],null,"position: relative; outline: none");var o=R("div",[i.lineSpace],"CodeMirror-lines");i.mover=z("div",[o],null,"position: relative"),i.sizer=z("div",[i.mover],"CodeMirror-sizer"),i.sizerWidth=null,i.heightForcer=z("div",null,null,"position: absolute; height: 50px; width: 1px;"),i.gutters=z("div",null,"CodeMirror-gutters"),i.lineGutter=null,i.scroller=z("div",[i.sizer,i.heightForcer,i.gutters],"CodeMirror-scroll"),i.scroller.setAttribute("tabIndex","-1"),i.wrapper=z("div",[i.scrollbarFiller,i.gutterFiller,i.scroller],"CodeMirror"),_&&b>=105&&(i.wrapper.style.clipPath="inset(0px)"),i.wrapper.setAttribute("translate","no"),v&&m<8&&(i.gutters.style.zIndex=-1,i.scroller.style.paddingRight=0),$||h&&T||(i.scroller.draggable=!0),e&&(e.appendChild?e.appendChild(i.wrapper):e(i.wrapper)),i.viewFrom=i.viewTo=t.first,i.reportedViewFrom=i.reportedViewTo=t.first,i.view=[],i.renderedView=null,i.externalMeasured=null,i.viewOffset=0,i.lastWrapHeight=i.lastWrapWidth=0,i.updateLineNumbers=null,i.nativeBarWidth=i.barHeight=i.barWidth=0,i.scrollbarsClipped=!1,i.lineNumWidth=i.lineNumInnerWidth=i.lineNumChars=null,i.alignWidgets=!1,i.cachedCharWidth=i.cachedTextHeight=i.cachedPaddingH=null,i.maxLine=null,i.maxLineLength=0,i.maxLineChanged=!1,i.wheelDX=i.wheelDY=i.wheelStartX=i.wheelStartY=null,i.shift=!1,i.selForContextMenu=null,i.activeTouch=null,i.gutterSpecs=nB(n.gutters,n.lineNumbers),n7(i),r.init(i)}nF.prototype.signal=function(e,t){eN(e,t)&&this.events.push(arguments)},nF.prototype.finish=function(){for(var e=0;e<this.events.length;e++)eL.apply(null,this.events[e])};var n5=0,n2=null;function nG(e){var t=e.wheelDeltaX,r=e.wheelDeltaY;return null==t&&e.detail&&e.axis==e.HORIZONTAL_AXIS&&(t=e.detail),null==r&&e.detail&&e.axis==e.VERTICAL_AXIS?r=e.detail:null==r&&(r=e.wheelDelta),{x:t,y:r}}function nU(e,t){_&&102==b&&(null==e.display.chromeScrollHack?e.display.sizer.style.pointerEvents="none":clearTimeout(e.display.chromeScrollHack),e.display.chromeScrollHack=setTimeout(function(){e.display.chromeScrollHack=null,e.display.sizer.style.pointerEvents=""},100));var r=nG(t),n=r.x,i=r.y,o=n2;0===t.deltaMode&&(n=t.deltaX,i=t.deltaY,o=1);var l=e.display,s=l.scroller,a=s.scrollWidth>s.clientWidth,u=s.scrollHeight>s.clientHeight;if(n&&a||i&&u){if(i&&N&&$){outer:for(var c=t.target,f=l.view;c!=s;c=c.parentNode)for(var d=0;d<f.length;d++)if(f[d].node==c){e.display.currentWheelTarget=c;break outer}}if(n&&!h&&!x&&null!=o){i&&u&&nd(e,Math.max(0,s.scrollTop+i*o)),ng(e,Math.max(0,s.scrollLeft+n*o)),(!i||i&&u)&&eM(t),l.wheelStartX=null;return}if(i&&null!=o){var p=i*o,g=e.doc.scrollTop,v=g+l.wrapper.clientHeight;p<0?g=Math.max(0,g+p-50):v=Math.min(e.doc.height,v+p+50),nE(e,{top:g,bottom:v})}n5<20&&0!==t.deltaMode&&(null==l.wheelStartX?(l.wheelStartX=s.scrollLeft,l.wheelStartY=s.scrollTop,l.wheelDX=n,l.wheelDY=i,setTimeout(function(){if(null!=l.wheelStartX){var e=s.scrollLeft-l.wheelStartX,t=s.scrollTop-l.wheelStartY,r=t&&l.wheelDY&&t/l.wheelDY||e&&l.wheelDX&&e/l.wheelDX;l.wheelStartX=l.wheelStartY=null,r&&(n2=(n2*n5+r)/(n5+1),++n5)}},200)):(l.wheelDX+=n,l.wheelDY+=i))}}v?n2=-.53:h?n2=15:_?n2=-.7:w&&(n2=-1/3);var nV=function(e,t){this.ranges=e,this.primIndex=t};nV.prototype.primary=function(){return this.ranges[this.primIndex]},nV.prototype.equals=function(e){if(e==this)return!0;if(e.primIndex!=this.primIndex||e.ranges.length!=this.ranges.length)return!1;for(var t=0;t<this.ranges.length;t++){var r=this.ranges[t],n=e.ranges[t];if(!tr(r.anchor,n.anchor)||!tr(r.head,n.head))return!1}return!0},nV.prototype.deepCopy=function(){for(var e=[],t=0;t<this.ranges.length;t++)e[t]=new nK(tn(this.ranges[t].anchor),tn(this.ranges[t].head));return new nV(e,this.primIndex)},nV.prototype.somethingSelected=function(){for(var e=0;e<this.ranges.length;e++)if(!this.ranges[e].empty())return!0;return!1},nV.prototype.contains=function(e,t){t||(t=e);for(var r=0;r<this.ranges.length;r++){var n=this.ranges[r];if(tt(t,n.from())>=0&&0>=tt(e,n.to()))return r}return -1};var nK=function(e,t){this.anchor=e,this.head=t};function nX(e,t,r){var n=e&&e.options.selectionsMayTouch,i=t[r];t.sort(function(e,t){return tt(e.from(),t.from())}),r=Q(t,i);for(var o=1;o<t.length;o++){var l=t[o],s=t[o-1],a=tt(s.to(),l.from());if(n&&!l.empty()?a>0:a>=0){var u=to(s.from(),l.from()),c=ti(s.to(),l.to()),f=s.empty()?l.from()==l.head:s.from()==s.head;o<=r&&--r,t.splice(--o,2,new nK(f?c:u,f?u:c))}}return new nV(t,r)}function nj(e,t){return new nV([new nK(e,t||e)],0)}function nY(e){return e.text?te(e.from.line+e.text.length-1,el(e.text).length+(1==e.text.length?e.from.ch:0)):e.to}function n8(e,t){if(0>tt(e,t.from))return e;if(0>=tt(e,t.to))return nY(t);var r=e.line+t.text.length-(t.to.line-t.from.line)-1,n=e.ch;return e.line==t.to.line&&(n+=nY(t).ch-t.to.ch),te(r,n)}function n9(e,t){for(var r=[],n=0;n<e.sel.ranges.length;n++){var i=e.sel.ranges[n];r.push(new nK(n8(i.anchor,t),n8(i.head,t)))}return nX(e.cm,r,e.sel.primIndex)}function nq(e,t,r){return e.line==t.line?te(r.line,e.ch-t.ch+r.ch):te(r.line+(e.line-t.line),e.ch)}function nZ(e){e.doc.mode=e2(e.options,e.doc.modeOption),nQ(e)}function nQ(e){e.doc.iter(function(e){e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null)}),e.doc.modeFrontier=e.doc.highlightFrontier=e.doc.first,nW(e,100),e.state.modeGen++,e.curOp&&rV(e)}function nJ(e,t){return 0==t.from.ch&&0==t.to.ch&&""==el(t.text)&&(!e.cm||e.cm.options.wholeLineUpdateBefore)}function ie(e,t,r,n){function i(e){return r?r[e]:null}function o(e,r,i){var o,l,s,a,u;o=e,l=r,s=i,a=n,o.text=l,o.stateAfter&&(o.stateAfter=null),o.styles&&(o.styles=null),null!=o.order&&(o.order=null),tT(o),tN(o,s),(u=a?a(o):1)!=o.height&&e9(o,u),tZ(e,"change",e,t)}function l(e,t){for(var r=[],o=e;o<t;++o)r.push(new t7(u[o],i(o),n));return r}var s=t.from,a=t.to,u=t.text,c=ej(e,s.line),f=ej(e,a.line),h=el(u),d=i(u.length-1),p=a.line-s.line;if(t.full)e.insert(0,l(0,u.length)),e.remove(u.length,e.size-u.length);else if(nJ(e,t)){var g=l(0,u.length-1);o(f,f.text,d),p&&e.remove(s.line,p),g.length&&e.insert(s.line,g)}else if(c==f){if(1==u.length)o(c,c.text.slice(0,s.ch)+h+c.text.slice(a.ch),d);else{var v=l(1,u.length-1);v.push(new t7(h+c.text.slice(a.ch),d,n)),o(c,c.text.slice(0,s.ch)+u[0],i(0)),e.insert(s.line+1,v)}}else if(1==u.length)o(c,c.text.slice(0,s.ch)+u[0]+f.text.slice(a.ch),i(0)),e.remove(s.line+1,p);else{o(c,c.text.slice(0,s.ch)+u[0],i(0)),o(f,h+f.text.slice(a.ch),d);var m=l(1,u.length-1);p>1&&e.remove(s.line+1,p-1),e.insert(s.line+1,m)}tZ(e,"change",e,t)}function it(e,t,r){!function e(n,i,o){if(n.linked)for(var l=0;l<n.linked.length;++l){var s=n.linked[l];if(null!=s.doc){var a=o&&s.sharedHist;(!r||a)&&(t(s.doc,a),e(s.doc,n,a))}}}(e,null,!0)}function ir(e,t){if(t.cm)throw Error("This document is already in use.");e.doc=t,t.cm=e,r2(e),nZ(e),ii(e),e.options.direction=t.direction,e.options.lineWrapping||tB(e),e.options.mode=t.modeOption,rV(e)}function ii(e){("rtl"==e.doc.direction?G:F)(e.display.lineDiv,"CodeMirror-rtl")}function io(e){this.done=[],this.undone=[],this.undoDepth=e?e.undoDepth:1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=e?e.maxGeneration:1}function il(e,t){var r={from:tn(t.from),to:nY(t),text:eY(e,t.from,t.to)};return ic(e,r,t.from.line,t.to.line+1),it(e,function(e){return ic(e,r,t.from.line,t.to.line+1)},!0),r}function is(e){for(;e.length;)if(el(e).ranges)e.pop();else break}function ia(e,t,r,n){var i,o=e.history;o.undone.length=0;var l,s,a,u=+new Date;if((o.lastOp==n||o.lastOrigin==t.origin&&t.origin&&("+"==t.origin.charAt(0)&&o.lastModTime>u-(e.cm?e.cm.options.historyEventDelay:500)||"*"==t.origin.charAt(0)))&&(l=(s=o,a=o.lastOp==n,a?(is(s.done),el(s.done)):s.done.length&&!el(s.done).ranges?el(s.done):s.done.length>1&&!s.done[s.done.length-2].ranges?(s.done.pop(),el(s.done)):void 0)))i=el(l.changes),0==tt(t.from,t.to)&&0==tt(t.from,i.to)?i.to=nY(t):l.changes.push(il(e,t));else{var c=el(o.done);for(c&&c.ranges||iu(e.sel,o.done),l={changes:[il(e,t)],generation:o.generation},o.done.push(l);o.done.length>o.undoDepth;)o.done.shift(),o.done[0].ranges||o.done.shift()}o.done.push(r),o.generation=++o.maxGeneration,o.lastModTime=o.lastSelTime=u,o.lastOp=o.lastSelOp=n,o.lastOrigin=o.lastSelOrigin=t.origin,i||eL(e,"historyAdded")}function iu(e,t){var r=el(t);r&&r.ranges&&r.equals(e)||t.push(e)}function ic(e,t,r,n){var i=t["spans_"+e.id],o=0;e.iter(Math.max(e.first,r),Math.min(e.first+e.size,n),function(r){r.markedSpans&&((i||(i=t["spans_"+e.id]={}))[o]=r.markedSpans),++o})}function ih(e){if(!e)return null;for(var t,r=0;r<e.length;++r)e[r].marker.explicitlyCleared?t||(t=e.slice(0,r)):t&&t.push(e[r]);return t?t.length?t:null:e}function id(e,t){var r=function e(t,r){var n=r["spans_"+t.id];if(!n)return null;for(var i=[],o=0;o<r.text.length;++o)i.push(ih(n[o]));return i}(e,t),n=tL(e,t);if(!r)return n;if(!n)return r;for(var i=0;i<r.length;++i){var o=r[i],l=n[i];if(o&&l)spans:for(var s=0;s<l.length;++s){for(var a=l[s],u=0;u<o.length;++u)if(o[u].marker==a.marker)continue spans;o.push(a)}else l&&(r[i]=l)}return r}function ip(e,t,r){for(var n=[],i=0;i<e.length;++i){var o=e[i];if(o.ranges){n.push(r?nV.prototype.deepCopy.call(o):o);continue}var l=o.changes,s=[];n.push({changes:s});for(var a=0;a<l.length;++a){var u=l[a],c=void 0;if(s.push({from:u.from,to:u.to,text:u.text}),t)for(var f in u)(c=f.match(/^spans_(\d+)$/))&&Q(t,Number(c[1]))>-1&&(el(s)[f]=u[f],delete u[f])}}return n}function ig(e,t,r,n){if(!n)return new nK(r||t,t);var i=e.anchor;if(r){var o=0>tt(t,i);o!=0>tt(r,i)?(i=t,t=r):o!=0>tt(t,r)&&(t=r)}return new nK(i,t)}function iv(e,t,r,n,i){null==i&&(i=e.cm&&(e.cm.display.shift||e.extend)),ib(e,new nV([ig(e.sel.primary(),t,r,i)],0),n)}function im(e,t,r){for(var n=[],i=e.cm&&(e.cm.display.shift||e.extend),o=0;o<e.sel.ranges.length;o++)n[o]=ig(e.sel.ranges[o],t[o],null,i);var l=nX(e.cm,n,e.sel.primIndex);ib(e,l,r)}function i$(e,t,r,n){var i=e.sel.ranges.slice(0);i[t]=r,ib(e,nX(e.cm,i,e.sel.primIndex),n)}function iy(e,t,r,n){ib(e,nj(t,r),n)}function i_(e,t,r){var n=e.history.done,i=el(n);i&&i.ranges?(n[n.length-1]=t,ix(e,t,r)):ib(e,t,r)}function ib(e,t,r){var n,i,o,l,s,a,u,c,f,h,d;ix(e,t,r),n=e,i=e.sel,o=e.cm?e.cm.curOp.id:NaN,l=r,h=n.history,d=l&&l.origin,o==h.lastSelOp||d&&h.lastSelOrigin==d&&(h.lastModTime==h.lastSelTime&&h.lastOrigin==d||(s=n,a=d,u=el(h.done),c=i,"*"==(f=a.charAt(0))||"+"==f&&u.ranges.length==c.ranges.length&&u.somethingSelected()==c.somethingSelected()&&new Date-s.history.lastSelTime<=(s.cm?s.cm.options.historyEventDelay:500)))?h.done[h.done.length-1]=i:iu(i,h.done),h.lastSelTime=+new Date,h.lastSelOrigin=d,h.lastSelOp=o,l&&!1!==l.clearRedo&&is(h.undone)}function ix(e,t,r){if(eN(e,"beforeSelectionChange")||e.cm&&eN(e.cm,"beforeSelectionChange")){var n,i,o,l;t=(n=e,i=t,o=r,l={ranges:i.ranges,update:function(e){this.ranges=[];for(var t=0;t<e.length;t++)this.ranges[t]=new nK(ts(n,e[t].anchor),ts(n,e[t].head))},origin:o&&o.origin},(eL(n,"beforeSelectionChange",n,l),n.cm&&eL(n.cm,"beforeSelectionChange",n.cm,l),l.ranges!=i.ranges)?nX(n.cm,l.ranges,l.ranges.length-1):i)}var s=r&&r.bias||(0>tt(t.primary().head,e.sel.primary().head)?-1:1);iw(e,iS(e,t,s,!0)),!(r&&!1===r.scroll)&&e.cm&&"nocursor"!=e.cm.getOption("readOnly")&&nu(e.cm)}function iw(e,t){!t.equals(e.sel)&&(e.sel=t,e.cm&&(e.cm.curOp.updateInput=1,e.cm.curOp.selectionChanged=!0,eT(e.cm)),tZ(e,"cursorActivity",e))}function iC(e){iw(e,iS(e,e.sel,null,!1))}function iS(e,t,r,n){for(var i,o=0;o<t.ranges.length;o++){var l=t.ranges[o],s=t.ranges.length==e.sel.ranges.length&&e.sel.ranges[o],a=ik(e,l.anchor,s&&s.anchor,r,n),u=l.head==l.anchor?a:ik(e,l.head,s&&s.head,r,n);(i||a!=l.anchor||u!=l.head)&&(i||(i=t.ranges.slice(0,o)),i[o]=new nK(a,u))}return i?nX(e.cm,i,t.primIndex):t}function iL(e,t,r,n,i){var o=ej(e,t.line);if(o.markedSpans)for(var l=0;l<o.markedSpans.length;++l){var s=o.markedSpans[l],a=s.marker,u="selectLeft"in a?!a.selectLeft:a.inclusiveLeft,c="selectRight"in a?!a.selectRight:a.inclusiveRight;if((null==s.from||(u?s.from<=t.ch:s.from<t.ch))&&(null==s.to||(c?s.to>=t.ch:s.to>t.ch))){if(i&&(eL(a,"beforeCursorEnter"),a.explicitlyCleared)){if(o.markedSpans){--l;continue}break}if(!a.atomic)continue;if(r){var f=a.find(n<0?1:-1),h=void 0;if((n<0?c:u)&&(f=iT(e,f,-n,f&&f.line==t.line?o:null)),f&&f.line==t.line&&(h=tt(f,r))&&(n<0?h<0:h>0))return iL(e,f,t,n,i)}var d=a.find(n<0?-1:1);return(n<0?u:c)&&(d=iT(e,d,n,d.line==t.line?o:null)),d?iL(e,d,t,n,i):null}}return t}function ik(e,t,r,n,i){var o=n||1,l=iL(e,t,r,o,i)||!i&&iL(e,t,r,o,!0)||iL(e,t,r,-o,i)||!i&&iL(e,t,r,-o,!0);return l||(e.cantEdit=!0,te(e.first,0))}function iT(e,t,r,n){return r<0&&0==t.ch?t.line>e.first?ts(e,te(t.line-1)):null:r>0&&t.ch==(n||ej(e,t.line)).text.length?t.line<e.first+e.size-1?te(t.line+1,0):null:new te(t.line,t.ch+r)}function iN(e){e.setSelection(te(e.firstLine(),0),te(e.lastLine()),ee)}function iO(e,t,r){var n={canceled:!1,from:t.from,to:t.to,text:t.text,origin:t.origin,cancel:function(){return n.canceled=!0}};return(r&&(n.update=function(t,r,i,o){t&&(n.from=ts(e,t)),r&&(n.to=ts(e,r)),i&&(n.text=i),void 0!==o&&(n.origin=o)}),eL(e,"beforeChange",e,n),e.cm&&eL(e.cm,"beforeChange",e.cm,n),n.canceled)?(e.cm&&(e.cm.curOp.updateInput=2),null):{from:n.from,to:n.to,text:n.text,origin:n.origin}}function iM(e,t,r){if(e.cm){if(!e.cm.curOp)return nA(e.cm,iM)(e,t,r);if(e.cm.state.suppressEdits)return}if(!(eN(e,"beforeChange")||e.cm&&eN(e.cm,"beforeChange"))||(t=iO(e,t,!0))){var n=tb&&!r&&function e(t,r,n){var i=null;if(t.iter(r.line,n.line+1,function(e){if(e.markedSpans)for(var t=0;t<e.markedSpans.length;++t){var r=e.markedSpans[t].marker;r.readOnly&&(!i||-1==Q(i,r))&&(i||(i=[])).push(r)}}),!i)return null;for(var o=[{from:r,to:n}],l=0;l<i.length;++l)for(var s=i[l],a=s.find(0),u=0;u<o.length;++u){var c=o[u];if(!(0>tt(c.to,a.from)||tt(c.from,a.to)>0)){var f=[u,1],h=tt(c.from,a.from),d=tt(c.to,a.to);!(h<0)&&(s.inclusiveLeft||h)||f.push({from:c.from,to:a.from}),!(d>0)&&(s.inclusiveRight||d)||f.push({from:a.to,to:c.to}),o.splice.apply(o,f),u+=f.length-3}}return o}(e,t.from,t.to);if(n)for(var i=n.length-1;i>=0;--i)iA(e,{from:n[i].from,to:n[i].to,text:i?[""]:t.text,origin:t.origin});else iA(e,t)}}function iA(e,t){if(1!=t.text.length||""!=t.text[0]||0!=tt(t.from,t.to)){var r=n9(e,t);ia(e,t,r,e.cm?e.cm.curOp.id:NaN),iW(e,t,r,tL(e,t));var n=[];it(e,function(e,r){r||-1!=Q(n,e.history)||(i1(e.history,t),n.push(e.history)),iW(e,t,null,tL(e,t))})}}function i0(e,t,r){var n=e.cm&&e.cm.state.suppressEdits;if(!n||r){for(var i,o=e.history,l=e.sel,s="undo"==t?o.done:o.undone,a="undo"==t?o.undone:o.done,u=0;u<s.length&&(i=s[u],r?!i.ranges||i.equals(e.sel):i.ranges);u++);if(u!=s.length){for(o.lastOrigin=o.lastSelOrigin=null;;)if((i=s.pop()).ranges){if(iu(i,a),r&&!i.equals(e.sel)){ib(e,i,{clearRedo:!1});return}l=i}else if(n){s.push(i);return}else break;var c=[];iu(l,a),a.push({changes:c,generation:o.generation}),o.generation=i.generation||++o.maxGeneration;for(var f=eN(e,"beforeChange")||e.cm&&eN(e.cm,"beforeChange"),h=i.changes.length-1;h>=0;--h){var d=function(r){var n=i.changes[r];if(n.origin=t,f&&!iO(e,n,!1))return s.length=0,{};c.push(il(e,n));var o=r?n9(e,n):el(s);iW(e,n,o,id(e,n)),!r&&e.cm&&e.cm.scrollIntoView({from:n.from,to:nY(n)});var l=[];it(e,function(e,t){t||-1!=Q(l,e.history)||(i1(e.history,n),l.push(e.history)),iW(e,n,null,id(e,n))})}(h);if(d)return d.v}}}}function iD(e,t){if(0!=t&&(e.first+=t,e.sel=new nV(es(e.sel.ranges,function(e){return new nK(te(e.anchor.line+t,e.anchor.ch),te(e.head.line+t,e.head.ch))}),e.sel.primIndex),e.cm)){rV(e.cm,e.first,e.first-t,t);for(var r=e.cm.display,n=r.viewFrom;n<r.viewTo;n++)rK(e.cm,n,"gutter")}}function iW(e,t,r,n){if(e.cm&&!e.cm.curOp)return nA(e.cm,iW)(e,t,r,n);if(t.to.line<e.first){iD(e,t.text.length-1-(t.to.line-t.from.line));return}if(!(t.from.line>e.lastLine())){if(t.from.line<e.first){var i=t.text.length-1-(e.first-t.from.line);iD(e,i),t={from:te(e.first,0),to:te(t.to.line+i,t.to.ch),text:[el(t.text)],origin:t.origin}}var o=e.lastLine();t.to.line>o&&(t={from:t.from,to:te(o,ej(e,o).text.length),text:[t.text[0]],origin:t.origin}),t.removed=eY(e,t.from,t.to),r||(r=n9(e,t)),e.cm?function e(t,r,n){var i=t.doc,o=t.display,l=r.from,s=r.to,a=!1,u=l.line;t.options.lineWrapping||(u=eq(tP(ej(i,l.line))),i.iter(u,s.line+1,function(e){if(e==o.maxLine)return a=!0,!0})),i.sel.contains(r.from,r.to)>-1&&eT(t),ie(i,r,n,r5(t)),!t.options.lineWrapping&&(i.iter(u,l.line+r.text.length,function(e){var t=t3(e);t>o.maxLineLength&&(o.maxLine=e,o.maxLineLength=t,o.maxLineChanged=!0,a=!1)}),a&&(t.curOp.updateMaxLine=!0)),function e(t,r){if(t.modeFrontier=Math.min(t.modeFrontier,r),!(t.highlightFrontier<r-10)){for(var n=t.first,i=r-1;i>n;i--){var o=ej(t,i).stateAfter;if(o&&(!(o instanceof tu)||i+o.lookAhead<r)){n=i+1;break}}t.highlightFrontier=Math.min(t.highlightFrontier,n)}}(i,l.line),nW(t,400);var c=r.text.length-(s.line-l.line)-1;r.full?rV(t):l.line!=s.line||1!=r.text.length||nJ(t.doc,r)?rV(t,l.line,s.line+1,c):rK(t,l.line,"text");var f=eN(t,"changes"),h=eN(t,"change");if(h||f){var d={from:l,to:s,text:r.text,removed:r.removed,origin:r.origin};h&&tZ(t,"change",t,d),f&&(t.curOp.changeObjs||(t.curOp.changeObjs=[])).push(d)}t.display.selForContextMenu=null}(e.cm,t,n):ie(e,t,n),ix(e,r,ee),e.cantEdit&&ik(e,te(e.firstLine(),0))&&(e.cantEdit=!1)}}function iH(e,t,r,n,i){var o;n||(n=r),0>tt(n,r)&&(r=(o=[n,r])[0],n=o[1]),"string"==typeof t&&(t=e.splitLines(t)),iM(e,{from:r,to:n,text:t,origin:i})}function iF(e,t,r,n){r<e.line?e.line+=n:t<e.line&&(e.line=t,e.ch=0)}function iP(e,t,r,n){for(var i=0;i<e.length;++i){var o=e[i],l=!0;if(o.ranges){o.copied||((o=e[i]=o.deepCopy()).copied=!0);for(var s=0;s<o.ranges.length;s++)iF(o.ranges[s].anchor,t,r,n),iF(o.ranges[s].head,t,r,n);continue}for(var a=0;a<o.changes.length;++a){var u=o.changes[a];if(r<u.from.line)u.from=te(u.from.line+n,u.from.ch),u.to=te(u.to.line+n,u.to.ch);else if(t<=u.to.line){l=!1;break}}l||(e.splice(0,i+1),i=0)}}function i1(e,t){var r=t.from.line,n=t.to.line,i=t.text.length-(n-r)-1;iP(e.done,r,n,i),iP(e.undone,r,n,i)}function iE(e,t,r,n){var i=t,o=t;return("number"==typeof t?o=ej(e,tl(e,t)):i=eq(t),null==i)?null:(n(o,i)&&e.cm&&rK(e.cm,i,r),o)}function iz(e){this.lines=e,this.parent=null;for(var t=0,r=0;r<e.length;++r)e[r].parent=this,t+=e[r].height;this.height=t}function iR(e){this.children=e;for(var t=0,r=0,n=0;n<e.length;++n){var i=e[n];t+=i.chunkSize(),r+=i.height,i.parent=this}this.size=t,this.height=r,this.parent=null}nK.prototype.from=function(){return to(this.anchor,this.head)},nK.prototype.to=function(){return ti(this.anchor,this.head)},nK.prototype.empty=function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch},iz.prototype={chunkSize:function(){return this.lines.length},removeInner:function(e,t){for(var r=e,n=e+t;r<n;++r){var i=this.lines[r];this.height-=i.height,t4(i),tZ(i,"delete")}this.lines.splice(e,t)},collapse:function(e){e.push.apply(e,this.lines)},insertInner:function(e,t,r){this.height+=r,this.lines=this.lines.slice(0,e).concat(t).concat(this.lines.slice(e));for(var n=0;n<t.length;++n)t[n].parent=this},iterN:function(e,t,r){for(var n=e+t;e<n;++e)if(r(this.lines[e]))return!0}},iR.prototype={chunkSize:function(){return this.size},removeInner:function(e,t){this.size-=t;for(var r=0;r<this.children.length;++r){var n=this.children[r],i=n.chunkSize();if(e<i){var o=Math.min(t,i-e),l=n.height;if(n.removeInner(e,o),this.height-=l-n.height,i==o&&(this.children.splice(r--,1),n.parent=null),0==(t-=o))break;e=0}else e-=i}if(this.size-t<25&&(this.children.length>1||!(this.children[0]instanceof iz))){var s=[];this.collapse(s),this.children=[new iz(s)],this.children[0].parent=this}},collapse:function(e){for(var t=0;t<this.children.length;++t)this.children[t].collapse(e)},insertInner:function(e,t,r){this.size+=t.length,this.height+=r;for(var n=0;n<this.children.length;++n){var i=this.children[n],o=i.chunkSize();if(e<=o){if(i.insertInner(e,t,r),i.lines&&i.lines.length>50){for(var l=i.lines.length%25+25,s=l;s<i.lines.length;){var a=new iz(i.lines.slice(s,s+=25));i.height-=a.height,this.children.splice(++n,0,a),a.parent=this}i.lines=i.lines.slice(0,l),this.maybeSpill()}break}e-=o}},maybeSpill:function(){if(!(this.children.length<=10)){var e=this;do{var t=e.children.splice(e.children.length-5,5),r=new iR(t);if(e.parent){e.size-=r.size,e.height-=r.height;var n=Q(e.parent.children,e);e.parent.children.splice(n+1,0,r)}else{var i=new iR(e.children);i.parent=e,e.children=[i,r],e=i}r.parent=e.parent}while(e.children.length>10);e.parent.maybeSpill()}},iterN:function(e,t,r){for(var n=0;n<this.children.length;++n){var i=this.children[n],o=i.chunkSize();if(e<o){var l=Math.min(t,o-e);if(i.iterN(e,l,r))return!0;if(0==(t-=l))break;e=0}else e-=o}}};var iI=function(e,t,r){if(r)for(var n in r)r.hasOwnProperty(n)&&(this[n]=r[n]);this.doc=e,this.node=t};function i3(e,t,r){tI(t)<(e.curOp&&e.curOp.scrollTop||e.doc.scrollTop)&&na(e,r)}iI.prototype.clear=function(){var e=this.doc.cm,t=this.line.widgets,r=this.line,n=eq(r);if(null!=n&&t){for(var i=0;i<t.length;++i)t[i]==this&&t.splice(i--,1);t.length||(r.widgets=null);var o=rc(this);e9(r,Math.max(0,r.height-o)),e&&(nM(e,function(){i3(e,r,-o),rK(e,n,"widget")}),tZ(e,"lineWidgetCleared",e,this,n))}},iI.prototype.changed=function(){var e=this,t=this.height,r=this.doc.cm,n=this.line;this.height=null;var i=rc(this)-t;i&&(tz(this.doc,n)||e9(n,n.height+i),r&&nM(r,function(){r.curOp.forceUpdate=!0,i3(r,n,i),tZ(r,"lineWidgetChanged",r,e,eq(n))}))},eO(iI);var iB=0,i7=function(e,t){this.lines=[],this.type=t,this.doc=e,this.id=++iB};function i4(e,t,r,n,i){if(n&&n.shared){var o,l,s,a,u,c,f,h;return o=e,l=t,s=r,a=n,u=i,(a=Y(a)).shared=!1,c=[i4(o,l,s,a,u)],f=c[0],h=a.widgetNode,it(o,function(e){h&&(a.widgetNode=h.cloneNode(!0)),c.push(i4(e,ts(e,l),ts(e,s),a,u));for(var t=0;t<e.linked.length;++t)if(e.linked[t].isParent)return;f=el(c)}),new i6(c,f)}if(e.cm&&!e.cm.curOp)return nA(e.cm,i4)(e,t,r,n,i);var d=new i7(e,i),p=tt(t,r);if(n&&Y(n,d,!1),p>0||0==p&&!1!==d.clearWhenEmpty)return d;if(d.replacedWith&&(d.collapsed=!0,d.widgetNode=R("span",[d.replacedWith],"CodeMirror-widget"),n.handleMouseEvents||d.widgetNode.setAttribute("cm-ignore-events","true"),n.insertLeft&&(d.widgetNode.insertLeft=!0)),d.collapsed){if(tF(e,t.line,t,r,d)||t.line!=r.line&&tF(e,r.line,t,r,d))throw Error("Inserting collapsed marker partially overlapping an existing one");tx=!0}d.addToHistory&&ia(e,{from:t,to:r,origin:"markText"},e.sel,NaN);var g,v=t.line,m=e.cm;if(e.iter(v,r.line+1,function(n){var i,o,l,s;m&&d.collapsed&&!m.options.lineWrapping&&tP(n)==m.display.maxLine&&(g=!0),d.collapsed&&v!=t.line&&e9(n,0),i=n,o=new tw(d,v==t.line?t.ch:null,v==r.line?r.ch:null),(s=(l=e.cm&&e.cm.curOp)&&window.WeakSet&&(l.markedSpans||(l.markedSpans=new WeakSet)))&&i.markedSpans&&s.has(i.markedSpans)?i.markedSpans.push(o):(i.markedSpans=i.markedSpans?i.markedSpans.concat([o]):[o],s&&s.add(i.markedSpans)),o.marker.attachLine(i),++v}),d.collapsed&&e.iter(t.line,r.line+1,function(t){tz(e,t)&&e9(t,0)}),d.clearOnEnter&&ew(d,"beforeCursorEnter",function(){return d.clear()}),d.readOnly&&(tb=!0,(e.history.done.length||e.history.undone.length)&&e.clearHistory()),d.collapsed&&(d.id=++iB,d.atomic=!0),m){if(g&&(m.curOp.updateMaxLine=!0),d.collapsed)rV(m,t.line,r.line+1);else if(d.className||d.startStyle||d.endStyle||d.css||d.attributes||d.title)for(var $=t.line;$<=r.line;$++)rK(m,$,"text");d.atomic&&iC(m.doc),tZ(m,"markerAdded",m,d)}return d}i7.prototype.clear=function(){if(!this.explicitlyCleared){var e=this.doc.cm,t=e&&!e.curOp;if(t&&nC(e),eN(this,"clear")){var r=this.find();r&&tZ(this,"clear",r.from,r.to)}for(var n=null,i=null,o=0;o<this.lines.length;++o){var l=this.lines[o],s=tC(l.markedSpans,this);e&&!this.collapsed?rK(e,eq(l),"text"):e&&(null!=s.to&&(i=eq(l)),null!=s.from&&(n=eq(l))),l.markedSpans=tS(l.markedSpans,s),null==s.from&&this.collapsed&&!tz(this.doc,l)&&e&&e9(l,rB(e.display))}if(e&&this.collapsed&&!e.options.lineWrapping)for(var a=0;a<this.lines.length;++a){var u=tP(this.lines[a]),c=t3(u);c>e.display.maxLineLength&&(e.display.maxLine=u,e.display.maxLineLength=c,e.display.maxLineChanged=!0)}null!=n&&e&&this.collapsed&&rV(e,n,i+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,e&&iC(e.doc)),e&&tZ(e,"markerCleared",e,this,n,i),t&&nS(e),this.parent&&this.parent.clear()}},i7.prototype.find=function(e,t){null==e&&"bookmark"==this.type&&(e=1);for(var r,n,i=0;i<this.lines.length;++i){var o=this.lines[i],l=tC(o.markedSpans,this);if(null!=l.from&&(r=te(t?o:eq(o),l.from),-1==e))return r;if(null!=l.to&&(n=te(t?o:eq(o),l.to),1==e))return n}return r&&{from:r,to:n}},i7.prototype.changed=function(){var e=this,t=this.find(-1,!0),r=this,n=this.doc.cm;t&&n&&nM(n,function(){var i=t.line,o=eq(t.line),l=r_(n,o);if(l&&(rL(l),n.curOp.selectionChanged=n.curOp.forceUpdate=!0),n.curOp.updateMaxLine=!0,!tz(r.doc,i)&&null!=r.height){var s=r.height;r.height=null;var a=rc(r)-s;a&&e9(i,i.height+a)}tZ(n,"markerChanged",n,e)})},i7.prototype.attachLine=function(e){if(!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;t.maybeHiddenMarkers&&-1!=Q(t.maybeHiddenMarkers,this)||(t.maybeUnhiddenMarkers||(t.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(e)},i7.prototype.detachLine=function(e){if(this.lines.splice(Q(this.lines,e),1),!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;(t.maybeHiddenMarkers||(t.maybeHiddenMarkers=[])).push(this)}},eO(i7);var i6=function(e,t){this.markers=e,this.primary=t;for(var r=0;r<e.length;++r)e[r].parent=this};function i5(e){return e.findMarks(te(e.first,0),e.clipPos(te(e.lastLine())),function(e){return e.parent})}function i2(e){for(var t=0;t<e.length;t++)!function(t){var r=e[t],n=[r.primary.doc];it(r.primary.doc,function(e){return n.push(e)});for(var i=0;i<r.markers.length;i++){var o=r.markers[i];-1==Q(n,o.doc)&&(o.parent=null,r.markers.splice(i--,1))}}(t)}i6.prototype.clear=function(){if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var e=0;e<this.markers.length;++e)this.markers[e].clear();tZ(this,"clear")}},i6.prototype.find=function(e,t){return this.primary.find(e,t)},eO(i6);var iG=0,iU=function(e,t,r,n,i){if(!(this instanceof iU))return new iU(e,t,r,n,i);null==r&&(r=0),iR.call(this,[new iz([new t7("",null)])]),this.first=r,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.modeFrontier=this.highlightFrontier=r;var o=te(r,0);this.sel=nj(o),this.history=new io(null),this.id=++iG,this.modeOption=t,this.lineSep=n,this.direction="rtl"==i?"rtl":"ltr",this.extend=!1,"string"==typeof e&&(e=this.splitLines(e)),ie(this,{from:o,to:o,text:e}),ib(this,nj(o),ee)};iU.prototype=eu(iR.prototype,{constructor:iU,iter:function(e,t,r){r?this.iterN(e-this.first,t-e,r):this.iterN(this.first,this.first+this.size,e)},insert:function(e,t){for(var r=0,n=0;n<t.length;++n)r+=t[n].height;this.insertInner(e-this.first,t,r)},remove:function(e,t){this.removeInner(e-this.first,t)},getValue:function(e){var t=e8(this,this.first,this.first+this.size);return!1===e?t:t.join(e||this.lineSeparator())},setValue:nD(function(e){var t=te(this.first,0),r=this.first+this.size-1;iM(this,{from:t,to:te(r,ej(this,r).text.length),text:this.splitLines(e),origin:"setValue",full:!0},!0),this.cm&&nc(this.cm,0,0),ib(this,nj(t),ee)}),replaceRange:function(e,t,r,n){t=ts(this,t),r=r?ts(this,r):t,iH(this,e,t,r,n)},getRange:function(e,t,r){var n=eY(this,ts(this,e),ts(this,t));return!1===r?n:""===r?n.join(""):n.join(r||this.lineSeparator())},getLine:function(e){var t=this.getLineHandle(e);return t&&t.text},getLineHandle:function(e){if(eQ(this,e))return ej(this,e)},getLineNumber:function(e){return eq(e)},getLineHandleVisualStart:function(e){return"number"==typeof e&&(e=ej(this,e)),tP(e)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(e){return ts(this,e)},getCursor:function(e){var t,r=this.sel.primary();return null==e||"head"==e?r.head:"anchor"==e?r.anchor:"end"==e||"to"==e||!1===e?r.to():r.from()},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:nD(function(e,t,r){iy(this,ts(this,"number"==typeof e?te(e,t||0):e),null,r)}),setSelection:nD(function(e,t,r){iy(this,ts(this,e),ts(this,t||e),r)}),extendSelection:nD(function(e,t,r){iv(this,ts(this,e),t&&ts(this,t),r)}),extendSelections:nD(function(e,t){im(this,ta(this,e),t)}),extendSelectionsBy:nD(function(e,t){im(this,ta(this,es(this.sel.ranges,e)),t)}),setSelections:nD(function(e,t,r){if(e.length){for(var n=[],i=0;i<e.length;i++)n[i]=new nK(ts(this,e[i].anchor),ts(this,e[i].head||e[i].anchor));null==t&&(t=Math.min(e.length-1,this.sel.primIndex)),ib(this,nX(this.cm,n,t),r)}}),addSelection:nD(function(e,t,r){var n=this.sel.ranges.slice(0);n.push(new nK(ts(this,e),ts(this,t||e))),ib(this,nX(this.cm,n,n.length-1),r)}),getSelection:function(e){for(var t,r=this.sel.ranges,n=0;n<r.length;n++){var i=eY(this,r[n].from(),r[n].to());t=t?t.concat(i):i}return!1===e?t:t.join(e||this.lineSeparator())},getSelections:function(e){for(var t=[],r=this.sel.ranges,n=0;n<r.length;n++){var i=eY(this,r[n].from(),r[n].to());!1!==e&&(i=i.join(e||this.lineSeparator())),t[n]=i}return t},replaceSelection:function(e,t,r){for(var n=[],i=0;i<this.sel.ranges.length;i++)n[i]=e;this.replaceSelections(n,t,r||"+input")},replaceSelections:nD(function(e,t,r){for(var n=[],i=this.sel,o=0;o<i.ranges.length;o++){var l=i.ranges[o];n[o]={from:l.from(),to:l.to(),text:this.splitLines(e[o]),origin:r}}for(var s=t&&"end"!=t&&function e(t,r,n){for(var i=[],o=te(t.first,0),l=o,s=0;s<r.length;s++){var a=r[s],u=nq(a.from,o,l),c=nq(nY(a),o,l);if(o=a.to,l=c,"around"==n){var f=t.sel.ranges[s],h=0>tt(f.head,f.anchor);i[s]=new nK(h?c:u,h?u:c)}else i[s]=new nK(u,u)}return new nV(i,t.sel.primIndex)}(this,n,t),a=n.length-1;a>=0;a--)iM(this,n[a]);s?i_(this,s):this.cm&&nu(this.cm)}),undo:nD(function(){i0(this,"undo")}),redo:nD(function(){i0(this,"redo")}),undoSelection:nD(function(){i0(this,"undo",!0)}),redoSelection:nD(function(){i0(this,"redo",!0)}),setExtending:function(e){this.extend=e},getExtending:function(){return this.extend},historySize:function(){for(var e=this.history,t=0,r=0,n=0;n<e.done.length;n++)!e.done[n].ranges&&++t;for(var i=0;i<e.undone.length;i++)!e.undone[i].ranges&&++r;return{undo:t,redo:r}},clearHistory:function(){var e=this;this.history=new io(this.history),it(this,function(t){return t.history=e.history},!0)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(e){return e&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(e){return this.history.generation==(e||this.cleanGeneration)},getHistory:function(){return{done:ip(this.history.done),undone:ip(this.history.undone)}},setHistory:function(e){var t=this.history=new io(this.history);t.done=ip(e.done.slice(0),null,!0),t.undone=ip(e.undone.slice(0),null,!0)},setGutterMarker:nD(function(e,t,r){return iE(this,e,"gutter",function(e){var n=e.gutterMarkers||(e.gutterMarkers={});return n[t]=r,!r&&ed(n)&&(e.gutterMarkers=null),!0})}),clearGutter:nD(function(e){var t=this;this.iter(function(r){r.gutterMarkers&&r.gutterMarkers[e]&&iE(t,r,"gutter",function(){return r.gutterMarkers[e]=null,ed(r.gutterMarkers)&&(r.gutterMarkers=null),!0})})}),lineInfo:function(e){var t;if("number"==typeof e){if(!eQ(this,e)||(t=e,!(e=ej(this,e))))return null}else if(null==(t=eq(e)))return null;return{line:t,handle:e,text:e.text,gutterMarkers:e.gutterMarkers,textClass:e.textClass,bgClass:e.bgClass,wrapClass:e.wrapClass,widgets:e.widgets}},addLineClass:nD(function(e,t,r){return iE(this,e,"gutter"==t?"gutter":"class",function(e){var n="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass";if(e[n]){if(H(r).test(e[n]))return!1;e[n]+=" "+r}else e[n]=r;return!0})}),removeLineClass:nD(function(e,t,r){return iE(this,e,"gutter"==t?"gutter":"class",function(e){var n="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass",i=e[n];if(!i)return!1;if(null==r)e[n]=null;else{var o=i.match(H(r));if(!o)return!1;var l=o.index+o[0].length;e[n]=i.slice(0,o.index)+(o.index&&l!=i.length?" ":"")+i.slice(l)||null}return!0})}),addLineWidget:nD(function(e,t,r){var n,i,o,l,s,a;return n=this,i=e,o=t,l=r,s=new iI(n,o,l),(a=n.cm)&&s.noHScroll&&(a.display.alignWidgets=!0),iE(n,i,"widget",function(e){var t=e.widgets||(e.widgets=[]);if(null==s.insertAt?t.push(s):t.splice(Math.min(t.length,Math.max(0,s.insertAt)),0,s),s.line=e,a&&!tz(n,e)){var r=tI(e)<n.scrollTop;e9(e,e.height+rc(s)),r&&na(a,s.height),a.curOp.forceUpdate=!0}return!0}),a&&tZ(a,"lineWidgetAdded",a,s,"number"==typeof i?i:eq(i)),s}),removeLineWidget:function(e){e.clear()},markText:function(e,t,r){return i4(this,ts(this,e),ts(this,t),r,r&&r.type||"range")},setBookmark:function(e,t){var r={replacedWith:t&&(null==t.nodeType?t.widget:t),insertLeft:t&&t.insertLeft,clearWhenEmpty:!1,shared:t&&t.shared,handleMouseEvents:t&&t.handleMouseEvents};return e=ts(this,e),i4(this,e,e,r,"bookmark")},findMarksAt:function(e){e=ts(this,e);var t=[],r=ej(this,e.line).markedSpans;if(r)for(var n=0;n<r.length;++n){var i=r[n];(null==i.from||i.from<=e.ch)&&(null==i.to||i.to>=e.ch)&&t.push(i.marker.parent||i.marker)}return t},findMarks:function(e,t,r){e=ts(this,e),t=ts(this,t);var n=[],i=e.line;return this.iter(e.line,t.line+1,function(o){var l=o.markedSpans;if(l)for(var s=0;s<l.length;s++){var a=l[s];!(null!=a.to&&i==e.line&&e.ch>=a.to||null==a.from&&i!=e.line||null!=a.from&&i==t.line&&a.from>=t.ch)&&(!r||r(a.marker))&&n.push(a.marker.parent||a.marker)}++i}),n},getAllMarks:function(){var e=[];return this.iter(function(t){var r=t.markedSpans;if(r)for(var n=0;n<r.length;++n)null!=r[n].from&&e.push(r[n].marker)}),e},posFromIndex:function(e){var t,r=this.first,n=this.lineSeparator().length;return this.iter(function(i){var o=i.text.length+n;if(o>e)return t=e,!0;e-=o,++r}),ts(this,te(r,t))},indexFromPos:function(e){var t=(e=ts(this,e)).ch;if(e.line<this.first||e.ch<0)return 0;var r=this.lineSeparator().length;return this.iter(this.first,e.line,function(e){t+=e.text.length+r}),t},copy:function(e){var t=new iU(e8(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep,this.direction);return t.scrollTop=this.scrollTop,t.scrollLeft=this.scrollLeft,t.sel=this.sel,t.extend=!1,e&&(t.history.undoDepth=this.history.undoDepth,t.setHistory(this.getHistory())),t},linkedDoc:function(e){e||(e={});var t=this.first,r=this.first+this.size;null!=e.from&&e.from>t&&(t=e.from),null!=e.to&&e.to<r&&(r=e.to);var n=new iU(e8(this,t,r),e.mode||this.modeOption,t,this.lineSep,this.direction);return e.sharedHist&&(n.history=this.history),(this.linked||(this.linked=[])).push({doc:n,sharedHist:e.sharedHist}),n.linked=[{doc:this,isParent:!0,sharedHist:e.sharedHist}],function e(t,r){for(var n=0;n<r.length;n++){var i=r[n],o=i.find(),l=t.clipPos(o.from),s=t.clipPos(o.to);if(tt(l,s)){var a=i4(t,l,s,i.primary,i.primary.type);i.markers.push(a),a.parent=i}}}(n,i5(this)),n},unlinkDoc:function(e){if(e instanceof oD&&(e=e.doc),this.linked){for(var t=0;t<this.linked.length;++t)if(this.linked[t].doc==e){this.linked.splice(t,1),e.unlinkDoc(this),i2(i5(this));break}}if(e.history==this.history){var r=[e.id];it(e,function(e){return r.push(e.id)},!0),e.history=new io(null),e.history.done=ip(this.history.done,r),e.history.undone=ip(this.history.undone,r)}},iterLinkedDocs:function(e){it(this,e)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(e){return this.lineSep?e.split(this.lineSep):ez(e)},lineSeparator:function(){return this.lineSep||"\n"},setDirection:nD(function(e){if("rtl"!=e&&(e="ltr"),e!=this.direction&&(this.direction=e,this.iter(function(e){return e.order=null}),this.cm)){var t;nM(t=this.cm,function(){ii(t),rV(t)})}})}),iU.prototype.eachLine=iU.prototype.iter;var iV=0;function iK(e){var t=this;if(iX(t),!(ek(t,e)||rf(t.display,e))){eM(e),v&&(iV=+new Date);var r=rG(t,e,!0),n=e.dataTransfer.files;if(!(!r||t.isReadOnly())){if(n&&n.length&&window.FileReader&&window.File)for(var i=n.length,o=Array(i),l=0,s=function(){++l==i&&nA(t,function(){var e={from:r=ts(t.doc,r),to:r,text:t.doc.splitLines(o.filter(function(e){return null!=e}).join(t.doc.lineSeparator())),origin:"paste"};iM(t.doc,e),i_(t.doc,nj(ts(t.doc,r),ts(t.doc,nY(e))))})()},a=function(e,r){if(t.options.allowDropFileTypes&&-1==Q(t.options.allowDropFileTypes,e.type)){s();return}var n=new FileReader;n.onerror=function(){return s()},n.onload=function(){var e=n.result;if(/[\x00-\x08\x0e-\x1f]{2}/.test(e)){s();return}o[r]=e,s()},n.readAsText(e)},u=0;u<n.length;u++)a(n[u],u);else{if(t.state.draggingText&&t.doc.sel.contains(r)>-1){t.state.draggingText(e),setTimeout(function(){return t.display.input.focus()},20);return}try{var c,f=e.dataTransfer.getData("Text");if(f){if(t.state.draggingText&&!t.state.draggingText.copy&&(c=t.listSelections()),ix(t.doc,nj(r,r)),c)for(var h=0;h<c.length;++h)iH(t.doc,"",c[h].anchor,c[h].head,"drag");t.replaceSelection(f,"around","paste"),t.display.input.focus()}}catch(d){}}}}}function iX(e){e.display.dragCursor&&(e.display.lineSpace.removeChild(e.display.dragCursor),e.display.dragCursor=null)}function ij(e){if(document.getElementsByClassName){for(var t=document.getElementsByClassName("CodeMirror"),r=[],n=0;n<t.length;n++){var i=t[n].CodeMirror;i&&r.push(i)}r.length&&r[0].operation(function(){for(var t=0;t<r.length;t++)e(r[t])})}}var iY=!1;function i8(e){var t=e.display;t.cachedCharWidth=t.cachedTextHeight=t.cachedPaddingH=null,t.scrollbarsClipped=!1,e.setSize()}for(var i9={3:"Pause",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",145:"ScrollLock",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",224:"Mod",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},iq=0;iq<10;iq++)i9[iq+48]=i9[iq+96]=String(iq);for(var iZ=65;iZ<=90;iZ++)i9[iZ]=String.fromCharCode(iZ);for(var iQ=1;iQ<=12;iQ++)i9[iQ+111]=i9[iQ+63235]="F"+iQ;var iJ={};function oe(e){var t,r,n,i,o=e.split(/-(?!$)/);e=o[o.length-1];for(var l=0;l<o.length-1;l++){var s=o[l];if(/^(cmd|meta|m)$/i.test(s))i=!0;else if(/^a(lt)?$/i.test(s))t=!0;else if(/^(c|ctrl|control)$/i.test(s))r=!0;else if(/^s(hift)?$/i.test(s))n=!0;else throw Error("Unrecognized modifier name: "+s)}return t&&(e="Alt-"+e),r&&(e="Ctrl-"+e),i&&(e="Cmd-"+e),n&&(e="Shift-"+e),e}function ot(e,t,r,n){var i=(t=oo(t)).call?t.call(e,n):t[e];if(!1===i)return"nothing";if("..."===i)return"multi";if(null!=i&&r(i))return"handled";if(t.fallthrough){if("[object Array]"!=Object.prototype.toString.call(t.fallthrough))return ot(e,t.fallthrough,r,n);for(var o=0;o<t.fallthrough.length;o++){var l=ot(e,t.fallthrough[o],r,n);if(l)return l}}}function or(e){var t="string"==typeof e?e:i9[e.keyCode];return"Ctrl"==t||"Alt"==t||"Shift"==t||"Mod"==t}function on(e,t,r){var n=e;return t.altKey&&"Alt"!=n&&(e="Alt-"+e),(D?t.metaKey:t.ctrlKey)&&"Ctrl"!=n&&(e="Ctrl-"+e),(D?t.ctrlKey:t.metaKey)&&"Mod"!=n&&(e="Cmd-"+e),!r&&t.shiftKey&&"Shift"!=n&&(e="Shift-"+e),e}function oi(e,t){if(x&&34==e.keyCode&&e.char)return!1;var r=i9[e.keyCode];return null!=r&&!e.altGraphKey&&(3==e.keyCode&&e.code&&(r=e.code),on(r,e,t))}function oo(e){return"string"==typeof e?iJ[e]:e}function ol(e,t){for(var r=e.doc.sel.ranges,n=[],i=0;i<r.length;i++){for(var o=t(r[i]);n.length&&0>=tt(o.from,el(n).to);){var l=n.pop();if(0>tt(l.from,o.from)){o.from=l.from;break}}n.push(o)}nM(e,function(){for(var t=n.length-1;t>=0;t--)iH(e.doc,"",n[t].from,n[t].to,"+delete");nu(e)})}function os(e,t,r){var n=ev(e.text,t+r,r);return n<0||n>e.text.length?null:n}function oa(e,t,r){var n=os(e,t.ch,r);return null==n?null:new te(t.line,n,r<0?"after":"before")}function ou(e,t,r,n,i){if(e){"rtl"==t.doc.direction&&(i=-i);var o=eb(r,t.doc.direction);if(o){var l,s=i<0?el(o):o[0],a=i<0==(1==s.level)?"after":"before";if(s.level>0||"rtl"==t.doc.direction){var u=rb(t,r),c=rx(t,u,l=i<0?r.text.length-1:0).top;l=em(function(e){return rx(t,u,e).top==c},i<0==(1==s.level)?s.from:s.to-1,l),"before"==a&&(l=os(r,l,1))}else l=i<0?s.to:s.from;return new te(n,l,a)}}return new te(n,i<0?r.text.length:0,i<0?"before":"after")}iJ.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},iJ.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},iJ.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},iJ.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},iJ.default=N?iJ.macDefault:iJ.pcDefault;var oc={selectAll:iN,singleSelection:function(e){return e.setSelection(e.getCursor("anchor"),e.getCursor("head"),ee)},killLine:function(e){return ol(e,function(t){if(!t.empty())return{from:t.from(),to:t.to()};var r=ej(e.doc,t.head.line).text.length;return t.head.ch==r&&t.head.line<e.lastLine()?{from:t.head,to:te(t.head.line+1,0)}:{from:t.head,to:te(t.head.line,r)}})},deleteLine:function(e){return ol(e,function(t){return{from:te(t.from().line,0),to:ts(e.doc,te(t.to().line+1,0))}})},delLineLeft:function(e){return ol(e,function(e){return{from:te(e.from().line,0),to:e.from()}})},delWrappedLineLeft:function(e){return ol(e,function(t){var r=e.charCoords(t.head,"div").top+5;return{from:e.coordsChar({left:0,top:r},"div"),to:t.from()}})},delWrappedLineRight:function(e){return ol(e,function(t){var r=e.charCoords(t.head,"div").top+5,n=e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:r},"div");return{from:t.from(),to:n}})},undo:function(e){return e.undo()},redo:function(e){return e.redo()},undoSelection:function(e){return e.undoSelection()},redoSelection:function(e){return e.redoSelection()},goDocStart:function(e){return e.extendSelection(te(e.firstLine(),0))},goDocEnd:function(e){return e.extendSelection(te(e.lastLine()))},goLineStart:function(e){return e.extendSelectionsBy(function(t){return of(e,t.head.line)},{origin:"+move",bias:1})},goLineStartSmart:function(e){return e.extendSelectionsBy(function(t){return oh(e,t.head)},{origin:"+move",bias:1})},goLineEnd:function(e){return e.extendSelectionsBy(function(t){var r,n,i,o;return r=e,n=t.head.line,i=ej(r.doc,n),o=function e(t){for(var r;r=tW(t);)t=r.find(1,!0).line;return t}(i),o!=i&&(n=eq(o)),ou(!0,r,i,n,-1)},{origin:"+move",bias:-1})},goLineRight:function(e){return e.extendSelectionsBy(function(t){var r=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:r},"div")},er)},goLineLeft:function(e){return e.extendSelectionsBy(function(t){var r=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:0,top:r},"div")},er)},goLineLeftSmart:function(e){return e.extendSelectionsBy(function(t){var r=e.cursorCoords(t.head,"div").top+5,n=e.coordsChar({left:0,top:r},"div");return n.ch<e.getLine(n.line).search(/\S/)?oh(e,t.head):n},er)},goLineUp:function(e){return e.moveV(-1,"line")},goLineDown:function(e){return e.moveV(1,"line")},goPageUp:function(e){return e.moveV(-1,"page")},goPageDown:function(e){return e.moveV(1,"page")},goCharLeft:function(e){return e.moveH(-1,"char")},goCharRight:function(e){return e.moveH(1,"char")},goColumnLeft:function(e){return e.moveH(-1,"column")},goColumnRight:function(e){return e.moveH(1,"column")},goWordLeft:function(e){return e.moveH(-1,"word")},goGroupRight:function(e){return e.moveH(1,"group")},goGroupLeft:function(e){return e.moveH(-1,"group")},goWordRight:function(e){return e.moveH(1,"word")},delCharBefore:function(e){return e.deleteH(-1,"codepoint")},delCharAfter:function(e){return e.deleteH(1,"char")},delWordBefore:function(e){return e.deleteH(-1,"word")},delWordAfter:function(e){return e.deleteH(1,"word")},delGroupBefore:function(e){return e.deleteH(-1,"group")},delGroupAfter:function(e){return e.deleteH(1,"group")},indentAuto:function(e){return e.indentSelection("smart")},indentMore:function(e){return e.indentSelection("add")},indentLess:function(e){return e.indentSelection("subtract")},insertTab:function(e){return e.replaceSelection("	")},insertSoftTab:function(e){for(var t=[],r=e.listSelections(),n=e.options.tabSize,i=0;i<r.length;i++){var o=r[i].from(),l=q(e.getLine(o.line),o.ch,n);t.push(eo(n-l%n))}e.replaceSelections(t)},defaultTab:function(e){e.somethingSelected()?e.indentSelection("add"):e.execCommand("insertTab")},transposeChars:function(e){return nM(e,function(){for(var t=e.listSelections(),r=[],n=0;n<t.length;n++)if(t[n].empty()){var i=t[n].head,o=ej(e.doc,i.line).text;if(o){if(i.ch==o.length&&(i=new te(i.line,i.ch-1)),i.ch>0)i=new te(i.line,i.ch+1),e.replaceRange(o.charAt(i.ch-1)+o.charAt(i.ch-2),te(i.line,i.ch-2),i,"+transpose");else if(i.line>e.doc.first){var l=ej(e.doc,i.line-1).text;l&&(i=new te(i.line,1),e.replaceRange(o.charAt(0)+e.doc.lineSeparator()+l.charAt(l.length-1),te(i.line-1,l.length-1),i,"+transpose"))}}r.push(new nK(i,i))}e.setSelections(r)})},newlineAndIndent:function(e){return nM(e,function(){for(var t=e.listSelections(),r=t.length-1;r>=0;r--)e.replaceRange(e.doc.lineSeparator(),t[r].anchor,t[r].head,"+input");t=e.listSelections();for(var n=0;n<t.length;n++)e.indentLine(t[n].from().line,null,!0);nu(e)})},openLine:function(e){return e.replaceSelection("\n","start")},toggleOverwrite:function(e){return e.toggleOverwrite()}};function of(e,t){var r=ej(e.doc,t),n=tP(r);return n!=r&&(t=eq(n)),ou(!0,e,n,t,1)}function oh(e,t){var r=of(e,t.line),n=ej(e.doc,r.line),i=eb(n,e.doc.direction);if(!i||0==i[0].level){var o=Math.max(r.ch,n.text.search(/\S/)),l=t.line==r.line&&t.ch<=o&&t.ch;return te(r.line,l?0:o,r.sticky)}return r}function od(e,t,r){if("string"==typeof t&&!(t=oc[t]))return!1;e.display.input.ensurePolled();var n=e.display.shift,i=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),r&&(e.display.shift=!1),i=t(e)!=J}finally{e.display.shift=n,e.state.suppressEdits=!1}return i}var op=new Z;function og(e,t,r,n){var i=e.state.keySeq;if(i){if(or(t))return"handled";if(/\'$/.test(t)?e.state.keySeq=null:op.set(50,function(){e.state.keySeq==i&&(e.state.keySeq=null,e.display.input.reset())}),ov(e,i+" "+t,r,n))return!0}return ov(e,t,r,n)}function ov(e,t,r,n){var i=function e(t,r,n){for(var i=0;i<t.state.keyMaps.length;i++){var o=ot(r,t.state.keyMaps[i],n,t);if(o)return o}return t.options.extraKeys&&ot(r,t.options.extraKeys,n,t)||ot(r,t.options.keyMap,n,t)}(e,t,n);return"multi"==i&&(e.state.keySeq=t),"handled"==i&&tZ(e,"keyHandled",e,t,r),("handled"==i||"multi"==i)&&(eM(r),rJ(e)),!!i}function om(e,t){var r=oi(t,!0);return!!r&&(t.shiftKey&&!e.state.keySeq?og(e,"Shift-"+r,t,function(t){return od(e,t,!0)})||og(e,r,t,function(t){if("string"==typeof t?/^go[A-Z]/.test(t):t.motion)return od(e,t)}):og(e,r,t,function(t){return od(e,t)}))}var o$=null;function oy(e){var t=this;if(!e.target||e.target==t.display.input.getField()){if(t.curOp.focus=B(K(t)),!ek(t,e)){v&&m<11&&27==e.keyCode&&(e.returnValue=!1);var r=e.keyCode;t.display.shift=16==r||e.shiftKey;var n=om(t,e);x&&(o$=n?r:null,!n&&88==r&&!eI&&(N?e.metaKey:e.ctrlKey)&&t.replaceSelection("",null,"cut")),h&&!N&&!n&&46==r&&e.shiftKey&&!e.ctrlKey&&document.execCommand&&document.execCommand("cut"),18!=r||/\bCodeMirror-crosshair\b/.test(t.display.lineDiv.className)||function e(t){var r=t.display.lineDiv;function n(e){18!=e.keyCode&&e.altKey||(F(r,"CodeMirror-crosshair"),eS(document,"keyup",n),eS(document,"mouseover",n))}G(r,"CodeMirror-crosshair"),ew(document,"keyup",n),ew(document,"mouseover",n)}(t)}}}function o_(e){16==e.keyCode&&(this.doc.sel.shift=!1),ek(this,e)}function ob(e){if(!(e.target&&e.target!=this.display.input.getField()||rf(this.display,e)||ek(this,e)||e.ctrlKey&&!e.altKey)&&(!N||!e.metaKey)){var t,r,n,i=e.keyCode,o=e.charCode;if(x&&i==o$){o$=null,eM(e);return}if(!(x&&(!e.which||e.which<10)&&om(this,e))){var l=String.fromCharCode(null==o?i:o);if("\b"!=l)t=this,r=e,!og(t,"'"+(n=l)+"'",r,function(e){return od(t,e,!0)})&&this.display.input.onKeyPress(e)}}}var ox=function(e,t,r){this.time=e,this.pos=t,this.button=r};function ow(e){var t,r,n,i,o,l,s,c,f,h,d,p,g,y,_,b,x=this.display;if(!(ek(this,e)||x.activeTouch&&x.input.supportsTouch())){if(x.input.ensurePolled(),x.shift=e.shiftKey,rf(x,e)){$||(x.scroller.draggable=!1,setTimeout(function(){return x.scroller.draggable=!0},100));return}if(!oL(this,e)){var C,S,L,k,T,M,A,D,H,F=rG(this,e),P=eH(e),E=F?(C=F,S=P,L=+new Date,u&&u.compare(L,C,S)?(a=u=null,"triple"):a&&a.compare(L,C,S)?(u=new ox(L,C,S),a=null,"double"):(a=new ox(L,C,S),u=null,"single")):"single";if(X(this).focus(),1==P&&this.state.selectingText&&this.state.selectingText(e),!(F&&(k=this,T=P,M=F,A=E,D=e,H="Click","double"==A?H="Double"+H:"triple"==A&&(H="Triple"+H),og(k,on(H=(1==T?"Left":2==T?"Middle":"Right")+H,D),D,function(e){if("string"==typeof e&&(e=oc[e]),!e)return!1;var t=!1;try{k.isReadOnly()&&(k.state.suppressEdits=!0),t=e(k,M)!=J}finally{k.state.suppressEdits=!1}return t})))){1==P?F?(t=this,r=F,n=E,i=e,v?setTimeout(j(ne,t),0):t.curOp.focus=B(K(t)),l=function e(t,r,n){var i=t.getOption("configureMouse"),o=i?i(t,r,n):{};if(null==o.unit){var l=O?n.shiftKey&&n.metaKey:n.altKey;o.unit=l?"rectangle":"single"==r?"char":"double"==r?"word":"line"}return(null==o.extend||t.doc.extend)&&(o.extend=t.doc.extend||n.shiftKey),null==o.addNew&&(o.addNew=N?n.metaKey:n.ctrlKey),null==o.moveOnDrag&&(o.moveOnDrag=!(N?n.altKey:n.ctrlKey)),o}(t,n,i),s=t.doc.sel,t.options.dragDrop&&eF&&!t.isReadOnly()&&"single"==n&&(o=s.contains(r))>-1&&(0>tt((o=s.ranges[o]).from(),r)||r.xRel>0)&&(tt(o.to(),r)>0||r.xRel<0)?(c=t,f=i,h=r,d=l,p=c.display,g=!1,y=nA(c,function(e){$&&(p.scroller.draggable=!1),c.state.draggingText=!1,c.state.delayingBlurEvent&&(c.hasFocus()?c.state.delayingBlurEvent=!1:nt(c)),eS(p.wrapper.ownerDocument,"mouseup",y),eS(p.wrapper.ownerDocument,"mousemove",_),eS(p.scroller,"dragstart",b),eS(p.scroller,"drop",y),g||(eM(e),d.addNew||iv(c.doc,h,null,null,d.extend),$&&!w||v&&9==m?setTimeout(function(){p.wrapper.ownerDocument.body.focus({preventScroll:!0}),p.input.focus()},20):p.input.focus())}),_=function(e){g=g||Math.abs(f.clientX-e.clientX)+Math.abs(f.clientY-e.clientY)>=10},b=function(){return g=!0},$&&(p.scroller.draggable=!0),c.state.draggingText=y,y.copy=!d.moveOnDrag,ew(p.wrapper.ownerDocument,"mouseup",y),ew(p.wrapper.ownerDocument,"mousemove",_),ew(p.scroller,"dragstart",b),ew(p.scroller,"drop",y),c.state.delayingBlurEvent=!0,setTimeout(function(){return p.input.focus()},20),p.scroller.dragDrop&&p.scroller.dragDrop()):function e(t,r,n,i){v&&nt(t);var o=t.display,l=t.doc;eM(r);var s,a,u=l.sel,c=u.ranges;if(i.addNew&&!i.extend?s=(a=l.sel.contains(n))>-1?c[a]:new nK(n,n):(s=l.sel.primary(),a=l.sel.primIndex),"rectangle"==i.unit)i.addNew||(s=new nK(n,n)),n=rG(t,r,!0,!0),a=-1;else{var f=oC(t,n,i.unit);s=i.extend?ig(s,f.anchor,f.head,i.extend):f}i.addNew?-1==a?(a=c.length,ib(l,nX(t,c.concat([s]),a),{scroll:!1,origin:"*mouse"})):c.length>1&&c[a].empty()&&"char"==i.unit&&!i.extend?(ib(l,nX(t,c.slice(0,a).concat(c.slice(a+1)),0),{scroll:!1,origin:"*mouse"}),u=l.sel):i$(l,a,s,et):(a=0,ib(l,new nV([s],0),et),u=l.sel);var h=n,d=o.wrapper.getBoundingClientRect(),p=0;function g(e){t.state.selectingText=!1,p=1/0,e&&(eM(e),o.input.focus()),eS(o.wrapper.ownerDocument,"mousemove",m),eS(o.wrapper.ownerDocument,"mouseup",$),l.history.lastSelOrigin=null}var m=nA(t,function(e){0!==e.buttons&&eH(e)?function e(r){var c=++p,f=rG(t,r,!0,"rectangle"==i.unit);if(f){if(0!=tt(f,h)){t.curOp.focus=B(K(t)),function e(r){if(0!=tt(h,r)){if(h=r,"rectangle"==i.unit){for(var o=[],c=t.options.tabSize,f=q(ej(l,n.line).text,n.ch,c),d=q(ej(l,r.line).text,r.ch,c),p=Math.min(f,d),g=Math.max(f,d),v=Math.min(n.line,r.line),m=Math.min(t.lastLine(),Math.max(n.line,r.line));v<=m;v++){var $=ej(l,v).text,y=en($,p,c);p==g?o.push(new nK(te(v,y),te(v,y))):$.length>y&&o.push(new nK(te(v,y),te(v,en($,g,c))))}o.length||o.push(new nK(n,n)),ib(l,nX(t,u.ranges.slice(0,a).concat(o),a),{origin:"*mouse",scroll:!1}),t.scrollIntoView(r)}else{var _,b=s,x=oC(t,r,i.unit),w=b.anchor;tt(x.anchor,w)>0?(_=x.head,w=to(b.from(),x.anchor)):(_=x.anchor,w=ti(b.to(),x.head));var C=u.ranges.slice(0);C[a]=function e(t,r){var n,i=r.anchor,o=r.head,l=ej(t.doc,i.line);if(0==tt(i,o)&&i.sticky==o.sticky)return r;var s=eb(l);if(!s)return r;var a=ey(s,i.ch,i.sticky),u=s[a];if(u.from!=i.ch&&u.to!=i.ch)return r;var c=a+(u.from==i.ch==(1!=u.level)?0:1);if(0==c||c==s.length)return r;if(o.line!=i.line)n=(o.line-i.line)*("ltr"==t.doc.direction?1:-1)>0;else{var f=ey(s,o.ch,o.sticky),h=f-a||(o.ch-i.ch)*(1==u.level?-1:1);n=f==c-1||f==c?h<0:h>0}var d=s[c+(n?-1:0)],p=n==(1==d.level),g=p?d.from:d.to,v=p?"after":"before";return i.ch==g&&i.sticky==v?r:new nK(new te(i.line,g,v),o)}(t,new nK(ts(l,w),_)),ib(l,nX(t,C,a),et)}}}(f);var g=nl(o,l);(f.line>=g.to||f.line<g.from)&&setTimeout(nA(t,function(){p==c&&e(r)}),150)}else{var v=r.clientY<d.top?-20:r.clientY>d.bottom?20:0;v&&setTimeout(nA(t,function(){p==c&&(o.scroller.scrollTop+=v,e(r))}),50)}}}(e):g(e)}),$=nA(t,g);t.state.selectingText=$,ew(o.wrapper.ownerDocument,"mousemove",m),ew(o.wrapper.ownerDocument,"mouseup",$)}(t,i,r,l)):eW(e)==x.scroller&&eM(e):2==P?(F&&iv(this.doc,F),setTimeout(function(){return x.input.focus()},20)):3==P&&(W?this.display.input.onContextMenu(e):nt(this))}}}}function oC(e,t,r){if("char"==r)return new nK(t,t);if("word"==r)return e.findWordAt(t);if("line"==r)return new nK(te(t.line,0),ts(e.doc,te(t.line+1,0)));var n=r(e,t);return new nK(n.from,n.to)}function oS(e,t,r,n){if(t.touches)o=t.touches[0].clientX,l=t.touches[0].clientY;else try{o=t.clientX,l=t.clientY}catch(i){return!1}if(o>=Math.floor(e.display.gutters.getBoundingClientRect().right))return!1;n&&eM(t);var o,l,s=e.display,a=s.lineDiv.getBoundingClientRect();if(l>a.bottom||!eN(e,r))return e0(t);l-=a.top-s.viewOffset;for(var u=0;u<e.display.gutterSpecs.length;++u){var c=s.gutters.childNodes[u];if(c&&c.getBoundingClientRect().right>=o){var f=eZ(e.doc,l),h=e.display.gutterSpecs[u];return eL(e,r,e,f,h.className,t),e0(t)}}}function oL(e,t){return oS(e,t,"gutterClick",!0)}function ok(e,t){var r,n;if(!(rf(e.display,t)||(r=e,n=t,eN(r,"gutterContextMenu")&&oS(r,n,"gutterContextMenu",!1))||ek(e,t,"contextmenu")))W||e.display.input.onContextMenu(t)}function oT(e){e.display.wrapper.className=e.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+e.options.theme.replace(/(^|\s)\s*/g," cm-s-"),rT(e)}ox.prototype.compare=function(e,t,r){return this.time+400>e&&0==tt(t,this.pos)&&r==this.button};var oN={toString:function(){return"CodeMirror.Init"}},oO={},oM={};function oA(e,t,r){if(!t!=!(r&&r!=oN)){var n=e.display.dragFunctions,i=t?ew:eS;i(e.display.scroller,"dragstart",n.start),i(e.display.scroller,"dragenter",n.enter),i(e.display.scroller,"dragover",n.over),i(e.display.scroller,"dragleave",n.leave),i(e.display.scroller,"drop",n.drop)}}function o0(e){e.options.lineWrapping?(G(e.display.wrapper,"CodeMirror-wrap"),e.display.sizer.style.minWidth="",e.display.sizerWidth=null):(F(e.display.wrapper,"CodeMirror-wrap"),tB(e)),r2(e),rV(e),rT(e),setTimeout(function(){return ny(e)},100)}function oD(e,t){var r=this;if(!(this instanceof oD))return new oD(e,t);this.options=t=t?Y(t):{},Y(oO,t,!1);var n=t.value;"string"==typeof n?n=new iU(n,t.mode,null,t.lineSeparator,t.direction):t.mode&&(n.modeOption=t.mode),this.doc=n;var i=new oD.inputStyles[t.inputStyle](this),o=this.display=new n6(e,n,i,t);for(var l in o.wrapper.CodeMirror=this,oT(this),t.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),nx(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:-1,cutIncoming:-1,selectingText:!1,draggingText:!1,highlight:new Z,keySeq:null,specialChars:null},t.autofocus&&!T&&o.input.focus(),v&&m<11&&setTimeout(function(){return r.display.input.reset(!0)},20),function e(t){var r=t.display;ew(r.scroller,"mousedown",nA(t,ow)),v&&m<11?ew(r.scroller,"dblclick",nA(t,function(e){if(!ek(t,e)){var r=rG(t,e);if(!(!r||oL(t,e)||rf(t.display,e))){eM(e);var n=t.findWordAt(r);iv(t.doc,n.anchor,n.head)}}})):ew(r.scroller,"dblclick",function(e){return ek(t,e)||eM(e)}),ew(r.scroller,"contextmenu",function(e){return ok(t,e)}),ew(r.input.getField(),"contextmenu",function(e){r.scroller.contains(e.target)||ok(t,e)});var n,i={end:0};function o(){r.activeTouch&&(n=setTimeout(function(){return r.activeTouch=null},1e3),(i=r.activeTouch).end=+new Date)}function l(e,t){if(null==t.left)return!0;var r=t.left-e.left,n=t.top-e.top;return r*r+n*n>400}ew(r.scroller,"touchstart",function(e){if(!ek(t,e)&&!function e(t){if(1!=t.touches.length)return!1;var r=t.touches[0];return r.radiusX<=1&&r.radiusY<=1}(e)&&!oL(t,e)){r.input.ensurePolled(),clearTimeout(n);var o=+new Date;r.activeTouch={start:o,moved:!1,prev:o-i.end<=300?i:null},1==e.touches.length&&(r.activeTouch.left=e.touches[0].pageX,r.activeTouch.top=e.touches[0].pageY)}}),ew(r.scroller,"touchmove",function(){r.activeTouch&&(r.activeTouch.moved=!0)}),ew(r.scroller,"touchend",function(e){var n=r.activeTouch;if(n&&!rf(r,e)&&null!=n.left&&!n.moved&&new Date-n.start<300){var i,s=t.coordsChar(r.activeTouch,"page");i=!n.prev||l(n,n.prev)?new nK(s,s):!n.prev.prev||l(n,n.prev.prev)?t.findWordAt(s):new nK(te(s.line,0),ts(t.doc,te(s.line+1,0))),t.setSelection(i.anchor,i.head),t.focus(),eM(e)}o()}),ew(r.scroller,"touchcancel",o),ew(r.scroller,"scroll",function(){r.scroller.clientHeight&&(nd(t,r.scroller.scrollTop),ng(t,r.scroller.scrollLeft,!0),eL(t,"scroll",t))}),ew(r.scroller,"mousewheel",function(e){return nU(t,e)}),ew(r.scroller,"DOMMouseScroll",function(e){return nU(t,e)}),ew(r.wrapper,"scroll",function(){return r.wrapper.scrollTop=r.wrapper.scrollLeft=0}),r.dragFunctions={enter:function(e){ek(t,e)||eD(e)},over:function(e){ek(t,e)||(function e(t,r){var n=rG(t,r);if(n){var i=document.createDocumentFragment();rq(t,n,i),t.display.dragCursor||(t.display.dragCursor=z("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),t.display.lineSpace.insertBefore(t.display.dragCursor,t.display.cursorDiv)),E(t.display.dragCursor,i)}}(t,e),eD(e))},start:function(e){return function e(t,r){if(v&&(!t.state.draggingText||+new Date-iV<100)){eD(r);return}if(!(ek(t,r)||rf(t.display,r))&&(r.dataTransfer.setData("Text",t.getSelection()),r.dataTransfer.effectAllowed="copyMove",r.dataTransfer.setDragImage&&!w)){var n=z("img",null,null,"position: fixed; left: 0; top: 0;");n.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",x&&(n.width=n.height=1,t.display.wrapper.appendChild(n),n._top=n.offsetTop),r.dataTransfer.setDragImage(n,0,0),x&&n.parentNode.removeChild(n)}}(t,e)},drop:nA(t,iK),leave:function(e){ek(t,e)||iX(t)}};var s=r.input.getField();ew(s,"keyup",function(e){return o_.call(t,e)}),ew(s,"keydown",nA(t,oy)),ew(s,"keypress",nA(t,ob)),ew(s,"focus",function(e){return nr(t,e)}),ew(s,"blur",function(e){return nn(t,e)})}(this),!function e(){if(!iY){var t;ew(window,"resize",function(){null==t&&(t=setTimeout(function(){t=null,ij(i8)},100))}),ew(window,"blur",function(){return ij(nn)}),iY=!0}}(),nC(this),this.curOp.forceUpdate=!0,ir(this,n),t.autofocus&&!T||this.hasFocus()?setTimeout(function(){r.hasFocus()&&!r.state.focused&&nr(r)},20):nn(this),oM)oM.hasOwnProperty(l)&&oM[l](this,t[l],oN);n3(this),t.finishInit&&t.finishInit(this);for(var s=0;s<oW.length;++s)oW[s](this);nS(this),$&&t.lineWrapping&&"optimizelegibility"==getComputedStyle(o.lineDiv).textRendering&&(o.lineDiv.style.textRendering="auto")}oD.defaults=oO,oD.optionHandlers=oM;var oW=[];function oH(e,t,r,n){var i,o=e.doc;null==r&&(r="add"),"smart"==r&&(o.mode.indent?i=td(e,t).state:r="prev");var l=e.options.tabSize,s=ej(o,t),a=q(s.text,null,l);s.stateAfter&&(s.stateAfter=null);var u,c=s.text.match(/^\s*/)[0];if(n||/\S/.test(s.text)){if("smart"==r&&((u=o.mode.indent(i,s.text.slice(c.length),s.text))==J||u>150)){if(!n)return;r="prev"}}else u=0,r="not";"prev"==r?u=t>o.first?q(ej(o,t-1).text,null,l):0:"add"==r?u=a+e.options.indentUnit:"subtract"==r?u=a-e.options.indentUnit:"number"==typeof r&&(u=a+r),u=Math.max(0,u);var f="",h=0;if(e.options.indentWithTabs)for(var d=Math.floor(u/l);d;--d)h+=l,f+="	";if(h<u&&(f+=eo(u-h)),f!=c)return iH(o,f,te(t,0),te(t,c.length),"+input"),s.stateAfter=null,!0;for(var p=0;p<o.sel.ranges.length;p++){var g=o.sel.ranges[p];if(g.head.line==t&&g.head.ch<c.length){var v=te(t,c.length);i$(o,p,new nK(v,v));break}}}oD.defineInitHook=function(e){return oW.push(e)};var oF=null;function oP(e){oF=e}function o1(e,t,r,n,i){var o=e.doc;e.display.shift=!1,n||(n=o.sel);var l=+new Date-200,s="paste"==i||e.state.pasteIncoming>l,a=ez(t),u=null;if(s&&n.ranges.length>1){if(oF&&oF.text.join("\n")==t){if(n.ranges.length%oF.text.length==0){u=[];for(var c=0;c<oF.text.length;c++)u.push(o.splitLines(oF.text[c]))}}else a.length==n.ranges.length&&e.options.pasteLinesPerSelection&&(u=es(a,function(e){return[e]}))}for(var f=e.curOp.updateInput,h=n.ranges.length-1;h>=0;h--){var d=n.ranges[h],p=d.from(),g=d.to();d.empty()&&(r&&r>0?p=te(p.line,p.ch-r):e.state.overwrite&&!s?g=te(g.line,Math.min(ej(o,g.line).text.length,g.ch+el(a).length)):s&&oF&&oF.lineWise&&oF.text.join("\n")==a.join("\n")&&(p=g=te(p.line,0)));var v={from:p,to:g,text:u?u[h%u.length]:a,origin:i||(s?"paste":e.state.cutIncoming>l?"cut":"+input")};iM(e.doc,v),tZ(e,"inputRead",e,v)}t&&!s&&oz(e,t),nu(e),e.curOp.updateInput<2&&(e.curOp.updateInput=f),e.curOp.typing=!0,e.state.pasteIncoming=e.state.cutIncoming=-1}function oE(e,t){var r=e.clipboardData&&e.clipboardData.getData("Text");if(r)return e.preventDefault(),!t.isReadOnly()&&!t.options.disableInput&&t.hasFocus()&&nM(t,function(){return o1(t,r,0,null,"paste")}),!0}function oz(e,t){if(e.options.electricChars&&e.options.smartIndent)for(var r=e.doc.sel,n=r.ranges.length-1;n>=0;n--){var i=r.ranges[n];if(!(i.head.ch>100)&&(!n||r.ranges[n-1].head.line!=i.head.line)){var o=e.getModeAt(i.head),l=!1;if(o.electricChars){for(var s=0;s<o.electricChars.length;s++)if(t.indexOf(o.electricChars.charAt(s))>-1){l=oH(e,i.head.line,"smart");break}}else o.electricInput&&o.electricInput.test(ej(e.doc,i.head.line).text.slice(0,i.head.ch))&&(l=oH(e,i.head.line,"smart"));l&&tZ(e,"electricInput",e,i.head.line)}}}function oR(e){for(var t=[],r=[],n=0;n<e.doc.sel.ranges.length;n++){var i=e.doc.sel.ranges[n].head.line,o={anchor:te(i,0),head:te(i+1,0)};r.push(o),t.push(e.getRange(o.anchor,o.head))}return{text:t,ranges:r}}function oI(e,t,r,n){e.setAttribute("autocorrect",r?"on":"off"),e.setAttribute("autocapitalize",n?"on":"off"),e.setAttribute("spellcheck",!!t)}function o3(){var e=z("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; min-height: 1em; outline: none"),t=z("div",[e],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return $?e.style.width="1000px":e.setAttribute("wrap","off"),L&&(e.style.border="1px solid black"),t}function oB(e,t,r,n,i){var o=t,l=r,s=ej(e,t.line),a=i&&"rtl"==e.direction?-r:r;function u(o){if("codepoint"==n){var l,u,c=s.text.charCodeAt(t.ch+(r>0?0:-1));if(isNaN(c))l=null;else{var f=r>0?c>=55296&&c<56320:c>=56320&&c<57343;l=new te(t.line,Math.max(0,Math.min(s.text.length,t.ch+r*(f?2:1))),-r)}}else l=i?function e(t,r,n,i){var o,l=eb(r,t.doc.direction);if(!l)return oa(r,n,i);n.ch>=r.text.length?(n.ch=r.text.length,n.sticky="before"):n.ch<=0&&(n.ch=0,n.sticky="after");var s=ey(l,n.ch,n.sticky),a=l[s];if("ltr"==t.doc.direction&&a.level%2==0&&(i>0?a.to>n.ch:a.from<n.ch))return oa(r,n,i);var u=function(e,t){return os(r,e instanceof te?e.ch:e,t)},c=function(e){return t.options.lineWrapping?(o=o||rb(t,r),rE(t,r,o,e)):{begin:0,end:r.text.length}},f=c("before"==n.sticky?u(n,-1):n.ch);if("rtl"==t.doc.direction||1==a.level){var h=1==a.level==i<0,d=u(n,h?1:-1);if(null!=d&&(h?d<=a.to&&d<=f.end:d>=a.from&&d>=f.begin))return new te(n.line,d,h?"before":"after")}var p=function(e,t,r){for(var i=function(e,t){return t?new te(n.line,u(e,1),"before"):new te(n.line,e,"after")};e>=0&&e<l.length;e+=t){var o=l[e],s=t>0==(1!=o.level),a=s?r.begin:u(r.end,-1);if(o.from<=a&&a<o.to||(a=s?o.from:u(o.to,-1),r.begin<=a&&a<r.end))return i(a,s)}},g=p(s+i,i,f);if(g)return g;var v=i>0?f.end:u(f.begin,-1);return null!=v&&!(i>0&&v==r.text.length)&&(g=p(i>0?0:l.length-1,i,c(v)))?g:null}(e.cm,s,t,r):oa(s,t,r);if(null==l){if(!(!o&&!((u=t.line+a)<e.first)&&!(u>=e.first+e.size)&&(t=new te(u,t.ch,t.sticky),s=ej(e,u))))return!1;t=ou(i,e.cm,s,t.line,a)}else t=l;return!0}if("char"==n||"codepoint"==n)u();else if("column"==n)u(!0);else if("word"==n||"group"==n)for(var c=null,f="group"==n,h=e.cm&&e.cm.getHelper(t,"wordChars"),d=!0;!(r<0)||u(!d);d=!1){var p=s.text.charAt(t.ch)||"\n",g=eh(p,h)?"w":f&&"\n"==p?"n":!f||/\s/.test(p)?null:"p";if(!f||d||g||(g="s"),c&&c!=g){r<0&&(r=1,u(),t.sticky="after");break}if(g&&(c=g),r>0&&!u(!d))break}var v=ik(e,t,o,l,!0);return tr(o,v)&&(v.hitSide=!0),v}function o7(e,t,r,n){var i,o,l=e.doc,s=t.left;if("page"==n){var a=Math.max(Math.min(e.display.wrapper.clientHeight,X(e).innerHeight||l(e).documentElement.clientHeight)-.5*rB(e.display),3);o=(r>0?t.bottom:t.top)+r*a}else"line"==n&&(o=r>0?t.bottom+3:t.top-3);for(;(i=rP(e,s,o)).outside;){if(r<0?o<=0:o>=l.height){i.hitSide=!0;break}o+=5*r}return i}var o4=function(e){this.cm=e,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new Z,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null};function o6(e,t){var r=r_(e,t.line);if(!r||r.hidden)return null;var n=ej(e.doc,t.line),i=r$(r,n,t.line),o=eb(n,e.doc.direction),l="left";o&&(l=ey(o,t.ch)%2?"right":"left");var s=rC(i.map,t.ch,l);return s.offset="right"==s.collapse?s.end:s.start,s}function o5(e,t){return t&&(e.bad=!0),e}function o2(e,t,r){var n;if(t==e.display.lineDiv){if(!(n=e.display.lineDiv.childNodes[r]))return o5(e.clipPos(te(e.display.viewTo-1)),!0);t=null,r=0}else for(n=t;;n=n.parentNode){if(!n||n==e.display.lineDiv)return null;if(n.parentNode&&n.parentNode==e.display.lineDiv)break}for(var i=0;i<e.display.view.length;i++){var o=e.display.view[i];if(o.node==n)return oG(o,t,r)}}function oG(e,t,r){var n=e.text.firstChild,i=!1;if(!t||!I(n,t))return o5(te(eq(e.line),0),!0);if(t==n&&(i=!0,t=n.childNodes[r],r=0,!t)){var o=e.rest?el(e.rest):e.line;return o5(te(eq(o),o.text.length),i)}var l=3==t.nodeType?t:null,s=t;for(!l&&1==t.childNodes.length&&3==t.firstChild.nodeType&&(l=t.firstChild,r&&(r=l.nodeValue.length));s.parentNode!=n;)s=s.parentNode;var a=e.measure,u=a.maps;function c(t,r,n){for(var i=-1;i<(u?u.length:0);i++)for(var o=i<0?a.map:u[i],l=0;l<o.length;l+=3){var s=o[l+2];if(s==t||s==r){var c=eq(i<0?e.line:e.rest[i]),f=o[l]+n;return(n<0||s!=t)&&(f=o[l+(n?1:0)]),te(c,f)}}}var f=c(l,s,r);if(f)return o5(f,i);for(var h=s.nextSibling,d=l?l.nodeValue.length-r:0;h;h=h.nextSibling){if(f=c(h,h.firstChild,0))return o5(te(f.line,f.ch-d),i);d+=h.textContent.length}for(var p=s.previousSibling,g=r;p;p=p.previousSibling){if(f=c(p,p.firstChild,-1))return o5(te(f.line,f.ch+g),i);g+=p.textContent.length}}o4.prototype.init=function(e){var t=this,r=this,n=r.cm,i=r.div=e.lineDiv;function o(e){for(var t=e.target;t;t=t.parentNode){if(t==i)return!0;if(/\bCodeMirror-(?:line)?widget\b/.test(t.className))break}return!1}function l(e){if(!(!o(e)||ek(n,e))){if(n.somethingSelected()){var t;oF=t={lineWise:!1,text:n.getSelections()},"cut"==e.type&&n.replaceSelection("",null,"cut")}else{if(!n.options.lineWiseCopyCut)return;var l,s=oR(n);oF=l={lineWise:!0,text:s.text},"cut"==e.type&&n.operation(function(){n.setSelections(s.ranges,0,ee),n.replaceSelection("",null,"cut")})}if(e.clipboardData){e.clipboardData.clearData();var a=oF.text.join("\n");if(e.clipboardData.setData("Text",a),e.clipboardData.getData("Text")==a){e.preventDefault();return}}var u=o3(),c=u.firstChild;oI(c),n.display.lineSpace.insertBefore(u,n.display.lineSpace.firstChild),c.value=oF.text.join("\n");var f=B(i.ownerDocument);V(c),setTimeout(function(){n.display.lineSpace.removeChild(u),f.focus(),f==i&&r.showPrimarySelection()},50)}}i.contentEditable=!0,oI(i,n.options.spellcheck,n.options.autocorrect,n.options.autocapitalize),ew(i,"paste",function(e){!(!o(e)||ek(n,e)||oE(e,n))&&m<=11&&setTimeout(nA(n,function(){return t.updateFromDOM()}),20)}),ew(i,"compositionstart",function(e){t.composing={data:e.data,done:!1}}),ew(i,"compositionupdate",function(e){t.composing||(t.composing={data:e.data,done:!1})}),ew(i,"compositionend",function(e){t.composing&&(e.data!=t.composing.data&&t.readFromDOMSoon(),t.composing.done=!0)}),ew(i,"touchstart",function(){return r.forceCompositionEnd()}),ew(i,"input",function(){t.composing||t.readFromDOMSoon()}),ew(i,"copy",l),ew(i,"cut",l)},o4.prototype.screenReaderLabelChanged=function(e){e?this.div.setAttribute("aria-label",e):this.div.removeAttribute("aria-label")},o4.prototype.prepareSelection=function(){var e=r9(this.cm,!1);return e.focus=B(this.div.ownerDocument)==this.div,e},o4.prototype.showSelection=function(e,t){e&&this.cm.display.view.length&&((e.focus||t)&&this.showPrimarySelection(),this.showMultipleSelections(e))},o4.prototype.getSelection=function(){return this.cm.display.wrapper.ownerDocument.getSelection()},o4.prototype.showPrimarySelection=function(){var e=this.getSelection(),t=this.cm,r=t.doc.sel.primary(),n=r.from(),o=r.to();if(t.display.viewTo==t.display.viewFrom||n.line>=t.display.viewTo||o.line<t.display.viewFrom){e.removeAllRanges();return}var l=o2(t,e.anchorNode,e.anchorOffset),s=o2(t,e.focusNode,e.focusOffset);if(!l||l.bad||!s||s.bad||0!=tt(to(l,s),n)||0!=tt(ti(l,s),o)){var a=t.display.view,u=n.line>=t.display.viewFrom&&o6(t,n)||{node:a[0].measure.map[2],offset:0},c=o.line<t.display.viewTo&&o6(t,o);if(!c){var f=a[a.length-1].measure,d=f.maps?f.maps[f.maps.length-1]:f.map;c={node:d[d.length-1],offset:d[d.length-2]-d[d.length-3]}}if(!u||!c){e.removeAllRanges();return}var p,g=e.rangeCount&&e.getRangeAt(0);try{p=i(u.node,u.offset,c.offset,c.node)}catch(v){}p&&(!h&&t.state.focused?(e.collapse(u.node,u.offset),p.collapsed||(e.removeAllRanges(),e.addRange(p))):(e.removeAllRanges(),e.addRange(p)),g&&null==e.anchorNode?e.addRange(g):h&&this.startGracePeriod()),this.rememberSelection()}},o4.prototype.startGracePeriod=function(){var e=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout(function(){e.gracePeriod=!1,e.selectionChanged()&&e.cm.operation(function(){return e.cm.curOp.selectionChanged=!0})},20)},o4.prototype.showMultipleSelections=function(e){E(this.cm.display.cursorDiv,e.cursors),E(this.cm.display.selectionDiv,e.selection)},o4.prototype.rememberSelection=function(){var e=this.getSelection();this.lastAnchorNode=e.anchorNode,this.lastAnchorOffset=e.anchorOffset,this.lastFocusNode=e.focusNode,this.lastFocusOffset=e.focusOffset},o4.prototype.selectionInEditor=function(){var e=this.getSelection();if(!e.rangeCount)return!1;var t=e.getRangeAt(0).commonAncestorContainer;return I(this.div,t)},o4.prototype.focus=function(){"nocursor"!=this.cm.options.readOnly&&(this.selectionInEditor()&&B(this.div.ownerDocument)==this.div||this.showSelection(this.prepareSelection(),!0),this.div.focus())},o4.prototype.blur=function(){this.div.blur()},o4.prototype.getField=function(){return this.div},o4.prototype.supportsTouch=function(){return!0},o4.prototype.receivedFocus=function(){var e=this,t=this;function r(){t.cm.state.focused&&(t.pollSelection(),t.polling.set(t.cm.options.pollInterval,r))}this.selectionInEditor()?setTimeout(function(){return e.pollSelection()},20):nM(this.cm,function(){return t.cm.curOp.selectionChanged=!0}),this.polling.set(this.cm.options.pollInterval,r)},o4.prototype.selectionChanged=function(){var e=this.getSelection();return e.anchorNode!=this.lastAnchorNode||e.anchorOffset!=this.lastAnchorOffset||e.focusNode!=this.lastFocusNode||e.focusOffset!=this.lastFocusOffset},o4.prototype.pollSelection=function(){if(null==this.readDOMTimeout&&!this.gracePeriod&&this.selectionChanged()){var e=this.getSelection(),t=this.cm;if(k&&_&&this.cm.display.gutterSpecs.length&&function e(t){for(var r=t;r;r=r.parentNode)if(/CodeMirror-gutter-wrapper/.test(r.className))return!0;return!1}(e.anchorNode)){this.cm.triggerOnKeyDown({type:"keydown",keyCode:8,preventDefault:Math.abs}),this.blur(),this.focus();return}if(!this.composing){this.rememberSelection();var r=o2(t,e.anchorNode,e.anchorOffset),n=o2(t,e.focusNode,e.focusOffset);r&&n&&nM(t,function(){ib(t.doc,nj(r,n),ee),(r.bad||n.bad)&&(t.curOp.selectionChanged=!0)})}}},o4.prototype.pollContent=function(){null!=this.readDOMTimeout&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var e,t,r,n,i,o=this.cm,l=o.display,s=o.doc.sel.primary(),a=s.from(),u=s.to();if(0==a.ch&&a.line>o.firstLine()&&(a=te(a.line-1,ej(o.doc,a.line-1).length)),u.ch==ej(o.doc,u.line).text.length&&u.line<o.lastLine()&&(u=te(u.line+1,0)),a.line<l.viewFrom||u.line>l.viewTo-1)return!1;a.line==l.viewFrom||0==(e=rU(o,a.line))?(t=eq(l.view[0].line),r=l.view[0].node):(t=eq(l.view[e].line),r=l.view[e-1].node.nextSibling);var c=rU(o,u.line);if(c==l.view.length-1?(n=l.viewTo-1,i=l.lineDiv.lastChild):(n=eq(l.view[c+1].line)-1,i=l.view[c+1].node.previousSibling),!r)return!1;for(var f=o.doc.splitLines(function e(t,r,n,i,o){var l="",s=!1,a=t.doc.lineSeparator(),u=!1;function c(){s&&(l+=a,u&&(l+=a),s=u=!1)}function f(e){e&&(c(),l+=e)}function h(e){if(1==e.nodeType){var r=e.getAttribute("cm-text");if(r){f(r);return}var n,l=e.getAttribute("cm-marker");if(l){var d,p=t.findMarks(te(i,0),te(o+1,0),(d=+l,function(e){return e.id==d}));p.length&&(n=p[0].find(0))&&f(eY(t.doc,n.from,n.to).join(a));return}if("false"!=e.getAttribute("contenteditable")){var g=/^(pre|div|p|li|table|br)$/i.test(e.nodeName);if(/^br$/i.test(e.nodeName)||0!=e.textContent.length){g&&c();for(var v=0;v<e.childNodes.length;v++)h(e.childNodes[v]);/^(pre|p)$/i.test(e.nodeName)&&(u=!0),g&&(s=!0)}}}else 3==e.nodeType&&f(e.nodeValue.replace(/\u200b/g,"").replace(/\u00a0/g," "))}for(;h(r),r!=n;)r=r.nextSibling,u=!1;return l}(o,r,i,t,n)),h=eY(o.doc,te(t,0),te(n,ej(o.doc,n).text.length));f.length>1&&h.length>1;)if(el(f)==el(h))f.pop(),h.pop(),n--;else if(f[0]==h[0])f.shift(),h.shift(),t++;else break;for(var d=0,p=0,g=f[0],v=h[0],m=Math.min(g.length,v.length);d<m&&g.charCodeAt(d)==v.charCodeAt(d);)++d;for(var $=el(f),y=el(h),_=Math.min($.length-(1==f.length?d:0),y.length-(1==h.length?d:0));p<_&&$.charCodeAt($.length-p-1)==y.charCodeAt(y.length-p-1);)++p;if(1==f.length&&1==h.length&&t==a.line)for(;d&&d>a.ch&&$.charCodeAt($.length-p-1)==y.charCodeAt(y.length-p-1);)d--,p++;f[f.length-1]=$.slice(0,$.length-p).replace(/^\u200b+/,""),f[0]=f[0].slice(d).replace(/\u200b+$/,"");var b=te(t,d),x=te(n,h.length?el(h).length-p:0);if(f.length>1||f[0]||tt(b,x))return iH(o.doc,f,b,x,"+input"),!0},o4.prototype.ensurePolled=function(){this.forceCompositionEnd()},o4.prototype.reset=function(){this.forceCompositionEnd()},o4.prototype.forceCompositionEnd=function(){this.composing&&(clearTimeout(this.readDOMTimeout),this.composing=null,this.updateFromDOM(),this.div.blur(),this.div.focus())},o4.prototype.readFromDOMSoon=function(){var e=this;null==this.readDOMTimeout&&(this.readDOMTimeout=setTimeout(function(){if(e.readDOMTimeout=null,e.composing){if(!e.composing.done)return;e.composing=null}e.updateFromDOM()},80))},o4.prototype.updateFromDOM=function(){var e=this;(this.cm.isReadOnly()||!this.pollContent())&&nM(this.cm,function(){return rV(e.cm)})},o4.prototype.setUneditable=function(e){e.contentEditable="false"},o4.prototype.onKeyPress=function(e){0!=e.charCode&&!this.composing&&(e.preventDefault(),this.cm.isReadOnly()||nA(this.cm,o1)(this.cm,String.fromCharCode(null==e.charCode?e.keyCode:e.charCode),0))},o4.prototype.readOnlyChanged=function(e){this.div.contentEditable=String("nocursor"!=e)},o4.prototype.onContextMenu=function(){},o4.prototype.resetPosition=function(){},o4.prototype.needsContentAttribute=!0;var oU=function(e){this.cm=e,this.prevInput="",this.pollingFast=!1,this.polling=new Z,this.hasSelection=!1,this.composing=null,this.resetting=!1};oU.prototype.init=function(e){var t=this,r=this,n=this.cm;this.createField(e);var i=this.textarea;function o(e){if(!ek(n,e)){if(n.somethingSelected()){var t;oF=t={lineWise:!1,text:n.getSelections()}}else{if(!n.options.lineWiseCopyCut)return;var o,l=oR(n);oF=o={lineWise:!0,text:l.text},"cut"==e.type?n.setSelections(l.ranges,null,ee):(r.prevInput="",i.value=l.text.join("\n"),V(i))}"cut"==e.type&&(n.state.cutIncoming=+new Date)}}e.wrapper.insertBefore(this.wrapper,e.wrapper.firstChild),L&&(i.style.width="0px"),ew(i,"input",function(){v&&m>=9&&t.hasSelection&&(t.hasSelection=null),r.poll()}),ew(i,"paste",function(e){!(ek(n,e)||oE(e,n))&&(n.state.pasteIncoming=+new Date,r.fastPoll())}),ew(i,"cut",o),ew(i,"copy",o),ew(e.scroller,"paste",function(t){if(!(rf(e,t)||ek(n,t))){if(!i.dispatchEvent){n.state.pasteIncoming=+new Date,r.focus();return}var o=new Event("paste");o.clipboardData=t.clipboardData,i.dispatchEvent(o)}}),ew(e.lineSpace,"selectstart",function(t){rf(e,t)||eM(t)}),ew(i,"compositionstart",function(){var e=n.getCursor("from");r.composing&&r.composing.range.clear(),r.composing={start:e,range:n.markText(e,n.getCursor("to"),{className:"CodeMirror-composing"})}}),ew(i,"compositionend",function(){r.composing&&(r.poll(),r.composing.range.clear(),r.composing=null)})},oU.prototype.createField=function(e){this.wrapper=o3(),this.textarea=this.wrapper.firstChild;var t=this.cm.options;oI(this.textarea,t.spellcheck,t.autocorrect,t.autocapitalize)},oU.prototype.screenReaderLabelChanged=function(e){e?this.textarea.setAttribute("aria-label",e):this.textarea.removeAttribute("aria-label")},oU.prototype.prepareSelection=function(){var e=this.cm,t=e.display,r=e.doc,n=r9(e);if(e.options.moveInputWithCursor){var i=rW(e,r.sel.primary().head,"div"),o=t.wrapper.getBoundingClientRect(),l=t.lineDiv.getBoundingClientRect();n.teTop=Math.max(0,Math.min(t.wrapper.clientHeight-10,i.top+l.top-o.top)),n.teLeft=Math.max(0,Math.min(t.wrapper.clientWidth-10,i.left+l.left-o.left))}return n},oU.prototype.showSelection=function(e){var t=this.cm.display;E(t.cursorDiv,e.cursors),E(t.selectionDiv,e.selection),null!=e.teTop&&(this.wrapper.style.top=e.teTop+"px",this.wrapper.style.left=e.teLeft+"px")},oU.prototype.reset=function(e){if(!this.contextMenuPending&&(!this.composing||!e)){var t=this.cm;if(this.resetting=!0,t.somethingSelected()){this.prevInput="";var r=t.getSelection();this.textarea.value=r,t.state.focused&&V(this.textarea),v&&m>=9&&(this.hasSelection=r)}else!e&&(this.prevInput=this.textarea.value="",v&&m>=9&&(this.hasSelection=null));this.resetting=!1}},oU.prototype.getField=function(){return this.textarea},oU.prototype.supportsTouch=function(){return!1},oU.prototype.focus=function(){if("nocursor"!=this.cm.options.readOnly&&(!T||B(this.textarea.ownerDocument)!=this.textarea))try{this.textarea.focus()}catch(e){}},oU.prototype.blur=function(){this.textarea.blur()},oU.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},oU.prototype.receivedFocus=function(){this.slowPoll()},oU.prototype.slowPoll=function(){var e=this;!this.pollingFast&&this.polling.set(this.cm.options.pollInterval,function(){e.poll(),e.cm.state.focused&&e.slowPoll()})},oU.prototype.fastPoll=function(){var e=!1,t=this;function r(){t.poll()||e?(t.pollingFast=!1,t.slowPoll()):(e=!0,t.polling.set(60,r))}t.pollingFast=!0,t.polling.set(20,r)},oU.prototype.poll=function(){var e=this,t=this.cm,r=this.textarea,n=this.prevInput;if(this.contextMenuPending||this.resetting||!t.state.focused||eR(r)&&!n&&!this.composing||t.isReadOnly()||t.options.disableInput||t.state.keySeq)return!1;var i=r.value;if(i==n&&!t.somethingSelected())return!1;if(v&&m>=9&&this.hasSelection===i||N&&/[\uf700-\uf7ff]/.test(i))return t.display.input.reset(),!1;if(t.doc.sel==t.display.selForContextMenu){var o=i.charCodeAt(0);if(8203!=o||n||(n="​"),8666==o)return this.reset(),this.cm.execCommand("undo")}for(var l=0,s=Math.min(n.length,i.length);l<s&&n.charCodeAt(l)==i.charCodeAt(l);)++l;return nM(t,function(){o1(t,i.slice(l),n.length-l,null,e.composing?"*compose":null),i.length>1e3||i.indexOf("\n")>-1?r.value=e.prevInput="":e.prevInput=i,e.composing&&(e.composing.range.clear(),e.composing.range=t.markText(e.composing.start,t.getCursor("to"),{className:"CodeMirror-composing"}))}),!0},oU.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},oU.prototype.onKeyPress=function(){v&&m>=9&&(this.hasSelection=null),this.fastPoll()},oU.prototype.onContextMenu=function(e){var t,r=this,n=r.cm,i=n.display,o=r.textarea;r.contextMenuPending&&r.contextMenuPending();var l=rG(n,e),s=i.scroller.scrollTop;if(l&&!x){n.options.resetSelectionOnContextMenu&&-1==n.doc.sel.contains(l)&&nA(n,ib)(n.doc,nj(l),ee);var a=o.style.cssText,u=r.wrapper.style.cssText,c=r.wrapper.offsetParent.getBoundingClientRect();if(r.wrapper.style.cssText="position: static",o.style.cssText="position: absolute; width: 30px; height: 30px;\n      top: "+(e.clientY-c.top-5)+"px; left: "+(e.clientX-c.left-5)+"px;\n      z-index: 1000; background: "+(v?"rgba(255, 255, 255, .05)":"transparent")+";\n      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);",$&&(t=o.ownerDocument.defaultView.scrollY),i.input.focus(),$&&o.ownerDocument.defaultView.scrollTo(null,t),i.input.reset(),n.somethingSelected()||(o.value=r.prevInput=" "),r.contextMenuPending=d,i.selForContextMenu=n.doc.sel,clearTimeout(i.detectingSelectAll),v&&m>=9&&h(),W){eD(e);var f=function(){eS(window,"mouseup",f),setTimeout(d,20)};ew(window,"mouseup",f)}else setTimeout(d,50)}function h(){if(null!=o.selectionStart){var e=n.somethingSelected(),t="​"+(e?o.value:"");o.value="⇚",o.value=t,r.prevInput=e?"":"​",o.selectionStart=1,o.selectionEnd=t.length,i.selForContextMenu=n.doc.sel}}function d(){if(r.contextMenuPending==d&&(r.contextMenuPending=!1,r.wrapper.style.cssText=u,o.style.cssText=a,v&&m<9&&i.scrollbars.setScrollTop(i.scroller.scrollTop=s),null!=o.selectionStart)){(!v||v&&m<9)&&h();var e=0,t=function(){i.selForContextMenu==n.doc.sel&&0==o.selectionStart&&o.selectionEnd>0&&"​"==r.prevInput?nA(n,iN)(n):e++<10?i.detectingSelectAll=setTimeout(t,500):(i.selForContextMenu=null,i.input.reset())};i.detectingSelectAll=setTimeout(t,200)}}},oU.prototype.readOnlyChanged=function(e){e||this.reset(),this.textarea.disabled="nocursor"==e,this.textarea.readOnly=!!e},oU.prototype.setUneditable=function(){},oU.prototype.needsContentAttribute=!1,!function e(t){var r=t.optionHandlers;function n(e,n,i,o){t.defaults[e]=n,i&&(r[e]=o?function(e,t,r){r!=oN&&i(e,t,r)}:i)}t.defineOption=n,t.Init=oN,n("value","",function(e,t){return e.setValue(t)},!0),n("mode",null,function(e,t){e.doc.modeOption=t,nZ(e)},!0),n("indentUnit",2,nZ,!0),n("indentWithTabs",!1),n("smartIndent",!0),n("tabSize",4,function(e){nQ(e),rT(e),rV(e)},!0),n("lineSeparator",null,function(e,t){if(e.doc.lineSep=t,t){var r=[],n=e.doc.first;e.doc.iter(function(e){for(var i=0;;){var o=e.text.indexOf(t,i);if(-1==o)break;i=o+t.length,r.push(te(n,o))}n++});for(var i=r.length-1;i>=0;i--)iH(e.doc,t,r[i],te(r[i].line,r[i].ch+t.length))}}),n("specialChars",/[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b\u200e\u200f\u2028\u2029\u202d\u202e\u2066\u2067\u2069\ufeff\ufff9-\ufffc]/g,function(e,t,r){e.state.specialChars=RegExp(t.source+(t.test("	")?"":"|	"),"g"),r!=oN&&e.refresh()}),n("specialCharPlaceholder",tU,function(e){return e.refresh()},!0),n("electricChars",!0),n("inputStyle",T?"contenteditable":"textarea",function(){throw Error("inputStyle can not (yet) be changed in a running editor")},!0),n("spellcheck",!1,function(e,t){return e.getInputField().spellcheck=t},!0),n("autocorrect",!1,function(e,t){return e.getInputField().autocorrect=t},!0),n("autocapitalize",!1,function(e,t){return e.getInputField().autocapitalize=t},!0),n("rtlMoveVisually",!M),n("wholeLineUpdateBefore",!0),n("theme","default",function(e){oT(e),n4(e)},!0),n("keyMap","default",function(e,t,r){var n=oo(t),i=r!=oN&&oo(r);i&&i.detach&&i.detach(e,n),n.attach&&n.attach(e,i||null)}),n("extraKeys",null),n("configureMouse",null),n("lineWrapping",!1,o0,!0),n("gutters",[],function(e,t){e.display.gutterSpecs=nB(t,e.options.lineNumbers),n4(e)},!0),n("fixedGutter",!0,function(e,t){e.display.gutters.style.left=t?r6(e.display)+"px":"0",e.refresh()},!0),n("coverGutterNextToScrollbar",!1,function(e){return ny(e)},!0),n("scrollbarStyle","native",function(e){nx(e),ny(e),e.display.scrollbars.setScrollTop(e.doc.scrollTop),e.display.scrollbars.setScrollLeft(e.doc.scrollLeft)},!0),n("lineNumbers",!1,function(e,t){e.display.gutterSpecs=nB(e.options.gutters,t),n4(e)},!0),n("firstLineNumber",1,n4,!0),n("lineNumberFormatter",function(e){return e},n4,!0),n("showCursorWhenSelecting",!1,r8,!0),n("resetSelectionOnContextMenu",!0),n("lineWiseCopyCut",!0),n("pasteLinesPerSelection",!0),n("selectionsMayTouch",!1),n("readOnly",!1,function(e,t){"nocursor"==t&&(nn(e),e.display.input.blur()),e.display.input.readOnlyChanged(t)}),n("screenReaderLabel",null,function(e,t){t=""===t?null:t,e.display.input.screenReaderLabelChanged(t)}),n("disableInput",!1,function(e,t){t||e.display.input.reset()},!0),n("dragDrop",!0,oA),n("allowDropFileTypes",null),n("cursorBlinkRate",530),n("cursorScrollMargin",0),n("cursorHeight",1,r8,!0),n("singleCursorHeightPerLine",!0,r8,!0),n("workTime",100),n("workDelay",100),n("flattenSpans",!0,nQ,!0),n("addModeClass",!1,nQ,!0),n("pollInterval",100),n("undoDepth",200,function(e,t){return e.doc.history.undoDepth=t}),n("historyEventDelay",1250),n("viewportMargin",10,function(e){return e.refresh()},!0),n("maxHighlightLength",1e4,nQ,!0),n("moveInputWithCursor",!0,function(e,t){t||e.display.input.resetPosition()}),n("tabindex",null,function(e,t){return e.display.input.getField().tabIndex=t||""}),n("autofocus",null),n("direction","ltr",function(e,t){return e.doc.setDirection(t)},!0),n("phrases",null)}(oD),t=(e=oD).optionHandlers,r=e.helpers={},e.prototype={constructor:e,focus:function(){X(this).focus(),this.display.input.focus()},setOption:function(e,r){var n=this.options,i=n[e];(n[e]!=r||"mode"==e)&&(n[e]=r,t.hasOwnProperty(e)&&nA(this,t[e])(this,r,i),eL(this,"optionChange",this,e))},getOption:function(e){return this.options[e]},getDoc:function(){return this.doc},addKeyMap:function(e,t){this.state.keyMaps[t?"push":"unshift"](oo(e))},removeKeyMap:function(e){for(var t=this.state.keyMaps,r=0;r<t.length;++r)if(t[r]==e||t[r].name==e)return t.splice(r,1),!0},addOverlay:n0(function(t,r){var n=t.token?t:e.getMode(this.options,t);if(n.startState)throw Error("Overlays may not be stateful.");(function e(t,r,n){for(var i=0,o=n(r);i<t.length&&n(t[i])<=o;)i++;t.splice(i,0,r)})(this.state.overlays,{mode:n,modeSpec:t,opaque:r&&r.opaque,priority:r&&r.priority||0},function(e){return e.priority}),this.state.modeGen++,rV(this)}),removeOverlay:n0(function(e){for(var t=this.state.overlays,r=0;r<t.length;++r){var n=t[r].modeSpec;if(n==e||"string"==typeof e&&n.name==e){t.splice(r,1),this.state.modeGen++,rV(this);return}}}),indentLine:n0(function(e,t,r){"string"!=typeof t&&"number"!=typeof t&&(t=null==t?this.options.smartIndent?"smart":"prev":t?"add":"subtract"),eQ(this.doc,e)&&oH(this,e,t,r)}),indentSelection:n0(function(e){for(var t=this.doc.sel.ranges,r=-1,n=0;n<t.length;n++){var i=t[n];if(i.empty())i.head.line>r&&(oH(this,i.head.line,e,!0),r=i.head.line,n==this.doc.sel.primIndex&&nu(this));else{var o=i.from(),l=i.to(),s=Math.max(r,o.line);r=Math.min(this.lastLine(),l.line-(l.ch?0:1))+1;for(var a=s;a<r;++a)oH(this,a,e);var u=this.doc.sel.ranges;0==o.ch&&t.length==u.length&&u[n].from().ch>0&&i$(this.doc,n,new nK(o,u[n].to()),ee)}}}),getTokenAt:function(e,t){return t$(this,e,t)},getLineTokens:function(e,t){return t$(this,te(e),t,!0)},getTokenTypeAt:function(e){e=ts(this.doc,e);var t,r=th(this,ej(this.doc,e.line)),n=0,i=(r.length-1)/2,o=e.ch;if(0==o)t=r[2];else for(;;){var l=n+i>>1;if((l?r[2*l-1]:0)>=o)i=l;else if(r[2*l+1]<o)n=l+1;else{t=r[2*l+2];break}}var s=t?t.indexOf("overlay "):-1;return s<0?t:0==s?null:t.slice(0,s-1)},getModeAt:function(t){var r=this.doc.mode;return r.innerMode?e.innerMode(r,this.getTokenAt(t).state).mode:r},getHelper:function(e,t){return this.getHelpers(e,t)[0]},getHelpers:function(e,t){var n=[];if(!r.hasOwnProperty(t))return n;var i=r[t],o=this.getModeAt(e);if("string"==typeof o[t])i[o[t]]&&n.push(i[o[t]]);else if(o[t])for(var l=0;l<o[t].length;l++){var s=i[o[t][l]];s&&n.push(s)}else o.helperType&&i[o.helperType]?n.push(i[o.helperType]):i[o.name]&&n.push(i[o.name]);for(var a=0;a<i._global.length;a++){var u=i._global[a];u.pred(o,this)&&-1==Q(n,u.val)&&n.push(u.val)}return n},getStateAfter:function(e,t){var r=this.doc;return td(this,(e=tl(r,null==e?r.first+r.size-1:e))+1,t).state},cursorCoords:function(e,t){var r,n=this.doc.sel.primary();return r=null==e?n.head:"object"==typeof e?ts(this.doc,e):e?n.from():n.to(),rW(this,r,t||"page")},charCoords:function(e,t){return rD(this,ts(this.doc,e),t||"page")},coordsChar:function(e,t){return e=r0(this,e,t||"page"),rP(this,e.left,e.top)},lineAtHeight:function(e,t){return e=r0(this,{top:e,left:0},t||"page").top,eZ(this.doc,e+this.display.viewOffset)},heightAtLine:function(e,t,r){var n,i=!1;if("number"==typeof e){var o=this.doc.first+this.doc.size-1;e<this.doc.first?e=this.doc.first:e>o&&(e=o,i=!0),n=ej(this.doc,e)}else n=e;return rA(this,n,{top:0,left:0},t||"page",r||i).top+(i?this.doc.height-tI(n):0)},defaultTextHeight:function(){return rB(this.display)},defaultCharWidth:function(){return r7(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(e,t,r,n,i){var o,l,s,a=this.display,u=(e=rW(this,ts(this.doc,e))).bottom,c=e.left;if(t.style.position="absolute",t.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(t),a.sizer.appendChild(t),"over"==n)u=e.top;else if("above"==n||"near"==n){var f=Math.max(a.wrapper.clientHeight,this.doc.height),h=Math.max(a.sizer.clientWidth,a.lineSpace.clientWidth);("above"==n||e.bottom+t.offsetHeight>f)&&e.top>t.offsetHeight?u=e.top-t.offsetHeight:e.bottom+t.offsetHeight<=f&&(u=e.bottom),c+t.offsetWidth>h&&(c=h-t.offsetWidth)}t.style.top=u+"px",t.style.left=t.style.right="","right"==i?(c=a.sizer.clientWidth-t.offsetWidth,t.style.right="0px"):("left"==i?c=0:"middle"==i&&(c=(a.sizer.clientWidth-t.offsetWidth)/2),t.style.left=c+"px"),r&&(o=this,l={left:c,top:u,right:c+t.offsetWidth,bottom:u+t.offsetHeight},null!=(s=ns(o,l)).scrollTop&&nd(o,s.scrollTop),null!=s.scrollLeft&&ng(o,s.scrollLeft))},triggerOnKeyDown:n0(oy),triggerOnKeyPress:n0(ob),triggerOnKeyUp:o_,triggerOnMouseDown:n0(ow),execCommand:function(e){if(oc.hasOwnProperty(e))return oc[e].call(null,this)},triggerElectric:n0(function(e){oz(this,e)}),findPosH:function(e,t,r,n){var i=1;t<0&&(i=-1,t=-t);for(var o=ts(this.doc,e),l=0;l<t&&!(o=oB(this.doc,o,i,r,n)).hitSide;++l);return o},moveH:n0(function(e,t){var r=this;this.extendSelectionsBy(function(n){return r.display.shift||r.doc.extend||n.empty()?oB(r.doc,n.head,e,t,r.options.rtlMoveVisually):e<0?n.from():n.to()},er)}),deleteH:n0(function(e,t){var r=this.doc.sel,n=this.doc;r.somethingSelected()?n.replaceSelection("",null,"+delete"):ol(this,function(r){var i=oB(n,r.head,e,t,!1);return e<0?{from:i,to:r.head}:{from:r.head,to:i}})}),findPosV:function(e,t,r,n){var i=1,o=n;t<0&&(i=-1,t=-t);for(var l=ts(this.doc,e),s=0;s<t;++s){var a=rW(this,l,"div");if(null==o?o=a.left:a.left=o,(l=o7(this,a,i,r)).hitSide)break}return l},moveV:n0(function(e,t){var r=this,n=this.doc,i=[],o=!this.display.shift&&!n.extend&&n.sel.somethingSelected();if(n.extendSelectionsBy(function(l){if(o)return e<0?l.from():l.to();var s=rW(r,l.head,"div");null!=l.goalColumn&&(s.left=l.goalColumn),i.push(s.left);var a=o7(r,s,e,t);return"page"==t&&l==n.sel.primary()&&na(r,rD(r,a,"div").top-s.top),a},er),i.length)for(var l=0;l<n.sel.ranges.length;l++)n.sel.ranges[l].goalColumn=i[l]}),findWordAt:function(e){var t=ej(this.doc,e.line).text,r=e.ch,n=e.ch;if(t){var i=this.getHelper(e,"wordChars");("before"==e.sticky||n==t.length)&&r?--r:++n;for(var o=t.charAt(r),l=eh(o,i)?function(e){return eh(e,i)}:/\s/.test(o)?function(e){return/\s/.test(e)}:function(e){return!/\s/.test(e)&&!eh(e)};r>0&&l(t.charAt(r-1));)--r;for(;n<t.length&&l(t.charAt(n));)++n}return new nK(te(e.line,r),te(e.line,n))},toggleOverwrite:function(e){(null==e||e!=this.state.overwrite)&&((this.state.overwrite=!this.state.overwrite)?G(this.display.cursorDiv,"CodeMirror-overwrite"):F(this.display.cursorDiv,"CodeMirror-overwrite"),eL(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==B(K(this))},isReadOnly:function(){return!!(this.options.readOnly||this.doc.cantEdit)},scrollTo:n0(function(e,t){nc(this,e,t)}),getScrollInfo:function(){var e=this.display.scroller;return{left:e.scrollLeft,top:e.scrollTop,height:e.scrollHeight-rg(this)-this.display.barHeight,width:e.scrollWidth-rg(this)-this.display.barWidth,clientHeight:rm(this),clientWidth:rv(this)}},scrollIntoView:n0(function(e,t){if(null==e?(e={from:this.doc.sel.primary().head,to:null},null==t&&(t=this.options.cursorScrollMargin)):"number"==typeof e?e={from:te(e,0),to:null}:null==e.from&&(e={from:e,to:null}),e.to||(e.to=e.from),e.margin=t||0,null!=e.from.line){var r,n;r=this,n=e,nf(r),r.curOp.scrollToPos=n}else nh(this,e.from,e.to,e.margin)}),setSize:n0(function(e,t){var r=this,n=function(e){return"number"==typeof e||/^\d+$/.test(String(e))?e+"px":e};null!=e&&(this.display.wrapper.style.width=n(e)),null!=t&&(this.display.wrapper.style.height=n(t)),this.options.lineWrapping&&rk(this);var i=this.display.viewFrom;this.doc.iter(i,this.display.viewTo,function(e){if(e.widgets){for(var t=0;t<e.widgets.length;t++)if(e.widgets[t].noHScroll){rK(r,i,"widget");break}}++i}),this.curOp.forceUpdate=!0,eL(this,"refresh",this)}),operation:function(e){return nM(this,e)},startOperation:function(){return nC(this)},endOperation:function(){return nS(this)},refresh:n0(function(){var e=this.display.cachedTextHeight;rV(this),this.curOp.forceUpdate=!0,rT(this),nc(this,this.doc.scrollLeft,this.doc.scrollTop),nz(this.display),(null==e||Math.abs(e-rB(this.display))>.5||this.options.lineWrapping)&&r2(this),eL(this,"refresh",this)}),swapDoc:n0(function(e){var t=this.doc;return t.cm=null,this.state.selectingText&&this.state.selectingText(),ir(this,e),rT(this),this.display.input.reset(),nc(this,e.scrollLeft,e.scrollTop),this.curOp.forceScroll=!0,tZ(this,"swapDoc",this,t),t}),phrase:function(e){var t=this.options.phrases;return t&&Object.prototype.hasOwnProperty.call(t,e)?t[e]:e},getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},eO(e),e.registerHelper=function(t,n,i){r.hasOwnProperty(t)||(r[t]=e[t]={_global:[]}),r[t][n]=i},e.registerGlobalHelper=function(t,n,i,o){e.registerHelper(t,n,o),r[t]._global.push({pred:i,val:o})};var oV="iter insert remove copy getEditor constructor".split(" ");for(var oK in iU.prototype)iU.prototype.hasOwnProperty(oK)&&0>Q(oV,oK)&&(oD.prototype[oK]=function(e){return function(){return e.apply(this.doc,arguments)}}(iU.prototype[oK]));return eO(iU),oD.inputStyles={textarea:oU,contenteditable:o4},oD.defineMode=function(e){oD.defaults.mode||"null"==e||(oD.defaults.mode=e),e4.apply(this,arguments)},oD.defineMIME=e6,oD.defineMode("null",function(){return{token:function(e){return e.skipToEnd()}}}),oD.defineMIME("text/plain","null"),oD.defineExtension=function(e,t){oD.prototype[e]=t},oD.defineDocExtension=function(e,t){iU.prototype[e]=t},oD.fromTextArea=function e(t,r){if((r=r?Y(r):{}).value=t.value,!r.tabindex&&t.tabIndex&&(r.tabindex=t.tabIndex),!r.placeholder&&t.placeholder&&(r.placeholder=t.placeholder),null==r.autofocus){var n,i=B(t.ownerDocument);r.autofocus=i==t||null!=t.getAttribute("autofocus")&&i==document.body}function o(){t.value=u.getValue()}if(t.form&&(ew(t.form,"submit",o),!r.leaveSubmitMethodAlone)){var l=t.form;n=l.submit;try{var s=l.submit=function(){o(),l.submit=n,l.submit(),l.submit=s}}catch(a){}}r.finishInit=function(e){e.save=o,e.getTextArea=function(){return t},e.toTextArea=function(){e.toTextArea=isNaN,o(),t.parentNode.removeChild(e.getWrapperElement()),t.style.display="",t.form&&(eS(t.form,"submit",o),r.leaveSubmitMethodAlone||"function"!=typeof t.form.submit||(t.form.submit=n))}},t.style.display="none";var u=oD(function(e){return t.parentNode.insertBefore(e,t.nextSibling)},r);return u},(n=oD).off=eS,n.on=ew,n.wheelEventPixels=function e(t){var r=nG(t);return r.x*=n2,r.y*=n2,r},n.Doc=iU,n.splitLines=ez,n.countColumn=q,n.findColumn=en,n.isWordChar=ef,n.Pass=J,n.signal=eL,n.Line=t7,n.changeEnd=nY,n.scrollbarModel=nb,n.Pos=te,n.cmpPos=tt,n.modes=eB,n.mimeModes=e7,n.resolveMode=e5,n.getMode=e2,n.modeExtensions=eG,n.extendMode=function e(t,r){Y(r,eG.hasOwnProperty(t)?eG[t]:eG[t]={})},n.copyState=eU,n.startState=eK,n.innerMode=eV,n.commands=oc,n.keyMap=iJ,n.keyName=oi,n.isModifierKey=or,n.lookupKey=ot,n.normalizeKeyMap=function e(t){var r={};for(var n in t)if(t.hasOwnProperty(n)){var i=t[n];if(/^(name|fallthrough|(de|at)tach)$/.test(n))continue;if("..."==i){delete t[n];continue}for(var o=es(n.split(" "),oe),l=0;l<o.length;l++){var s=void 0,a=void 0;l==o.length-1?(a=o.join(" "),s=i):(a=o.slice(0,l+1).join(" "),s="...");var u=r[a];if(u){if(u!=s)throw Error("Inconsistent bindings for "+a)}else r[a]=s}delete t[n]}for(var c in r)t[c]=r[c];return t},n.StringStream=eX,n.SharedTextMarker=i6,n.TextMarker=i7,n.LineWidget=iI,n.e_preventDefault=eM,n.e_stopPropagation=eA,n.e_stop=eD,n.addClass=G,n.contains=I,n.rmClass=F,n.keyNames=i9,oD.version="5.65.12",oD});