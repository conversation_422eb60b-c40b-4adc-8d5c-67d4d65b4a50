window.bp=window.bp||{},(l=>{"undefined"!=typeof BP_Uploader&&(bp.Models=bp.Models||{},bp.Collections=bp.Collections||{},bp.Views=bp.Views||{},bp.Avatar={start:function(){var t=this;this.removeLegacyUI(),this.views=new Backbone.Collection,this.jcropapi={},this.warning=null,this.setupNav(),this.avatars=bp.Uploader.filesUploaded,this.Attachment=new Backbone.Model,bp.Uploader.filesQueue.on("reset",this.cropView,this),l("body.wp-admin").on("tb_unload","#TB_window",function(){t.resetViews()}),l("body.wp-admin").on("click",".bp-xprofile-avatar-user-edit",function(){t.resetViews()}),l(document).on("click",".avatar-crop-cancel",function(e){e.preventDefault(),t.resetViews()})},removeLegacyUI:function(){l("#avatar-upload-form").length?(l("#avatar-upload").remove(),l("#avatar-upload-form p").remove()):l("#group-settings-form").length?(l("#group-settings-form p").each(function(e){0!==e&&l(this).remove()}),l("#delete-group-avatar-button").length&&l("#delete-group-avatar-button").remove()):l("#group-create-body").length?(l(".main-column p #file").remove(),l(".main-column p #upload").remove()):l("#bp_xprofile_user_admin_avatar a.bp-xprofile-avatar-user-admin").length&&l("#bp_xprofile_user_admin_avatar a.bp-xprofile-avatar-user-admin").remove(),l(".bb-custom-profile-group-avatar-feedback p").length&&(l(".bb-custom-profile-group-avatar-feedback").hide(),l(".bb-custom-profile-group-avatar-feedback p").removeClass("success error").html(""))},setView:function(e){switch(_.isUndefined(this.views.models)||_.each(this.views.models,function(e){e.get("view").remove()},this),this.views.reset(),_.isUndefined(this.avatars)||this.avatars.reset(),_.isEmpty(this.jcropapi)||(this.jcropapi.destroy(),this.jcropapi={}),e){case"upload":this.uploaderView();break;case"delete":this.deleteView()}},resetViews:function(){this.nav.trigger("bp-avatar-view:changed","upload"),_.each(this.navItems.models,function(e){"upload"===e.id?e.set({active:1}):e.set({active:0})}),l(".bb-custom-profile-group-avatar-feedback p").length&&(this.removeWarning(),l(".bb-custom-profile-group-avatar-feedback").hide().find(".bp-feedback").removeClass("success error").find("p").html(""))},setupNav:function(){var a,i,s=this;this.navItems=new Backbone.Collection,_.each(BP_Uploader.settings.nav,function(e,t){_.isObject(e)&&((i=0)===t&&(a=e.id,i=1),s.navItems.add({id:e.id,name:e.caption,href:"#",active:i,hide:_.isUndefined(e.hide)?0:e.hide}))}),this.nav=new bp.Views.Nav({collection:this.navItems}),this.nav.inject(".bp-avatar-nav"),this.setView(a),this.nav.on("bp-avatar-view:changed",_.bind(this.setView,this))},uploaderView:function(){bp.Uploader.filesQueue.on("add",this.uploadProgress,this);var e=new bp.Views.Uploader;this.views.add({id:"upload",view:e}),e.inject(".bp-avatar")},uploadProgress:function(){var e=new bp.Views.uploaderStatus({collection:bp.Uploader.filesQueue});_.isUndefined(this.views.get("status"))?this.views.add({id:"status",view:e}):this.views.set({id:"status",view:e}),e.inject(".bp-avatar-status")},cropView:function(){var e;_.isEmpty(this.avatars.models)||(_.isUndefined(this.views.get("status"))||((e=this.views.get("status")).get("view").remove(),this.views.remove({id:"status",view:e})),e=new bp.Views.Avatars({collection:this.avatars}),this.views.add({id:"crop",view:e}),e.inject(".bp-avatar"),l(".bb-custom-profile-group-avatar-feedback p").length&&this.removeWarning())},setAvatar:function(a){var e,i=this;_.isUndefined(this.views.get("crop"))||(_.isEmpty(this.jcropapi)||(this.jcropapi.destroy(),this.jcropapi={}),(e=this.views.get("crop")).get("view").remove(),this.views.remove({id:"crop",view:e})),l(".bb-custom-profile-group-avatar-feedback p").length&&(l(".buddyboss_page_bp-settings #TB_window #TB_closeWindowButton").trigger("click"),l(".bp-xprofile-avatar-user-edit").html(l(".bp-xprofile-avatar-user-edit").data("uploading"))),bp.ajax.post("bp_avatar_set",{json:!0,original_file:a.get("url"),crop_w:a.get("w"),crop_h:a.get("h"),crop_x:a.get("x"),crop_y:a.get("y"),item_id:a.get("item_id"),item_type:a.get("item_type"),object:a.get("object"),type:_.isUndefined(a.get("type"))?"crop":a.get("type"),nonce:a.get("nonces").set}).done(function(e){l(".bb-custom-profile-group-avatar-feedback p").length&&l(".bp-xprofile-avatar-user-edit").html(l(".bp-xprofile-avatar-user-edit").data("upload"));var t=new bp.Views.AvatarStatus({value:BP_Uploader.strings.feedback_messages[e.feedback_code],type:"success"});i.views.add({id:"status",view:t}),t.inject(".bp-avatar-status"),l("."+a.get("object")+"-"+e.item_id+"-avatar").each(function(){l(this).prop("src",e.avatar)}),l(".header-aside-inner .user-link .avatar").length&&!l("body").hasClass("group-avatar")&&(l(".header-aside-inner .user-link .avatar").prop("src",e.avatar),l(".header-aside-inner .user-link .avatar").prop("srcset",e.avatar)),bp.Avatar.navItems.get("delete").set({hide:0}),i.Attachment.set(_.extend(_.pick(a.attributes,["object","item_id"]),{url:e.avatar,action:"uploaded"})),l(".custom-profile-group-avatar a.bb-img-remove-button").length&&l(".custom-profile-group-avatar a.bb-img-remove-button").removeClass("bp-hide"),l(".custom-profile-group-avatar ."+a.get("object")+"-"+e.item_id+"-avatar").removeClass("bp-hide"),l(".custom-profile-group-avatar .bb-upload-container .bb-default-custom-avatar-field").val(e.avatar),l(".custom-profile-group-avatar .bb-upload-container img").prop("src",e.avatar).removeClass("bp-hide"),l(".preview_avatar_cover .preview-item-avatar img").prop("src",e.avatar)}).fail(function(e){l(".bb-custom-profile-group-avatar-feedback p").length&&l(".bp-xprofile-avatar-user-edit").html(l(".bp-xprofile-avatar-user-edit").data("upload"));var t=BP_Uploader.strings.default_error,e=(_.isUndefined(e)||(t=BP_Uploader.strings.feedback_messages[e.feedback_code]),l(".bb-custom-profile-group-avatar-feedback p").length&&(l(".bb-custom-profile-group-avatar-feedback p").removeClass("success error").addClass("error").html(t),l(".bb-custom-profile-group-avatar-feedback").show()),new bp.Views.AvatarStatus({value:t,type:"error"}));i.views.add({id:"status",view:e}),e.inject(".bp-avatar-status")})},deleteView:function(){var e=new Backbone.Model(_.pick(BP_Uploader.settings.defaults.multipart_params.bp_params,"object","item_id","nonces")),e=new bp.Views.DeleteAvatar({model:e});this.views.add({id:"delete",view:e}),e.inject(".bp-avatar")},deleteAvatar:function(a){var e,i=this;_.isUndefined(this.views.get("delete"))||((e=this.views.get("delete")).get("view").remove(),this.views.remove({id:"delete",view:e})),bp.ajax.post("bp_avatar_delete",{json:!0,item_id:a.get("item_id"),object:a.get("object"),nonce:a.get("nonces").remove}).done(function(e){var t=new bp.Views.AvatarStatus({value:BP_Uploader.strings.feedback_messages[e.feedback_code],type:"success"});i.views.add({id:"status",view:t}),t.inject(".bp-avatar-status"),l("."+a.get("object")+"-"+e.item_id+"-avatar").each(function(){l(this).prop("src",e.avatar)}),bp.Avatar.navItems.get("delete").set({active:0,hide:1}),i.Attachment.set(_.extend(_.pick(a.attributes,["object","item_id"]),{url:e.avatar,action:"deleted"})),l(".header-aside-inner .user-link .avatar").length&&!l("body").hasClass("group-avatar")&&(l(".header-aside-inner .user-link .avatar").prop("src",e.avatar),l(".header-aside-inner .user-link .avatar").prop("srcset",e.avatar))}).fail(function(e){var t=BP_Uploader.strings.default_error,e=(_.isUndefined(e)||(t=BP_Uploader.strings.feedback_messages[e.feedback_code]),new bp.Views.AvatarStatus({value:t,type:"error"}));i.views.add({id:"status",view:e}),e.inject(".bp-avatar-status")})},removeWarning:function(){_.isNull(this.warning)||this.warning.remove()},displayWarning:function(e){this.removeWarning(),this.warning=new bp.Views.uploaderWarning({value:e}),this.warning.inject(".bp-avatar-status")}},bp.Views.Nav=bp.View.extend({tagName:"ul",className:"avatar-nav-items",events:{"click .bp-avatar-nav-item":"toggleView"},initialize:function(){1!==_.findWhere(this.collection.models,{id:"delete"}).get("hide")&&bp.Avatar.displayWarning(BP_Uploader.strings.avatar_size_warning+"<br/>"+BP_Uploader.strings.has_avatar_warning),_.each(this.collection.models,this.addNavItem,this),this.collection.on("change:hide",this.showHideNavItem,this)},addNavItem:function(e){1!==e.get("hide")&&this.views.add(new bp.Views.NavItem({model:e}))},showHideNavItem:function(t){var a=null;_.each(this.views._views[""],function(e){1===e.model.get("hide")&&e.remove(),t.get("id")===e.model.get("id")&&(a=!0)}),_.isBoolean(a)||this.addNavItem(t)},toggleView:function(e){e.preventDefault(),bp.Avatar.removeWarning();var t=l(e.target).data("nav");_.each(this.collection.models,function(e){e.id===t?(e.set({active:1}),this.trigger("bp-avatar-view:changed",e.id)):e.set({active:0})},this)}}),bp.Views.NavItem=bp.View.extend({tagName:"li",className:"avatar-nav-item",template:bp.template("bp-avatar-nav"),initialize:function(){1===this.model.get("active")&&(this.el.className+=" current"),this.el.id+="bp-avatar-"+this.model.get("id"),this.model.on("change:active",this.setCurrentNav,this)},setCurrentNav:function(e){1===e.get("active")?this.$el.addClass("current"):this.$el.removeClass("current")}}),bp.Views.Avatars=bp.View.extend({className:"items",initialize:function(){_.each(this.collection.models,this.addItemView,this)},addItemView:function(e){var t={full_h:150,full_w:150,item_type:""};_.isUndefined(BP_Uploader.settings.crop.full_h)||_.isUndefined(BP_Uploader.settings.crop.full_w)||(t.full_h=BP_Uploader.settings.crop.full_h,t.full_w=BP_Uploader.settings.crop.full_w),_.isUndefined(BP_Uploader.settings.defaults.multipart_params.bp_params.item_type)||(t.item_type=BP_Uploader.settings.defaults.multipart_params.bp_params.item_type),e.set(_.extend(_.pick(BP_Uploader.settings.defaults.multipart_params.bp_params,"object","item_id","nonces"),t)),this.views.add(new bp.Views.Avatar({model:e}))}}),bp.Views.Avatar=bp.View.extend({className:"item",template:bp.template("bp-avatar-item"),events:{"click .avatar-crop-submit":"cropAvatar"},initialize:function(){_.defaults(this.options,{full_h:BP_Uploader.settings.crop.full_h,full_w:BP_Uploader.settings.crop.full_w,aspectRatio:1}),!1!==this.model.get("feedback")&&bp.Avatar.displayWarning(this.model.get("feedback")),this.on("ready",this.initCropper)},initCropper:function(){var e,t,a,i,s,r,o=this,n=this.$el.find("#avatar-to-crop img"),d=this.$el.width(),p={};_.isUndefined(this.options.full_h)||_.isUndefined(this.options.full_w)||(this.options.aspectRatio=this.options.full_w/this.options.full_h),p.w=this.model.get("width"),p.h=this.model.get("height"),this.options.full_w+p.w+20<d&&(l("#avatar-to-crop").addClass("adjust"),this.$el.find(".avatar-crop-management").addClass("adjust")),p.h<=p.w?(e=Math.round(p.h/4),t=(s=r=Math.round(p.h/2))+e,i=r+(a=(p.w-r)/2)):(a=Math.round(p.w/4),s=r=Math.round(p.w/2),i=r+a,t=s+(e=(p.h-s)/2)),n.Jcrop({onChange:_.bind(o.showPreview,o),onSelect:_.bind(o.showPreview,o),aspectRatio:o.options.aspectRatio,setSelect:[a,e,i,t]},function(){bp.Avatar.jcropapi=this})},cropAvatar:function(e){e.preventDefault(),bp.Avatar.setAvatar(this.model)},showPreview:function(e){var t,a;e.w&&e.h&&0<parseInt(e.w,10)&&(t=this.options.full_w,a=this.options.full_h,t=t/e.w,a=a/e.h,this.model.set({x:e.x,y:e.y,w:e.w,h:e.h}),l("#avatar-crop-preview").css({maxWidth:"none",width:Math.round(t*this.model.get("width"))+"px",height:Math.round(a*this.model.get("height"))+"px",marginLeft:"-"+Math.round(t*this.model.get("x"))+"px",marginTop:"-"+Math.round(a*this.model.get("y"))+"px"}))}}),bp.Views.AvatarStatus=bp.View.extend({tagName:"p",className:"updated",id:"bp-avatar-feedback",initialize:function(){this.el.className+=" "+this.options.type,this.value=this.options.value},render:function(){return this.$el.html(this.value),this}}),bp.Views.DeleteAvatar=bp.View.extend({tagName:"div",id:"bp-delete-avatar-container",template:bp.template("bp-avatar-delete"),events:{"click #bp-delete-avatar":"deleteAvatar"},deleteAvatar:function(e){e.preventDefault(),bp.Avatar.deleteAvatar(this.model)}}),bp.Avatar.start())})((bp,jQuery));