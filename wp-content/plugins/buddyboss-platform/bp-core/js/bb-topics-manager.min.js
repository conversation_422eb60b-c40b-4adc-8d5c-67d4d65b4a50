window.wp=window.wp||{},window.bp=window.bp||{},(b=>{var c={config:{topicListSelector:".bb-activity-topics-list",modalSelector:"#bb-rl-activity-topic-form_modal",modalContentSelector:".bb-action-popup-content",backdropSelector:"#bb-hello-backdrop-activity-topic",topicNameSelector:"#bb_topic_name",topicWhoCanPostSelector:'input[name="bb_permission_type"]',topicIdSelector:"#bb_topic_id",itemIdSelector:"#bb_item_id",itemTypeSelector:"#bb_item_type",nonceSelector:"#bb_topic_nonce",actionFromSelector:"#bb_action_from",addTopicButtonSelector:".bb-add-topic",closeModalSelector:".bb-model-close-button, .bb-hello-activity-topic #bb_topic_cancel, .bb-hello-activity-topic-migrate #bb_topic_cancel",submitButtonSelector:"#bb_topic_submit",editTopicSelector:".bb-edit-topic",deleteTopicSelector:".bb-delete-topic",errorContainerSelector:".bb-hello-error",errorContainer:'<div class="bb-hello-error"><i class="bb-icon-rf bb-icon-exclamation"></i>',topicActionsButton:".bb-topic-actions-wrapper .bb-topic-actions_button",migrateTopicButtonSelector:"#bb_topic_migrate",migrateTopicBackdropModal:"#bb-hello-backdrop-activity-topic-migrate",migrateTopicContainerModal:"#bb-activity-topic-migrate-form_modal",modalOpenClass:"activity-modal-open",addTopicAction:"bb_add_topic",editTopicAction:"bb_edit_topic",deleteTopicAction:"bb_delete_topic",migrateTopicAction:"bb_migrate_topic",topicsLimit:bbTopicsManagerVars.topics_limit,ajaxUrl:bbTopicsManagerVars.ajax_url},isNavigating:!1,start:function(){this.init(),this.initTopicsManagerFrontend()},init:function(t){t&&b.extend(!0,this.config,t),this.setupElements(),this.addListeners(),this.makeTopicsSortable(),this.checkTopicsLimit()},initTopicsManagerFrontend:function(){var t;bp.Nouveau=bp.Nouveau||{},void 0!==bp.Nouveau.Activity&&"undefined"!=typeof BP_Nouveau&&(_.extend(bp,_.pick(wp,"Backbone","ajax","template")),bp.Models=bp.Models||{},bp.Collections=bp.Collections||{},bp.Views=bp.Views||{},t=BP_Nouveau.activity.params,this.isEnabledActivityTopic=!_.isUndefined(t.topics.bb_is_enabled_activity_topics)&&t.topics.bb_is_enabled_activity_topics,this.isActivityTopicRequired=!_.isUndefined(t.topics.bb_is_activity_topic_required)&&t.topics.bb_is_activity_topic_required,this.isActivityTopicRequired=!_.isUndefined(t.topics.bb_is_activity_topic_required)&&t.topics.bb_is_activity_topic_required,this.topicTooltipError=!!this.isEnabledActivityTopic&&!_.isUndefined(t.topics.topic_tooltip_error)&&t.topics.topic_tooltip_error,this.topicLists=!this.isEnabledActivityTopic||_.isUndefined(t.topics.topic_lists)?[]:t.topics.topic_lists,void 0!==bp.View&&(bp.Views.TopicSelector=bp.View.extend({tagName:"div",className:"whats-new-topic-selector",template:bp.template("bb-activity-post-form-topic-selector"),events:{"click .bb-topic-selector-button":"toggleTopicSelectorDropdown","click .bb-topic-selector-list a":"selectTopic"},initialize:function(){var t=0,i=(_.isUndefined(this.model.get("topics"))?_.isUndefined(bp.draft_activity.data.topics)||(t=_.isUndefined(bp.draft_activity.data.topics.topic_id)?0:bp.draft_activity.data.topics.topic_id):t=_.isUndefined(this.model.get("topics").topic_id)?0:this.model.get("topics").topic_id,""),e=(_.isUndefined(this.model.get("topics"))?_.isUndefined(bp.draft_activity.data.topics)||(i=_.isUndefined(bp.draft_activity.data.topics.topic_name)?"":bp.draft_activity.data.topics.topic_name):i=_.isUndefined(this.model.get("topics").topic_name)?"":this.model.get("topics").topic_name,[]),o=this.model.get("topics"),e=o&&!_.isUndefined(o.topic_lists)?o.topic_lists:_.isUndefined(bp.draft_activity.data.topics)||_.isUndefined(bp.draft_activity.data.topics.topic_lists)?c.topicLists:bp.draft_activity.data.topics.topic_lists;this.model.set("topics",{topic_id:t,topic_name:i,topic_lists:e}),this.listenTo(Backbone,"topic:update",this.updateTopics),this.listenTo(this.model,"change:topics",function(t,i){i&&this.updateTopics(i)}),b(document).on("click.topicSelector",b.proxy(this.closeTopicSelectorDropdown,this))},updateTopics:function(t){_.isObject(t)&&!_.isUndefined(t.topic_lists)&&(i=t.topic_lists);var i,e=this.model.get("topics"),o=e?e.topic_id:0,e=e?e.topic_name:"";_.isObject(t)&&(_.isUndefined(t.topic_id)||(o=t.topic_id),_.isUndefined(t.topic_name)||(e=t.topic_name)),this.model.set("topics",{topic_lists:i,topic_id:o,topic_name:e}),_.isEmpty(i)?b(".bb-topic-tooltip-wrapper").remove():b(document).trigger("bb_display_full_form"),void 0!==bp.Nouveau.Activity&&bp.Nouveau.Activity.postForm&&b("#whats-new").trigger("input"),this.render()},render:function(){this.$el.html(this.template(this.model.attributes))},toggleTopicSelectorDropdown:function(){this.$el.toggleClass("is-active")},selectTopic:function(t){t.preventDefault();var i=b(t.currentTarget).data("topic-id"),t=b(t.currentTarget).text().trim();""===i?(this.model.set("topics",{topic_id:0,topic_name:"",topic_lists:this.model.get("topics").topic_lists}),t=this.$el.find(".bb-topic-selector-button").data("select-topic-text")):this.model.set("topics",{topic_id:i,topic_name:t,topic_lists:this.model.get("topics").topic_lists}),this.$el.find(".bb-topic-selector-button").text(t),this.$el.removeClass("is-active"),this.$el.find(".bb-topic-selector-list li a").removeClass("selected"),""!==i&&this.$el.find('.bb-topic-selector-list li a[data-topic-id="'+i+'"]').addClass("selected"),void 0!==bp.Nouveau.Activity&&bp.Nouveau.Activity.postForm&&b("#whats-new").trigger("input")},closeTopicSelectorDropdown:function(t){b(t.target).closest(".whats-new-topic-selector").length||this.$el.removeClass("is-active")}})),this.addFrontendListeners())},setupElements:function(){this.$document=b(document),this.$topicList=b(this.config.topicListSelector),this.$modal=b(this.config.modalSelector),this.$backdrop=b(this.config.backdropSelector),this.$topicName=b(this.config.topicNameSelector),this.$topicWhoCanPost=b(this.config.topicWhoCanPostSelector),this.$topicId=b(this.config.topicIdSelector),this.$addTopicButton=b(this.config.addTopicButtonSelector),this.$nonce=b(this.config.nonceSelector),this.$itemId=b(this.config.itemIdSelector),this.$itemType=b(this.config.itemTypeSelector),this.$actionFrom=b(this.config.actionFromSelector),this.$migrateTopicBackdropModalSelector=b(this.config.migrateTopicBackdropModal),this.$migrateTopicContainerModalSelector=b(this.config.migrateTopicContainerModal)},addListeners:function(){this.$document.on("click",this.config.addTopicButtonSelector,this.handleAddTopic.bind(this)),this.$document.on("click",this.config.submitButtonSelector,this.handleSubmitTopic.bind(this)),this.$document.on("click",this.config.closeModalSelector,this.handleCloseModal.bind(this)),this.$document.on("click",this.config.editTopicSelector,this.handleEditTopic.bind(this)),this.$document.on("click",this.config.deleteTopicSelector,this.handleDeleteTopic.bind(this)),this.$document.on("click",this.config.topicActionsButton,this.handleActionsDropdown.bind(this)),this.$document.on("click",".bb-topic-actions-wrapper .bp-secondary-action",this.closeActionsDropdown.bind(this)),this.$document.on("click",function(t){b(t.target).closest(".bb-topic-actions-wrapper").length||b(".bb-topic-actions-wrapper").removeClass("active")}),this.$document.on("change",".bb-topic-name-field",this.enableDisableSubmitButton.bind(this)),this.$document.on("keyup","#bb_topic_name, .bb-topic-name-field",this.enableDisableSubmitButton.bind(this)),this.$document.on("change",'input[name="bb_migrate_existing_topic"]',this.enableDisableMigrateTopicButton.bind(this)),this.$document.on("change","#bb_existing_topic_id",this.updateMigrateButtonForDropdown.bind(this)),this.$document.on("click",this.config.migrateTopicButtonSelector,this.handleMigrateTopic.bind(this))},makeTopicsSortable:function(){var o=this;this.$topicList.length&&this.$topicList.sortable({update:function(t){var i=b(t.target),e=(i.addClass("is-loading"),[]),t=(i.find(".bb-activity-topic-item").find(".bb-edit-topic").each(function(){var t=b(this).data("topic-attr").topic_id;t&&e.push(t)}),{action:"bb_update_topics_order",topic_ids:e,nonce:bbTopicsManagerVars.bb_update_topics_order_nonce});b.post(o.config.ajaxUrl,t,function(t){i.removeClass("is-loading"),t.success?t.data&&t.data.message&&(i.after('<div class="bb-topics-sort-success notice notice-success"><p>'+t.data.message+"</p></div>"),setTimeout(function(){b(".bb-topics-sort-success").fadeOut(300,function(){b(this).remove()})},3e3)):(t.data&&t.data.error&&alert(t.data.error),i.sortable("cancel"))}.bind(this)).fail(function(){i.removeClass("is-loading"),alert(bbTopicsManagerVars.generic_error),i.sortable("cancel")})}})},handleAddTopic:function(t){t.preventDefault(),t.stopPropagation(),this.checkTopicsLimit()||(b("body").addClass(this.config.modalOpenClass),this.$modal.find(this.config.errorContainerSelector).remove(),this.$modal.find(".bb-model-header h4 .target_name").length&&this.$modal.find(".bb-model-header h4 .target_name").text(bbTopicsManagerVars.create_topic_text),this.$modal.find(".bb-hello-title h2").length&&this.$modal.find(".bb-hello-title h2").text(bbTopicsManagerVars.create_topic_text),this.$topicName.val(""),this.$topicId.val(""),this.$topicWhoCanPost.first().prop("checked",!0),this.$modal.show(),this.$backdrop.show(),b(document).trigger("bb_modal_opened",[this.$modal]))},handleSubmitTopic:function(d){d.preventDefault(),d.stopPropagation(),this.$modal.find(this.config.errorContainerSelector).remove();var t=this.$topicName.data("selected"),t=t?t.name:this.$topicName.val(),i=this.$topicWhoCanPost.filter(":checked").val(),r=this.$topicId.val(),e=this.$itemId.val(),o=this.$itemType.val(),c=this.$nonce.val(),a=this.$actionFrom.val(),s=b("#bb_is_global_activity").val();""!==t&&(this.$modal.addClass("loading"),t={action:this.config.addTopicAction,name:t,permission_type:i,topic_id:r,item_id:e,item_type:o,nonce:c,action_from:a,is_global_activity:s},i=this.config.ajaxUrl,b.post(i,t,function(t){var i,e,o,c,a,s,n,l,p;this.$modal.removeClass("loading"),t.success&&t.data.content?(s=(c=(o=t.data.content).topic).topic_id,n=c.name,l=c.permission_type,p=c.item_id,i=c.item_type,e=o.edit_nonce,o=o.delete_nonce,c=!_.isUndefined(c.is_global_activity)&&c.is_global_activity,a=wp.template("bb-topic-lists"),s={topic_id:s,topic_name:n,topic_who_can_post:l,item_id:p,item_type:i,edit_nonce:e,delete_nonce:o},c&&(s.is_global_activity=c),n=a({topics:s}),l=b(n),(p=this.$topicList.find('.bb-activity-topic-item[data-topic-id="'+r+'"]')).length?p.replaceWith(l):this.$topicList.append(l),this.handleCloseModal(d),this.checkTopicsLimit()):(this.$modal.find(this.config.modalContentSelector).prepend(this.config.errorContainer),this.$modal.find(this.config.errorContainerSelector).append(t.data.error))}.bind(this)))},handleCloseModal:function(t){t.preventDefault(),t.stopPropagation(),b("body").removeClass(this.config.modalOpenClass),(this.$modal.hasClass("bb-modal-panel--activity-topic")||this.$modal.hasClass("bb-action-popup--activity-topic"))&&(this.$modal.hide(),this.$backdrop.hide()),(this.$migrateTopicContainerModalSelector.hasClass("bb-modal-panel--activity-topic-migrate")||this.$migrateTopicContainerModalSelector.hasClass("bb-action-popup--activity-migrate-topic"))&&(this.$migrateTopicBackdropModalSelector.hide(),this.$migrateTopicContainerModalSelector.hide(),b('input[name="bb_migrate_existing_topic"]:first').prop("checked",!0),b("#bb_existing_topic_id option:not(:first)").prop("selected",!1)),this.$topicName.val(""),this.$topicWhoCanPost.prop("checked",!1),this.$topicId.val(""),b("#bb_is_global_activity").val(""),this.$topicName.prop("readonly",!1),this.$topicName.prop("disabled",!1),b(this.config.submitButtonSelector).prop("disabled",!0),b(document).trigger("bb_modal_closed",[this.$modal])},handleEditTopic:function(t){t.preventDefault(),t.stopPropagation();var t=b(t.currentTarget).data("topic-attr"),i=t.topic_id,e=t.item_id,o=t.item_type,c=t.nonce,t=t.bb_is_global_activity,a=(b("body").addClass(this.config.modalOpenClass),this.$modal.find(".bb-model-header h4 .target_name").length&&this.$modal.find(".bb-model-header h4 .target_name").text(bbTopicsManagerVars.edit_topic_text),this.$modal.find(".bb-hello-title h2").length&&this.$modal.find(".bb-hello-title h2").text(bbTopicsManagerVars.edit_topic_text),this.$modal.show(),this.$backdrop.show(),this.$topicWhoCanPost.prop("checked",!1),b(document).trigger("bb_modal_opened",[this.$modal]),this.$modal.find(this.config.errorContainerSelector)),a=(0<a.length&&a.remove(),this.$modal.addClass("is-loading"),{action:this.config.editTopicAction,topic_id:i,item_id:e,item_type:o,nonce:c,is_global_activity:t}),i=this.config.ajaxUrl||wp.ajax.settings.url;b.post(i,a,function(t){var i,e;this.$modal.removeClass("is-loading"),t.success?(i=t.data.topic,this.$topicName.hasClass("select2-hidden-accessible")?(0===this.$topicName.find("option[value='"+i.slug+"']").length&&(e=new Option(i.name,i.slug,!0,!0),this.$topicName.append(e)),this.$topicName.val(i.slug).trigger("change"),this.$topicName.prop("disabled",i.is_global_activity),i.is_global_activity?this.$topicName.closest(".input-field").addClass("bb-topic-global-selected"):this.$topicName.closest(".input-field").removeClass("bb-topic-global-selected")):(this.$topicName.val(i.name),this.$topicName.prop("readonly",i.is_global_activity)),this.$topicWhoCanPost.filter('[value="'+i.permission_type+'"]').prop("checked",!0),this.$topicId.val(i.topic_id),b("#bb_is_global_activity").val(i.is_global_activity),this.handleEnableDisableSubmitButton(i.name)):(this.$modal.find(this.config.modalContentSelector).prepend(this.config.errorContainer),this.$modal.find(this.config.errorContainerSelector).append(t.data.error))}.bind(this))},handleDeleteTopic:function(t){t.preventDefault(),t.stopPropagation();var t=b(t.currentTarget),i=t.closest(".bb-activity-topic-item"),t=t.data("topic-attr"),e=t.topic_id,o=t.nonce,c=t.item_id,t=t.item_type,a=i.find(".bb-topic-title").text().trim(),s=b('input[name="bb_migrate_existing_topic"]:checked').val(),n=b("#bb_existing_topic_id").val();this.handleEnableDisableMigrateTopicButton(s,n),this.checkTopicPostsBeforeDelete({topicId:e,topicName:a,nonce:o,itemId:c,itemType:t,$topicItem:i})},checkTopicPostsBeforeDelete:function(t){var i={action:this.config.deleteTopicAction,topic_id:t.topicId,nonce:t.nonce,item_id:t.itemId,item_type:t.itemType},t=t.topicName||"";c.$migrateTopicContainerModalSelector.find("#bb-hello-title").length&&c.$migrateTopicContainerModalSelector.find("#bb-hello-title").text(bbTopicsManagerVars.delete_topic_text.replace("%s",t)),c.$migrateTopicContainerModalSelector.find(".bb-model-header h4 .target_name").length&&c.$migrateTopicContainerModalSelector.find(".bb-model-header h4 .target_name").text(bbTopicsManagerVars.delete_topic_text.replace("%s",t)),this.$migrateTopicContainerModalSelector.addClass("is-loading"),b("body").addClass(this.config.modalOpenClass),this.$migrateTopicContainerModalSelector.show(),this.$migrateTopicBackdropModalSelector.show(),b.post(this.config.ajaxUrl,i,function(t){var i;c.$migrateTopicContainerModalSelector.removeClass("is-loading"),t.success&&t.data&&(t=t.data,c.$migrateTopicContainerModalSelector.find("#bb_topic_id").val(t.topic_id),c.$migrateTopicContainerModalSelector.find("#bb_item_id").val(t.item_id),c.$migrateTopicContainerModalSelector.find("#bb_item_type").val(t.item_type),c.$migrateTopicContainerModalSelector.find("#bb_topic_nonce").val(t.nonce),(i=c.$migrateTopicContainerModalSelector.find("#bb_existing_topic_list #bb_existing_topic_id")).find("option:not(:first)").remove(),t.topic_lists)&&_.each(t.topic_lists,function(t){t=new Option(t.name,t.topic_id);i.append(t)})}).fail(function(){}.bind(this))},handleMigrateTopic:function(i){var t=this.$migrateTopicContainerModalSelector.find("#bb_topic_id").val(),e=this.$migrateTopicContainerModalSelector.find("#bb_topic_nonce").val(),o=this.$migrateTopicContainerModalSelector.find("#bb_item_id").val(),c=this.$migrateTopicContainerModalSelector.find("#bb_item_type").val(),a=this.$migrateTopicContainerModalSelector.find("#bb_existing_topic_id").val(),s=b('.bb-activity-topics-list .bb-activity-topic-item[data-topic-id="'+t+'"]'),n=b('input[name="bb_migrate_existing_topic"]:checked').val(),t={action:this.config.migrateTopicAction,old_topic_id:t,nonce:e,item_id:o,item_type:c,migrate_type:n},e=("migrate"===n&&(t.new_topic_id=a),this.config.ajaxUrl);b(this.config.migrateTopicButtonSelector).addClass("is-loading"),b.post(e,t,function(t){b(this.config.migrateTopicButtonSelector).removeClass("is-loading"),t.success?(s.remove(),this.handleCloseModal(i),this.checkTopicsLimit()):(this.$migrateTopicContainerModalSelector.find(".bb-hello-content").prepend(this.config.errorContainer),this.$migrateTopicContainerModalSelector.find(".bb-hello-error").append(t.data.error))}.bind(this)).fail(function(){this.$migrateTopicContainerModalSelector.find(".bb-hello-content").prepend(this.config.errorContainer),this.$migrateTopicContainerModalSelector.find(".bb-hello-error").append(bbTopicsManagerVars.generic_error)}.bind(this))},checkTopicsLimit:function(){var t=this.$topicList.find(".bb-activity-topic-item").length,i=this.config.topicsLimit,e=b(".bb-topic-limit-not-reached"),o=b(".bb-topic-limit-reached"),i=i<=t;return i?(this.$addTopicButton.hide(),e.hide(),o.show()):(this.$addTopicButton.show(),this.$addTopicButton.hasClass("bp-hide")&&this.$addTopicButton.removeClass("bp-hide"),0===parseInt(t)?e.hide():e.show(),o.hide()),0<t?this.$topicList.closest(".bb-activity-topics-content").addClass("bb-has-topics"):this.$topicList.closest(".bb-activity-topics-content").removeClass("bb-has-topics"),i},handleActionsDropdown:function(t){t.preventDefault(),t.stopPropagation();t=b(t.currentTarget).closest(".bb-topic-actions-wrapper");b(".bb-topic-actions-wrapper.active").not(t).removeClass("active"),t.toggleClass("active")},closeActionsDropdown:function(t){b(t.target).closest(".bb-topic-actions-wrapper").removeClass("active")},addFrontendListeners:function(){c.isEnabledActivityTopic&&(this.isActivityTopicRequired&&(this.$document.on("mouseenter focus","#whats-new-submit",this.showTopicTooltip.bind(this)),this.$document.on("mouseleave blur","#whats-new-submit",this.hideTopicTooltip.bind(this))),c.isActivityTopicRequired&&this.$document.on("bb_display_full_form",function(){0===b(".activity-update-form #whats-new-submit .bb-topic-tooltip-wrapper").length&&b(".activity-update-form.modal-popup #whats-new-submit").prepend('<div class="bb-topic-tooltip-wrapper"><div class="bb-topic-tooltip">'+c.topicTooltipError+"</div></div>")}),this.$document.on("click",".activity-topic-selector li a",this.topicActivityFilter.bind(this)),void 0!==BP_Nouveau.is_send_ajax_request&&"1"===BP_Nouveau.is_send_ajax_request&&this.$document.ready(this.handleUrlHashTopic.bind(this)),this.$document.on("click",".bb-topic-url",this.topicActivityFilter.bind(this)),b(window).on("hashchange",this.handleBrowserNavigation.bind(this)),b(window).on("popstate",this.handleBrowserNavigation.bind(this)))},showTopicTooltip:function(t){t=b(t.currentTarget).closest("#whats-new-submit");0<t.closest(".focus-in--empty").length&&t.find(".bb-topic-tooltip-wrapper").addClass("active").show()},hideTopicTooltip:function(){b(".bb-topic-tooltip-wrapper").removeClass("active").hide()},topicActivityFilter:function(t){t.preventDefault(),t.stopPropagation();var i=b(t.currentTarget),e=i.data("topic-id"),o=i.attr("href"),c=b(".activity-topic-selector li a"),a=c.filter('[data-topic-id="'+e+'"]');if(!i.closest("li").hasClass("menu-item-has-children")){var s="";if(o)try{s=(l=new URL(o,window.location.origin)).searchParams.get("bb-topic")}catch(t){s=new URLSearchParams(o.split("?")[1]||"").get("bb-topic")}var n=BP_Nouveau.activity.params.topics.is_activity_directory;if(n&&0<i.closest("li.groups").length)window.location.href=o;else{var l,n=window.location.href;try{l=new URL(n),!e||i.hasClass("all")||"all"===s?l.searchParams.delete("bb-topic"):l.searchParams.set("bb-topic",s),window.history.pushState({},"",l.toString())}catch(t){o=n.split("?")[0];e&&!i.hasClass("all")&&"all"!==s&&(o+="?bb-topic="+encodeURIComponent(s)),window.history.pushState({},"",o)}c.removeClass("selected active"),a.length&&(0<a.closest("li").closest(".bb_nav_more_dropdown").length?(this.moveTopicPosition({$topicItem:a,topicId:e}),b('.activity-topic-selector li a[data-topic-id="'+e+'"]').first()):a).addClass("selected active"),!e||i.hasClass("all")||"all"===s?(bp.Nouveau.setStorage("bp-activity","topic_id",""),0<(l=c.first()).length&&l.addClass("selected active")):bp.Nouveau.setStorage("bp-activity","topic_id",e),bp.Nouveau.Activity.filterActivity(t)}}},handleUrlHashTopic:function(){var t,i,e=new URLSearchParams(window.location.search).get("bb-topic");e?(i=(t=b(".activity-topic-selector li a")).filter(function(){var t=b(this).attr("href")||"",i=b(this).data("topic-slug"),t=t.match(/bb-topic=([^&]+)/);return(t?t[1]:"")===e||i===e})).length&&(i.trigger("click"),c.moveTopicPosition({$topicItem:i,topicId:i.data("topic-id")}),t.removeClass("selected active"),i.addClass("selected active"),bp.Nouveau.setStorage("bp-activity","topic_id",i.data("topic-id")),0<(t=b('[data-bp-list="activity"]')).length)&&b("html, body").animate({scrollTop:t.offset().top-200},300):(bp.Nouveau.setStorage("bp-activity","topic_id",""),0<(i=b(".activity-topic-selector li a").first()).length&&i.addClass("selected active"))},moveTopicPosition:function(t){var i,e,o,c=t.$topicItem,a=t.topicId,t=b(".activity-topic-selector").find("> ul"),s=t.find("li:has(a.more-action-button)"),n=0<c.closest(".bb_nav_more_dropdown").length;a&&!c.hasClass("all")&&n&&(s=s.prev("li"),o=(i=c.closest("li")).clone(!0),t.find("li:first-child").after(o),o=s.clone(!0),(e=(e=b(".more-action-button").closest("li").find("ul")).length?e:b("ul.bb_nav_more_dropdown")).length)&&(e.prepend(o),e.find("li a").each(function(){var t=b(this);t.data("topic-id")===a&&t.closest("li").remove()}),s.remove(),setTimeout(function(){e.find("li a").each(function(){var t=b(this);t.data("topic-id")===a&&t.closest("li").remove()})},100),bp.Nouveau.wrapNavigation(".activity-topic-selector ul",120,!0)),!a||c.hasClass("all")||n||(i=c.closest("li"),o=t.find("li:first-child"),i.is(o.next()))||i.insertAfter(o)},bbTopicValidateContent:function(t){var i=t.selector,e=t.validContent,o=t.class,t=t.data;_.isUndefined(t.poll)||_.isUndefined(t.poll_id)||""===t.poll_id||(e=!0),!_.isUndefined(t.topics)&&!_.isUndefined(t.topics.topic_lists)&&0<t.topics.topic_lists.length&&(e&&!_.isUndefined(t.topics.topic_id)&&0!==parseInt(t.topics.topic_id)?(i.removeClass(o),b("#whats-new-submit").find(".bb-topic-tooltip-wrapper").remove()):e&&(_.isUndefined(t.topics.topic_id)||0===parseInt(t.topics.topic_id))?(i.addClass(o),b(document).trigger("bb_display_full_form")):e||_.isUndefined(t.topics.topic_id)||0===parseInt(t.topics.topic_id)?e||!_.isUndefined(t.topics.topic_id)&&0!==parseInt(t.topics.topic_id)?i.removeClass(o):(i.addClass(o),b(document).trigger("bb_display_full_form")):(i.addClass(o),b("#whats-new-submit").find(".bb-topic-tooltip-wrapper").remove()))},enableDisableSubmitButton:function(t){t=b(t.currentTarget).val();this.handleEnableDisableSubmitButton(t)},handleEnableDisableSubmitButton:function(t){""===t?(this.$migrateTopicContainerModalSelector&&this.$migrateTopicContainerModalSelector.is(":visible")&&b(this.config.migrateTopicButtonSelector).prop("disabled",!0),this.$modal&&this.$modal.is(":visible")&&b(this.config.submitButtonSelector).prop("disabled",!0)):(this.$migrateTopicContainerModalSelector&&this.$migrateTopicContainerModalSelector.is(":visible")&&b(this.config.migrateTopicButtonSelector).prop("disabled",!1),this.$modal&&this.$modal.is(":visible")&&b(this.config.submitButtonSelector).prop("disabled",!1))},enableDisableMigrateTopicButton:function(t){var t=b(t.currentTarget).val(),i=b("#bb_existing_topic_id").val();this.handleEnableDisableMigrateTopicButton(t,i)},updateMigrateButtonForDropdown:function(t){var i=b('input[name="bb_migrate_existing_topic"]:checked').val(),t=b(t.currentTarget).val();this.handleEnableDisableMigrateTopicButton(i,t)},handleEnableDisableMigrateTopicButton:function(t,i){"delete"===t||"migrate"===t&&0!==parseInt(i)?b(this.config.migrateTopicButtonSelector).prop("disabled",!1):b(this.config.migrateTopicButtonSelector).prop("disabled",!0)},handleBrowserNavigation:function(t){this.isNavigating=!0;var i,e,o,c=new URLSearchParams(window.location.search).get("bb-topic"),a=(c?(o=(i=b(".activity-topic-selector li a")).filter(function(){var t=b(this).attr("href")||"",i=b(this).data("topic-slug"),t=t.match(/bb-topic=([^&]+)/);return(t?t[1]:"")===c||i===c})).length&&(e=o.data("topic-id"),i.removeClass("selected active"),(0<o.closest("li").closest(".bb_nav_more_dropdown").length?(this.moveTopicPosition({$topicItem:o,topicId:e}),b('.activity-topic-selector li a[data-topic-id="'+e+'"]').first()):o).addClass("selected active"),bp.Nouveau.setStorage("bp-activity","topic_id",e),bp.Nouveau.Activity.filterActivity(t)):((i=b(".activity-topic-selector li a")).removeClass("selected active"),bp.Nouveau.setStorage("bp-activity","topic_id",""),0<(o=i.first()).length&&o.addClass("selected active"),bp.Nouveau.Activity.filterActivity(t)),this);setTimeout(function(){a.isNavigating=!1},100)}};b(function(){c.start()}),window.BBTopicsManager=c})(jQuery);