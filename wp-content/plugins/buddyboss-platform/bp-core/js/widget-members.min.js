function member_widget_click_handler(){jQuery(".widget div#members-list-options a").on("click",function(){var t=this;return jQuery(t).addClass("loading"),jQuery(".widget div#members-list-options a").removeClass("selected"),jQuery(this).addClass("selected"),jQuery.post(ajaxurl,{action:"widget_members",cookie:encodeURIComponent(document.cookie),_wpnonce:jQuery("input#_wpnonce-members").val(),"max-members":jQuery("input#members_widget_max").val(),filter:jQuery(this).attr("id")},function(e){jQuery(t).removeClass("loading"),member_widget_response(e)}),!1})}function member_widget_response(e){var t=jQuery.parseJSON(e);1===t.success?(jQuery(".widget ul#members-list").fadeOut(200,function(){jQuery(".widget ul#members-list").html(t.data),jQuery(".widget ul#members-list").fadeIn(200)}),!0===t.show_more?jQuery(".more-block").removeClass("bp-hide"):jQuery(".more-block").addClass("bp-hide")):jQuery(".widget ul#members-list").fadeOut(200,function(){var e="<p>"+t.data+"</p>";jQuery(".widget ul#members-list").html(e),jQuery(".widget ul#members-list").fadeIn(200)})}function member_widget_online_click_handler(){jQuery(".widget div#who-online-members-list-options a").on("click",function(){jQuery(this).addClass("loading"),jQuery(".widget div#who-online-members-list-options a").removeClass("selected"),jQuery(this).addClass("selected");var e=jQuery(this).attr("data-content");return jQuery(".widget_bp_core_whos_online_widget .widget-content").hide(),jQuery(".widget_bp_core_whos_online_widget #"+e).show(),jQuery(this).removeClass("loading"),!1})}jQuery(document).ready(function(){member_widget_click_handler(),member_widget_online_click_handler(),"undefined"!=typeof wp&&wp.customize&&wp.customize.selectiveRefresh&&wp.customize.selectiveRefresh.bind("partial-content-rendered",function(){member_widget_click_handler(),member_widget_online_click_handler()}),"undefined"!=typeof wp&&void 0!==wp.heartbeat&&(jQuery(document).on("heartbeat-send",function(e,t){jQuery("#boss_whos_online_widget_heartbeat").length&&(t.boss_whos_online_widget=jQuery("#boss_whos_online_widget_heartbeat").data("max")),jQuery("#boss_recently_active_widget_heartbeat").length&&(t.boss_recently_active_widget=jQuery("#boss_recently_active_widget_heartbeat").data("max")),jQuery("#recently-active-members").length&&(t.buddyboss_members_widget_active=jQuery("#recently-active-members").data("max")),jQuery(".bs-heartbeat-reload").removeClass("hide")}),jQuery(document).on("heartbeat-tick",function(e,t){jQuery("#boss_whos_online_widget_total_heartbeat").length&&jQuery("#boss_whos_online_widget_total_heartbeat").html(t.boss_whos_online_widget_total),jQuery("#boss_whos_online_widget_heartbeat").length&&jQuery("#boss_whos_online_widget_heartbeat").html(t.boss_whos_online_widget),jQuery("#boss_whos_online_widget_connections").length&&jQuery("#boss_whos_online_widget_connections").html(t.boss_whos_online_widget_connection),jQuery("#who-online-members-list-options #online-members").length&&jQuery("#who-online-members-list-options #online-members .widget-num-count").html(t.boss_whos_online_widget_total),jQuery("#who-online-members-list-options #connection-members").length&&jQuery("#who-online-members-list-options #connection-members .widget-num-count").html(t.boss_whos_online_widget_total_connection),jQuery("#boss_recently_active_widget_heartbeat").length&&jQuery("#boss_recently_active_widget_heartbeat").html(t.boss_recently_active_widget),jQuery("#members-list").length&&jQuery("#recently-active-members").length&&jQuery("#recently-active-members").hasClass("selected")&&jQuery(".widget_bp_core_members_widget").find("#members-list").html(t.buddyboss_members_widget_active),jQuery(".bs-heartbeat-reload").addClass("hide")})),jQuery("#boss_whos_online_widget_connections").length&&(jQuery("#boss_whos_online_widget_connections").hide(),jQuery("#online-members").addClass("selected"))});