<?php
/**
 * Plugin Name: RSS Aggregator
 * Plugin URI: https://example.com/rss-aggregator
 * Description: Agregacja treści z kanałów RSS z integracją BuddyBoss i GeoDirectory
 * Version: 1.3.0
 * Author: Tw<PERSON>j Autor
 * Author URI: https://example.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: rss-aggregator
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('RSS_AGGREGATOR_VERSION', '1.3.0');
define('RSS_AGGREGATOR_DB_VERSION', '1.3.0');
define('RSS_AGGREGATOR_PLUGIN_FILE', __FILE__);
define('RSS_AGGREGATOR_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('RSS_AGGREGATOR_PLUGIN_URL', plugin_dir_url(__FILE__));
define('RSS_AGGREGATOR_PLUGIN_BASENAME', plugin_basename(__FILE__));

// Database table names
global $wpdb;
define('RSS_AGGREGATOR_FEEDS_TABLE', $wpdb->prefix . 'rss_aggregator_feeds');
define('RSS_AGGREGATOR_ITEMS_TABLE', $wpdb->prefix . 'rss_aggregator_items');

/**
 * Main plugin class
 */
class RSS_Aggregator_Plugin {
    
    /**
     * Plugin instance
     */
    private static $instance = null;
    
    /**
     * Get plugin instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * Initialize plugin
     */
    private function init() {
        // Load plugin textdomain
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        
        // Include required files
        $this->include_files();
        
        // Initialize components
        add_action('init', array($this, 'init_components'));
        
        // Activation and deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Load plugin textdomain
     */
    public function load_textdomain() {
        // Get user's language preference
        $language = get_option('rss_aggregator_language', 'en_US');

        // Override WordPress locale for this plugin
        add_filter('plugin_locale', function($locale, $domain) use ($language) {
            if ($domain === 'rss-aggregator') {
                return $language;
            }
            return $locale;
        }, 10, 2);

        load_plugin_textdomain(
            'rss-aggregator',
            false,
            dirname(RSS_AGGREGATOR_PLUGIN_BASENAME) . '/languages'
        );
    }
    
    /**
     * Include required files
     */
    private function include_files() {
        // Core classes
        require_once RSS_AGGREGATOR_PLUGIN_DIR . 'includes/class-rss-aggregator.php';
        require_once RSS_AGGREGATOR_PLUGIN_DIR . 'includes/class-rss-database.php';
        require_once RSS_AGGREGATOR_PLUGIN_DIR . 'includes/class-rss-fetcher.php';
        require_once RSS_AGGREGATOR_PLUGIN_DIR . 'includes/class-rss-parser.php';
        require_once RSS_AGGREGATOR_PLUGIN_DIR . 'includes/class-rss-thumbnail-extractor.php';
        require_once RSS_AGGREGATOR_PLUGIN_DIR . 'includes/class-rss-cron.php';
        require_once RSS_AGGREGATOR_PLUGIN_DIR . 'includes/class-rss-integrations.php';
        
        // Admin classes
        if (is_admin()) {
            require_once RSS_AGGREGATOR_PLUGIN_DIR . 'admin/class-rss-admin.php';
        }

        // Public classes
        if (!is_admin()) {
            require_once RSS_AGGREGATOR_PLUGIN_DIR . 'public/class-rss-public.php';
        }
    }
    
    /**
     * Initialize components
     */
    public function init_components() {
        // Check dependencies
        if (!$this->check_dependencies()) {
            return;
        }
        
        // Initialize main class
        RSS_Aggregator::get_instance();

        // Initialize admin - only in admin area
        if (is_admin()) {
            RSS_Admin::get_instance();
        }

        // Initialize public
        if (!is_admin()) {
            RSS_Public::get_instance();
        }

        // Debug log
        error_log('RSS Aggregator: Plugin initialized successfully');
    }
    
    /**
     * Check plugin dependencies
     */
    private function check_dependencies() {
        $missing_plugins = array();
        
        // Check if BuddyBoss is active
        if (!class_exists('BuddyPress')) {
            $missing_plugins[] = 'BuddyBoss Platform';
        }
        
        // Check if GeoDirectory is active
        if (!class_exists('GeoDirectory')) {
            $missing_plugins[] = 'GeoDirectory';
        }
        
        if (!empty($missing_plugins)) {
            add_action('admin_notices', function() use ($missing_plugins) {
                $plugin_list = implode(', ', $missing_plugins);
                echo '<div class="notice notice-error"><p>';
                printf(
                    __('RSS Aggregator requires the following plugins to be active: %s', 'rss-aggregator'),
                    '<strong>' . $plugin_list . '</strong>'
                );
                echo '</p></div>';
            });
            return false;
        }
        
        return true;
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables
        $this->create_tables();
        
        // Set default options
        $this->set_default_options();
        
        // Schedule cron events
        $this->schedule_cron_events();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clear scheduled cron events
        $this->clear_cron_events();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Create database tables
     */
    private function create_tables() {
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        
        $charset_collate = '';
        if (!empty($GLOBALS['wpdb']->charset)) {
            $charset_collate = "DEFAULT CHARACTER SET {$GLOBALS['wpdb']->charset}";
        }
        if (!empty($GLOBALS['wpdb']->collate)) {
            $charset_collate .= " COLLATE {$GLOBALS['wpdb']->collate}";
        }
        
        // Feeds table
        $feeds_table_sql = "CREATE TABLE " . RSS_AGGREGATOR_FEEDS_TABLE . " (
            id int(11) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            url varchar(500) NOT NULL,
            geodirectory_place_id bigint(20) DEFAULT NULL,
            geodirectory_place_name varchar(255) DEFAULT NULL,
            assigned_user_id bigint(20) DEFAULT NULL,
            assigned_user_name varchar(255) DEFAULT NULL,
            county varchar(100) DEFAULT NULL,
            update_frequency varchar(50) DEFAULT 'hourly',
            initial_import_count int(11) DEFAULT 10,
            last_updated datetime DEFAULT NULL,
            status enum('active','inactive') DEFAULT 'active',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY url (url),
            KEY geodirectory_place_id (geodirectory_place_id),
            KEY assigned_user_id (assigned_user_id),
            KEY county (county),
            KEY status (status)
        ) $charset_collate;";
        
        // Items table
        $items_table_sql = "CREATE TABLE " . RSS_AGGREGATOR_ITEMS_TABLE . " (
            id int(11) NOT NULL AUTO_INCREMENT,
            feed_id int(11) NOT NULL,
            post_id bigint(20) DEFAULT NULL,
            title varchar(500) NOT NULL,
            description text,
            url varchar(500) NOT NULL,
            guid varchar(500) DEFAULT NULL,
            pub_date datetime DEFAULT NULL,
            thumbnail_url varchar(500) DEFAULT NULL,
            thumbnail_local varchar(500) DEFAULT NULL,
            processed tinyint(1) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY feed_guid (feed_id, guid),
            KEY feed_id (feed_id),
            KEY post_id (post_id),
            KEY processed (processed),
            KEY pub_date (pub_date)
        ) $charset_collate;";
        
        dbDelta($feeds_table_sql);
        dbDelta($items_table_sql);
        
        // Save database version
        update_option('rss_aggregator_db_version', RSS_AGGREGATOR_VERSION);

        // Run database migrations if needed
        rss_aggregator_run_migrations();
    }
    
    /**
     * Set default options
     */
    private function set_default_options() {
        $default_options = array(
            'rss_aggregator_default_frequency' => 'hourly',
            'rss_aggregator_max_items_per_feed' => 50,
            'rss_aggregator_enable_thumbnails' => 1,
            'rss_aggregator_enable_buddyboss' => 1,
            'rss_aggregator_enable_geodirectory' => 1,
            'rss_aggregator_post_status' => 'publish',
            'rss_aggregator_post_author' => 1
        );
        
        foreach ($default_options as $option => $value) {
            if (get_option($option) === false) {
                update_option($option, $value);
            }
        }
    }
    
    /**
     * Schedule cron events
     */
    private function schedule_cron_events() {
        if (!wp_next_scheduled('rss_aggregator_check_feeds')) {
            wp_schedule_event(time(), 'hourly', 'rss_aggregator_check_feeds');
        }
    }
    
    /**
     * Clear cron events
     */
    private function clear_cron_events() {
        wp_clear_scheduled_hook('rss_aggregator_check_feeds');
        
        // Clear individual feed cron events
        global $wpdb;
        $feeds = $wpdb->get_results("SELECT id FROM " . RSS_AGGREGATOR_FEEDS_TABLE);
        foreach ($feeds as $feed) {
            wp_clear_scheduled_hook('rss_aggregator_update_feed_' . $feed->id);
        }
    }
}

/**
 * Run database migrations
 */
function rss_aggregator_run_migrations() {
    global $wpdb;

    $current_version = get_option('rss_aggregator_db_version', '1.0.0');

    // Migration to add initial_import_count column
    if (version_compare($current_version, '1.1.0', '<')) {
        $feeds_table = RSS_AGGREGATOR_FEEDS_TABLE;

        // Check if column exists
        $column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$feeds_table} LIKE 'initial_import_count'");

        if (empty($column_exists)) {
            $result = $wpdb->query("ALTER TABLE {$feeds_table} ADD COLUMN initial_import_count int(11) DEFAULT 10 AFTER update_frequency");
            if ($result === false) {
                error_log('RSS Aggregator: Failed to add initial_import_count column: ' . $wpdb->last_error);
            }
        }

        // Update version
        update_option('rss_aggregator_db_version', '1.1.0');
    }

    // Migration to add user assignment columns
    if (version_compare($current_version, '1.2.0', '<')) {
        $feeds_table = RSS_AGGREGATOR_FEEDS_TABLE;

        // Check if columns exist and add them one by one
        $place_name_exists = $wpdb->get_results("SHOW COLUMNS FROM {$feeds_table} LIKE 'geodirectory_place_name'");
        if (empty($place_name_exists)) {
            $result = $wpdb->query("ALTER TABLE {$feeds_table} ADD COLUMN geodirectory_place_name varchar(255) DEFAULT NULL AFTER geodirectory_place_id");
            if ($result === false) {
                error_log('RSS Aggregator: Failed to add geodirectory_place_name column: ' . $wpdb->last_error);
            }
        }

        $user_id_exists = $wpdb->get_results("SHOW COLUMNS FROM {$feeds_table} LIKE 'assigned_user_id'");
        if (empty($user_id_exists)) {
            $result = $wpdb->query("ALTER TABLE {$feeds_table} ADD COLUMN assigned_user_id bigint(20) DEFAULT NULL AFTER geodirectory_place_name");
            if ($result === false) {
                error_log('RSS Aggregator: Failed to add assigned_user_id column: ' . $wpdb->last_error);
            }
        }

        $user_name_exists = $wpdb->get_results("SHOW COLUMNS FROM {$feeds_table} LIKE 'assigned_user_name'");
        if (empty($user_name_exists)) {
            $result = $wpdb->query("ALTER TABLE {$feeds_table} ADD COLUMN assigned_user_name varchar(255) DEFAULT NULL AFTER assigned_user_id");
            if ($result === false) {
                error_log('RSS Aggregator: Failed to add assigned_user_name column: ' . $wpdb->last_error);
            }
        }

        // Add index for user assignments (check if exists first)
        $index_exists = $wpdb->get_results("SHOW INDEX FROM {$feeds_table} WHERE Key_name = 'idx_assigned_user'");
        if (empty($index_exists)) {
            $result = $wpdb->query("ALTER TABLE {$feeds_table} ADD INDEX idx_assigned_user (assigned_user_id)");
            if ($result === false) {
                error_log('RSS Aggregator: Failed to add idx_assigned_user index: ' . $wpdb->last_error);
            }
        }

        // Update version
        update_option('rss_aggregator_db_version', '1.2.0');
    }

    // Migration to add region column
    if (version_compare($current_version, '1.3.0', '<')) {
        $feeds_table = RSS_AGGREGATOR_FEEDS_TABLE;

        // Check if region column exists and add it if missing
        $region_exists = $wpdb->get_results("SHOW COLUMNS FROM {$feeds_table} LIKE 'region'");
        if (empty($region_exists)) {
            $result = $wpdb->query("ALTER TABLE {$feeds_table} ADD COLUMN region varchar(255) DEFAULT NULL AFTER county");
            if ($result === false) {
                error_log('RSS Aggregator: Failed to add region column: ' . $wpdb->last_error);
            }
        }

        // Update version
        update_option('rss_aggregator_db_version', '1.3.0');
    }
}

// Initialize plugin
RSS_Aggregator_Plugin::get_instance();
