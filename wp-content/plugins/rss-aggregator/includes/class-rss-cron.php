<?php
/**
 * RSS Cron handler class
 *
 * @package RSS_Aggregator
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * RSS Cron class
 */
class RSS_Cron {
    
    /**
     * Database handler
     */
    private $database;
    
    /**
     * Available update frequencies
     */
    private $frequencies = array(
        'every_15_minutes' => 900,   // 15 minutes
        'every_30_minutes' => 1800,  // 30 minutes
        'hourly' => 3600,            // 1 hour
        'every_2_hours' => 7200,     // 2 hours
        'every_6_hours' => 21600,    // 6 hours
        'daily' => 86400             // 24 hours
    );
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->database = new RSS_Database();
        $this->init();
    }
    
    /**
     * Initialize cron functionality
     */
    private function init() {
        // Add custom cron schedules
        add_filter('cron_schedules', array($this, 'add_custom_cron_schedules'));

        // Hook cron actions
        add_action('rss_aggregator_check_feeds', array($this, 'check_feeds_cron'));
        add_action('rss_aggregator_cleanup', array($this, 'cleanup_old_items'));

        // Hook individual feed updates - register dynamic hooks
        add_action('init', array($this, 'register_feed_hooks'));

        // Schedule cleanup if not already scheduled
        if (!wp_next_scheduled('rss_aggregator_cleanup')) {
            wp_schedule_event(time(), 'daily', 'rss_aggregator_cleanup');
        }
    }
    
    /**
     * Add custom cron schedules
     *
     * @param array $schedules Existing schedules
     * @return array Modified schedules
     */
    public function add_custom_cron_schedules($schedules) {
        $schedules['every_15_minutes'] = array(
            'interval' => 900,
            'display' => __('Every 15 minutes', 'rss-aggregator')
        );
        
        $schedules['every_30_minutes'] = array(
            'interval' => 1800,
            'display' => __('Every 30 minutes', 'rss-aggregator')
        );
        
        $schedules['every_2_hours'] = array(
            'interval' => 7200,
            'display' => __('Every 2 hours', 'rss-aggregator')
        );
        
        $schedules['every_6_hours'] = array(
            'interval' => 21600,
            'display' => __('Every 6 hours', 'rss-aggregator')
        );
        
        return $schedules;
    }

    /**
     * Register hooks for individual feed updates
     */
    public function register_feed_hooks() {
        // Get all feeds and register their hooks
        $feeds = $this->database->get_feeds();

        foreach ($feeds as $feed) {
            $hook_name = 'rss_aggregator_update_feed_' . $feed->id;

            // Register hook for this feed if not already registered
            if (!has_action($hook_name)) {
                add_action($hook_name, array($this, 'process_single_feed_hook'), 10, 1);
            }
        }
    }

    /**
     * Process single feed from cron hook
     *
     * @param int $feed_id Feed ID
     */
    public function process_single_feed_hook($feed_id) {
        $this->process_single_feed($feed_id);
    }

    /**
     * Main cron job to check feeds
     */
    public function check_feeds_cron() {
        // Get feeds that are due for update
        $feeds = $this->database->get_feeds_due_for_update();
        
        if (empty($feeds)) {
            return;
        }
        
        // Process each feed
        foreach ($feeds as $feed) {
            $this->process_single_feed($feed->id);
            
            // Add small delay to prevent server overload
            usleep(500000); // 0.5 seconds
        }
        
        // Log cron execution
        error_log('RSS Aggregator: Cron processed ' . count($feeds) . ' feeds');
    }
    
    /**
     * Process single feed in cron
     *
     * @param int $feed_id Feed ID
     */
    private function process_single_feed($feed_id) {
        try {
            // Get main aggregator instance
            $aggregator = RSS_Aggregator::get_instance();
            
            if ($aggregator) {
                $result = $aggregator->update_single_feed($feed_id);
                
                if ($result !== false) {
                    error_log("RSS Aggregator Cron: Processed {$result} items from feed {$feed_id}");
                } else {
                    error_log("RSS Aggregator Cron: Failed to process feed {$feed_id}");
                }
            }
        } catch (Exception $e) {
            error_log("RSS Aggregator Cron: Exception processing feed {$feed_id}: " . $e->getMessage());
        }
    }
    
    /**
     * Schedule individual feed update
     *
     * @param int $feed_id Feed ID
     * @param string $frequency Update frequency
     */
    public function schedule_feed_update($feed_id, $frequency = 'hourly') {
        $hook_name = 'rss_aggregator_update_feed_' . $feed_id;

        // Clear existing schedule
        $this->unschedule_feed_update($feed_id);

        // Validate frequency
        if (!array_key_exists($frequency, $this->frequencies)) {
            $frequency = 'hourly';
        }

        // Schedule new event with feed_id as argument
        if (!wp_next_scheduled($hook_name, array($feed_id))) {
            wp_schedule_event(time(), $frequency, $hook_name, array($feed_id));
        }
    }
    
    /**
     * Unschedule individual feed update
     *
     * @param int $feed_id Feed ID
     */
    public function unschedule_feed_update($feed_id) {
        $hook_name = 'rss_aggregator_update_feed_' . $feed_id;
        wp_clear_scheduled_hook($hook_name, array($feed_id));
    }
    
    /**
     * Reschedule all feeds based on their frequency settings
     */
    public function reschedule_all_feeds() {
        $feeds = $this->database->get_feeds(array('status' => 'active'));
        
        foreach ($feeds as $feed) {
            $this->schedule_feed_update($feed->id, $feed->update_frequency);
        }
    }
    
    /**
     * Get next scheduled time for feed
     *
     * @param int $feed_id Feed ID
     * @return int|false Next scheduled timestamp or false if not scheduled
     */
    public function get_next_feed_update($feed_id) {
        $hook_name = 'rss_aggregator_update_feed_' . $feed_id;
        return wp_next_scheduled($hook_name, array($feed_id));
    }
    
    /**
     * Force update all feeds now
     */
    public function force_update_all_feeds() {
        $feeds = $this->database->get_feeds(array('status' => 'active'));
        
        foreach ($feeds as $feed) {
            $this->process_single_feed($feed->id);
        }
    }
    
    /**
     * Cleanup old items
     */
    public function cleanup_old_items() {
        $days_to_keep = get_option('rss_aggregator_cleanup_days', 30);
        $deleted_count = $this->database->clean_old_items($days_to_keep);
        
        if ($deleted_count > 0) {
            error_log("RSS Aggregator: Cleaned up {$deleted_count} old items");
        }
    }
    
    /**
     * Get cron status information
     *
     * @return array Cron status
     */
    public function get_cron_status() {
        $status = array(
            'wp_cron_enabled' => !defined('DISABLE_WP_CRON') || !DISABLE_WP_CRON,
            'main_cron_scheduled' => wp_next_scheduled('rss_aggregator_check_feeds'),
            'cleanup_cron_scheduled' => wp_next_scheduled('rss_aggregator_cleanup'),
            'individual_feeds' => array()
        );
        
        // Check individual feed schedules
        $feeds = $this->database->get_feeds(array('status' => 'active'));
        foreach ($feeds as $feed) {
            $hook_name = 'rss_aggregator_update_feed_' . $feed->id;
            $next_run = wp_next_scheduled($hook_name);
            
            $status['individual_feeds'][] = array(
                'feed_id' => $feed->id,
                'feed_name' => $feed->name,
                'frequency' => $feed->update_frequency,
                'next_run' => $next_run,
                'next_run_formatted' => $next_run ? date('Y-m-d H:i:s', $next_run) : 'Not scheduled'
            );
        }
        
        return $status;
    }
    
    /**
     * Get available frequencies
     *
     * @return array Frequencies with labels
     */
    public function get_available_frequencies() {
        return array(
            'every_15_minutes' => __('Every 15 minutes', 'rss-aggregator'),
            'every_30_minutes' => __('Every 30 minutes', 'rss-aggregator'),
            'hourly' => __('Every hour', 'rss-aggregator'),
            'every_2_hours' => __('Every 2 hours', 'rss-aggregator'),
            'every_6_hours' => __('Every 6 hours', 'rss-aggregator'),
            'daily' => __('Once daily', 'rss-aggregator')
        );
    }
    
    /**
     * Check if WP-Cron is working properly
     *
     * @return bool Is WP-Cron working
     */
    public function is_wp_cron_working() {
        // Check if WP-Cron is disabled
        if (defined('DISABLE_WP_CRON') && DISABLE_WP_CRON) {
            return false;
        }
        
        // Test by scheduling a temporary event
        $test_hook = 'rss_aggregator_cron_test_' . time();
        wp_schedule_single_event(time() + 60, $test_hook);
        
        $scheduled = wp_next_scheduled($test_hook);
        
        // Clean up test event
        wp_clear_scheduled_hook($test_hook);
        
        return $scheduled !== false;
    }
    
    /**
     * Get cron execution log
     *
     * @param int $limit Number of log entries to return
     * @return array Log entries
     */
    public function get_cron_log($limit = 50) {
        // This would require a custom logging table
        // For now, return empty array
        return array();
    }
    
    /**
     * Manual cron trigger for testing
     */
    public function trigger_manual_cron() {
        if (!current_user_can('manage_options')) {
            return false;
        }
        
        // Trigger the main cron job
        do_action('rss_aggregator_check_feeds');
        
        return true;
    }
    
    /**
     * Get time until next cron run
     *
     * @return array Time information
     */
    public function get_time_until_next_run() {
        $next_run = wp_next_scheduled('rss_aggregator_check_feeds');
        
        if (!$next_run) {
            return array(
                'scheduled' => false,
                'message' => __('No cron job scheduled', 'rss-aggregator')
            );
        }
        
        $time_diff = $next_run - time();
        
        if ($time_diff <= 0) {
            return array(
                'scheduled' => true,
                'overdue' => true,
                'message' => __('Cron job is overdue', 'rss-aggregator')
            );
        }
        
        $hours = floor($time_diff / 3600);
        $minutes = floor(($time_diff % 3600) / 60);
        $seconds = $time_diff % 60;
        
        $time_string = '';
        if ($hours > 0) {
            $time_string .= $hours . 'h ';
        }
        if ($minutes > 0) {
            $time_string .= $minutes . 'm ';
        }
        $time_string .= $seconds . 's';
        
        return array(
            'scheduled' => true,
            'overdue' => false,
            'seconds' => $time_diff,
            'formatted' => trim($time_string),
            'next_run' => date('Y-m-d H:i:s', $next_run),
            'message' => sprintf(__('Next run in %s', 'rss-aggregator'), trim($time_string))
        );
    }
    
    /**
     * Reset all cron schedules
     */
    public function reset_all_schedules() {
        // Clear main cron
        wp_clear_scheduled_hook('rss_aggregator_check_feeds');
        
        // Clear cleanup cron
        wp_clear_scheduled_hook('rss_aggregator_cleanup');
        
        // Clear individual feed crons
        $feeds = $this->database->get_feeds();
        foreach ($feeds as $feed) {
            $this->unschedule_feed_update($feed->id);
        }
        
        // Reschedule main cron
        if (!wp_next_scheduled('rss_aggregator_check_feeds')) {
            wp_schedule_event(time(), 'hourly', 'rss_aggregator_check_feeds');
        }
        
        // Reschedule cleanup
        if (!wp_next_scheduled('rss_aggregator_cleanup')) {
            wp_schedule_event(time(), 'daily', 'rss_aggregator_cleanup');
        }
        
        // Reschedule individual feeds
        $this->reschedule_all_feeds();
    }
}
