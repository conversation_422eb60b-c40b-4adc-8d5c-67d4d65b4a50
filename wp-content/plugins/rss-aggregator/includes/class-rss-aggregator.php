<?php
/**
 * Main RSS Aggregator class
 *
 * @package RSS_Aggregator
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Main RSS Aggregator class
 */
class RSS_Aggregator {
    
    /**
     * Plugin instance
     */
    private static $instance = null;
    
    /**
     * Database handler
     */
    private $database;
    
    /**
     * RSS fetcher
     */
    private $fetcher;
    
    /**
     * RSS parser
     */
    private $parser;

    /**
     * Thumbnail extractor
     */
    private $thumbnail_extractor;

    /**
     * Cron handler
     */
    private $cron;
    
    /**
     * Integrations handler
     */
    private $integrations;
    
    /**
     * Get plugin instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * Initialize the plugin
     */
    private function init() {
        // Initialize components
        $this->database = new RSS_Database();
        $this->fetcher = new RSS_Fetcher();
        $this->parser = new RSS_Parser();
        $this->thumbnail_extractor = new RSS_Thumbnail_Extractor();
        $this->cron = new RSS_Cron();
        $this->integrations = new RSS_Integrations();
        
        // Setup hooks
        $this->setup_hooks();
    }
    
    /**
     * Setup WordPress hooks
     */
    private function setup_hooks() {
        // Cron hooks
        add_action('rss_aggregator_check_feeds', array($this, 'check_all_feeds'));
        add_action('rss_aggregator_update_feed', array($this, 'update_single_feed'), 10, 1);
        
        // AJAX hooks for admin
        add_action('wp_ajax_rss_aggregator_test_feed', array($this, 'ajax_test_feed'));
        add_action('wp_ajax_rss_aggregator_force_update', array($this, 'ajax_force_update'));
        add_action('wp_ajax_rss_aggregator_delete_feed_post', array($this, 'ajax_delete_feed_post'));
        
        // Post hooks for integration
        add_action('transition_post_status', array($this, 'handle_post_status_change'), 10, 3);
        
        // Admin hooks
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        
        // Public hooks
        add_action('wp_enqueue_scripts', array($this, 'enqueue_public_scripts'));
        
        // Shortcode
        add_shortcode('rss_aggregator_feed', array($this, 'shortcode_feed_display'));

        // Filter to display place names correctly
        add_filter('the_author', array($this, 'filter_place_author_display'));
        add_filter('get_the_author', array($this, 'filter_place_author_display'));
        add_filter('author_link', array($this, 'filter_place_author_link'), 10, 3);
    }

    /**
     * Check all active feeds for updates
     */
    public function check_all_feeds() {
        $feeds = $this->database->get_feeds_due_for_update();
        
        foreach ($feeds as $feed) {
            $this->update_single_feed($feed->id);
        }
    }
    
    /**
     * Update a single feed
     *
     * @param int $feed_id Feed ID
     * @param bool $is_initial_import Whether this is the first import for this feed
     */
    public function update_single_feed($feed_id, $is_initial_import = null) {
        $feed = $this->database->get_feed($feed_id);

        if (!$feed || $feed->status !== 'active') {
            return false;
        }

        // Determine if this is initial import
        if ($is_initial_import === null) {
            $is_initial_import = empty($feed->last_updated);
        }

        try {
            // Fetch RSS content
            $rss_content = $this->fetcher->fetch_feed($feed->url);

            if (!$rss_content) {
                error_log("RSS Aggregator: Failed to fetch feed {$feed->url}");
                return false;
            }

            // Parse RSS content
            $items = $this->parser->parse_feed($rss_content, $feed);

            if (empty($items)) {
                error_log("RSS Aggregator: No items found in feed {$feed->url}");
                return false;
            }

            // Sort items by publication date (newest first)
            $items = $this->sort_items_by_date($items);

            // Limit items for initial import
            if ($is_initial_import && !empty($feed->initial_import_count)) {
                $total_items = count($items);
                $items = array_slice($items, 0, intval($feed->initial_import_count));
                error_log("RSS Aggregator: Initial import - found {$total_items} items, limited to {$feed->initial_import_count} items for feed {$feed->url}");
            }

            // Process each item
            $processed_count = 0;
            $skipped_count = 0;
            foreach ($items as $index => $item) {
                if ($this->process_feed_item($item, $feed)) {
                    $processed_count++;
                    error_log("RSS Aggregator: Processed item " . ($index + 1) . ": " . ($item['title'] ?? 'No title'));
                } else {
                    $skipped_count++;
                    error_log("RSS Aggregator: Skipped item " . ($index + 1) . ": " . ($item['title'] ?? 'No title') . " (already exists or failed)");
                }
            }

            // Update feed last_updated timestamp
            $this->database->update_feed_timestamp($feed_id);

            // Log success
            $import_type = $is_initial_import ? 'Initial import' : 'Update';
            error_log("RSS Aggregator: {$import_type} processed {$processed_count} items, skipped {$skipped_count} items from feed {$feed->url}");

            return $processed_count;

        } catch (Exception $e) {
            error_log("RSS Aggregator: Error updating feed {$feed->url}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Process a single feed item
     *
     * @param array $item Feed item data
     * @param object $feed Feed object
     * @return bool Success status
     */
    private function process_feed_item($item, $feed) {
        // Check if item already exists
        if ($this->database->item_exists($feed->id, $item['guid'])) {
            return false;
        }
        
        // Save item to database
        $item_id = $this->database->save_item($item, $feed->id);
        
        if (!$item_id) {
            return false;
        }
        
        // Create WordPress post
        $post_id = $this->create_wordpress_post($item, $feed);
        
        if ($post_id) {
            // Update item with post ID
            $this->database->update_item_post_id($item_id, $post_id);
            
            // Handle integrations
            $this->integrations->handle_new_post($post_id, $feed, $item);
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Create WordPress post from feed item
     *
     * @param array $item Feed item data
     * @param object $feed Feed object
     * @return int|false Post ID on success, false on failure
     */
    private function create_wordpress_post($item, $feed) {
        // Determine post author based on priority:
        // 1. GeoDirectory place (if assigned)
        // 2. Assigned user (if specified)
        // 3. Default author from settings
        $post_author = $this->determine_post_author($feed);

        error_log("RSS Aggregator: Determined post author {$post_author} for feed {$feed->id} (place: {$feed->geodirectory_place_id}, user: {$feed->assigned_user_id})");

        $post_data = array(
            'post_title' => sanitize_text_field($item['title']),
            'post_content' => wp_kses_post($item['description']),
            'post_status' => get_option('rss_aggregator_post_status', 'publish'),
            'post_author' => $post_author,
            'post_type' => 'post',
            'post_date' => $item['pub_date'] ? date('Y-m-d H:i:s', strtotime($item['pub_date'])) : current_time('mysql'),
            'meta_input' => array(
                '_rss_aggregator_feed_id' => $feed->id,
                '_rss_aggregator_source_url' => esc_url_raw($item['url']),
                '_rss_aggregator_guid' => sanitize_text_field($item['guid']),
                '_rss_aggregator_county' => sanitize_text_field($feed->county),
                '_rss_aggregator_region' => sanitize_text_field($feed->region),
                '_rss_aggregator_place_id' => intval($feed->geodirectory_place_id),
                '_rss_aggregator_place_name' => sanitize_text_field($feed->geodirectory_place_name),
                '_rss_aggregator_assigned_user_id' => intval($feed->assigned_user_id),
                '_rss_aggregator_assigned_user_name' => sanitize_text_field($feed->assigned_user_name)
            )
        );
        
        // Insert post
        $post_id = wp_insert_post($post_data);
        
        if (is_wp_error($post_id)) {
            error_log("RSS Aggregator: Failed to create post: " . $post_id->get_error_message());
            return false;
        }
        
        // Handle thumbnail - try to extract if not present
        $thumbnail_url = $this->thumbnail_extractor->extract_thumbnail($item);
        error_log("RSS Aggregator: Extracted thumbnail URL for post {$post_id}: " . ($thumbnail_url ?: 'none'));

        if ($thumbnail_url) {
            $result = $this->handle_post_thumbnail($post_id, $thumbnail_url);
            error_log("RSS Aggregator: Thumbnail handling result for post {$post_id}: " . ($result ? 'success' : 'failed'));
        }
        
        // Add categories based on county
        if (!empty($feed->county)) {
            $this->assign_post_category($post_id, $feed->county);
        }
        
        return $post_id;
    }

    /**
     * Filter author display for place users
     *
     * @param string $author_name Current author name
     * @return string Filtered author name
     */
    public function filter_place_author_display($author_name) {
        global $post;

        // Only filter for RSS aggregated posts
        if (!$post || !get_post_meta($post->ID, '_rss_aggregator_feed_id', true)) {
            return $author_name;
        }

        $author_id = $post->post_author;
        $is_place_user = get_user_meta($author_id, '_rss_aggregator_is_place_user', true);

        if ($is_place_user) {
            $user = get_user_by('id', $author_id);
            if ($user && !empty($user->display_name)) {
                return $user->display_name;
            }
        }

        return $author_name;
    }

    /**
     * Filter author link for place users - link to place instead of user
     *
     * @param string $link Author link
     * @param int $author_id Author ID
     * @param string $author_nicename Author nicename
     * @return string Filtered author link
     */
    public function filter_place_author_link($link, $author_id, $author_nicename) {
        $is_place_user = get_user_meta($author_id, '_rss_aggregator_is_place_user', true);

        if ($is_place_user) {
            $place_id = get_user_meta($author_id, '_rss_aggregator_place_id', true);
            if ($place_id) {
                // Link to the GeoDirectory place instead of user profile
                $place_link = get_permalink($place_id);
                if ($place_link) {
                    return $place_link;
                }
            }
        }

        return $link;
    }

    /**
     * Determine post author based on feed settings
     *
     * @param object $feed Feed object
     * @return int User ID for post author
     */
    private function determine_post_author($feed) {
        error_log("RSS Aggregator: === DETERMINING AUTHOR FOR FEED {$feed->id} ===");
        error_log("RSS Aggregator: Feed place_id: " . ($feed->geodirectory_place_id ?? 'none'));
        error_log("RSS Aggregator: Feed assigned_user_id: " . ($feed->assigned_user_id ?? 'none'));

        // Priority 1: If place is assigned in RSS feed form, use place as publisher
        if (!empty($feed->geodirectory_place_id)) {
            error_log("RSS Aggregator: PRIORITY 1: Place assigned in RSS form, using place name as publisher");
            $place_author = $this->get_place_as_author($feed->geodirectory_place_id, $feed->geodirectory_place_name);
            error_log("RSS Aggregator: Place author: " . ($place_author ?: 'not found'));

            if ($place_author) {
                error_log("RSS Aggregator: ✅ USING PLACE NAME AS PUBLISHER: {$place_author}");
                return $place_author;
            }
            error_log("RSS Aggregator: ❌ Place author not found, moving to priority 2");
        } else {
            error_log("RSS Aggregator: PRIORITY 1: No place assigned, skipping to priority 2");
        }

        // Priority 2: If no place but user is assigned, use assigned user as publisher
        if (!empty($feed->assigned_user_id)) {
            error_log("RSS Aggregator: PRIORITY 2: User assigned, checking user {$feed->assigned_user_id}");
            $user = get_user_by('id', $feed->assigned_user_id);
            error_log("RSS Aggregator: User lookup: " . ($user ? "Found: {$user->display_name}" : 'Not found'));

            if ($user) {
                error_log("RSS Aggregator: ✅ USING ASSIGNED USER AS PUBLISHER: {$feed->assigned_user_id}");
                return $feed->assigned_user_id;
            }
            error_log("RSS Aggregator: ❌ Assigned user not found, moving to priority 3");
        } else {
            error_log("RSS Aggregator: PRIORITY 2: No user assigned, skipping to priority 3");
        }

        // Priority 3: Nothing assigned, use default author from settings
        $default_author = get_option('rss_aggregator_post_author', 1);
        error_log("RSS Aggregator: PRIORITY 3: Nothing assigned, using default author: {$default_author}");

        $user = get_user_by('id', $default_author);
        if ($user) {
            error_log("RSS Aggregator: ✅ USING DEFAULT AUTHOR AS PUBLISHER: {$default_author}");
            return $default_author;
        }

        // Fallback: First admin user
        $admin_users = get_users(array('role' => 'administrator', 'number' => 1));
        if (!empty($admin_users)) {
            error_log("RSS Aggregator: ✅ FALLBACK: USING FIRST ADMIN: {$admin_users[0]->ID}");
            return $admin_users[0]->ID;
        }

        // Last resort: User ID 1
        error_log("RSS Aggregator: ✅ LAST RESORT: USING USER ID 1");
        return 1;
    }

    /**
     * Get owner of GeoDirectory place
     * Priority: assigned user from feed settings, not the post author
     *
     * @param int $place_id GeoDirectory place ID
     * @return int|false User ID or false if not found
     */
    private function get_place_owner($place_id) {
        error_log("RSS Aggregator: get_place_owner called with place_id: {$place_id}");

        // Get the place post to find its owner
        $place_post = get_post($place_id);

        if (!$place_post) {
            error_log("RSS Aggregator: Place post {$place_id} not found");
            return false;
        }

        error_log("RSS Aggregator: Place post found - ID: {$place_post->ID}, Type: {$place_post->post_type}, Owner: {$place_post->post_author}, Status: {$place_post->post_status}");

        // The place owner is the post author of the GeoDirectory place
        if (!empty($place_post->post_author)) {
            $owner_user = get_user_by('id', $place_post->post_author);
            error_log("RSS Aggregator: Place owner {$place_post->post_author} " . ($owner_user ? "found: {$owner_user->display_name}" : "not found"));

            if ($owner_user) {
                return $place_post->post_author;
            }
        }

        error_log("RSS Aggregator: No valid place owner found");
        return false;
    }

    /**
     * Get place as author - creates or finds user with place name
     *
     * @param int $place_id GeoDirectory place ID
     * @param string $place_name Place name from feed
     * @return int|false User ID or false if not found
     */
    private function get_place_as_author($place_id, $place_name) {

        // Try to find existing user with this place name as display name
        $existing_users = get_users(array(
            'meta_key' => '_rss_aggregator_place_id',
            'meta_value' => $place_id,
            'number' => 1
        ));

        if (!empty($existing_users)) {
            return $existing_users[0]->ID;
        }

        // Create a virtual user for this place
        $place_username = 'place_' . $place_id;
        $place_email = 'place_' . $place_id . '@geodirectory.local';
        $display_name = $place_name ?: 'Place ' . $place_id;

        // Check if user already exists by username
        $existing_user = get_user_by('login', $place_username);
        if ($existing_user) {
            // Update display name if it changed
            if ($existing_user->display_name !== $display_name) {
                wp_update_user(array(
                    'ID' => $existing_user->ID,
                    'display_name' => $display_name
                ));
            }
            return $existing_user->ID;
        }

        // Create new user for this place
        $user_data = array(
            'user_login' => $place_username,
            'user_email' => $place_email,
            'display_name' => $display_name,
            'first_name' => $display_name,
            'last_name' => '',
            'user_pass' => wp_generate_password(),
            'role' => 'contributor'
        );

        $user_id = wp_insert_user($user_data);

        if (is_wp_error($user_id)) {
            return false;
        }

        // Store place ID in user meta
        update_user_meta($user_id, '_rss_aggregator_place_id', $place_id);
        update_user_meta($user_id, '_rss_aggregator_is_place_user', true);

        return $user_id;
    }

    /**
     * Handle post thumbnail from URL
     *
     * @param int $post_id Post ID
     * @param string $thumbnail_url Thumbnail URL
     */
    private function handle_post_thumbnail($post_id, $thumbnail_url) {
        if (!get_option('rss_aggregator_enable_thumbnails', 1)) {
            error_log("RSS Aggregator: Thumbnails disabled in settings");
            return false;
        }

        error_log("RSS Aggregator: Downloading thumbnail from: {$thumbnail_url}");

        // Download and attach thumbnail
        $attachment_id = $this->download_and_attach_image($thumbnail_url, $post_id);

        if ($attachment_id) {
            set_post_thumbnail($post_id, $attachment_id);
            error_log("RSS Aggregator: Successfully set thumbnail {$attachment_id} for post {$post_id}");
            return true;
        } else {
            error_log("RSS Aggregator: Failed to download/attach thumbnail for post {$post_id}");
            return false;
        }
    }
    
    /**
     * Download and attach image to post
     *
     * @param string $image_url Image URL
     * @param int $post_id Post ID
     * @return int|false Attachment ID on success, false on failure
     */
    private function download_and_attach_image($image_url, $post_id) {
        require_once(ABSPATH . 'wp-admin/includes/media.php');
        require_once(ABSPATH . 'wp-admin/includes/file.php');
        require_once(ABSPATH . 'wp-admin/includes/image.php');
        
        $tmp = download_url($image_url);
        
        if (is_wp_error($tmp)) {
            return false;
        }
        
        $file_array = array(
            'name' => basename($image_url),
            'tmp_name' => $tmp
        );
        
        $attachment_id = media_handle_sideload($file_array, $post_id);
        
        if (is_wp_error($attachment_id)) {
            @unlink($tmp);
            return false;
        }
        
        return $attachment_id;
    }
    
    /**
     * Assign post category based on county
     *
     * @param int $post_id Post ID
     * @param string $county County name
     */
    private function assign_post_category($post_id, $county) {
        $category_name = 'Powiat ' . $county;
        
        // Get or create category
        $category = get_term_by('name', $category_name, 'category');
        
        if (!$category) {
            $category_id = wp_create_category($category_name);
        } else {
            $category_id = $category->term_id;
        }
        
        if ($category_id) {
            wp_set_post_categories($post_id, array($category_id), true);
        }
    }
    
    /**
     * Handle post status changes
     *
     * @param string $new_status New post status
     * @param string $old_status Old post status
     * @param WP_Post $post Post object
     */
    public function handle_post_status_change($new_status, $old_status, $post) {
        // Handle RSS aggregated posts
        if (get_post_meta($post->ID, '_rss_aggregator_feed_id', true)) {
            $this->integrations->handle_buddyboss_activity($new_status, $old_status, $post);
        }
    }
    
    /**
     * AJAX handler for testing feed
     */
    public function ajax_test_feed() {
        check_ajax_referer('rss_aggregator_admin', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'rss-aggregator'));
        }
        
        $feed_url = sanitize_url($_POST['feed_url']);
        
        if (empty($feed_url)) {
            wp_send_json_error(__('Feed URL is required', 'rss-aggregator'));
        }
        
        $result = $this->fetcher->test_feed($feed_url);
        
        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['message']);
        }
    }
    
    /**
     * AJAX handler for forcing feed update
     */
    public function ajax_force_update() {
        check_ajax_referer('rss_aggregator_admin', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'rss-aggregator'));
        }
        
        $feed_id = intval($_POST['feed_id']);
        
        if (empty($feed_id)) {
            wp_send_json_error(__('Feed ID is required', 'rss-aggregator'));
        }
        
        $result = $this->update_single_feed($feed_id);
        
        if ($result !== false) {
            wp_send_json_success(array(
                'message' => sprintf(__('Processed %d items', 'rss-aggregator'), $result)
            ));
        } else {
            wp_send_json_error(__('Failed to update feed', 'rss-aggregator'));
        }
    }

    /**
     * AJAX handler for deleting feed post
     */
    public function ajax_delete_feed_post() {
        try {
            check_ajax_referer('rss_aggregator_admin', 'nonce');
        } catch (Exception $e) {
            wp_send_json_error(__('Security check failed', 'rss-aggregator'));
            return;
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'rss-aggregator'));
            return;
        }

        $item_id = intval($_POST['item_id'] ?? 0);

        if (empty($item_id)) {
            wp_send_json_error(__('Invalid item ID', 'rss-aggregator'));
            return;
        }

        $result = $this->database->delete_feed_post($item_id);

        if ($result) {
            wp_send_json_success(__('Post deleted successfully', 'rss-aggregator'));
        } else {
            wp_send_json_error(__('Failed to delete post', 'rss-aggregator'));
        }
    }

    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'rss-aggregator') === false) {
            return;
        }
        
        wp_enqueue_script(
            'rss-aggregator-admin',
            RSS_AGGREGATOR_PLUGIN_URL . 'admin/js/admin.js',
            array('jquery'),
            RSS_AGGREGATOR_VERSION,
            true
        );
        
        wp_enqueue_style(
            'rss-aggregator-admin',
            RSS_AGGREGATOR_PLUGIN_URL . 'admin/css/admin.css',
            array(),
            RSS_AGGREGATOR_VERSION
        );
        
        wp_localize_script('rss-aggregator-admin', 'rssAggregator', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('rss_aggregator_admin'),
            'strings' => array(
                'testing' => __('Testing...', 'rss-aggregator'),
                'updating' => __('Updating...', 'rss-aggregator'),
                'success' => __('Success', 'rss-aggregator'),
                'error' => __('Error', 'rss-aggregator')
            )
        ));
    }
    
    /**
     * Enqueue public scripts
     */
    public function enqueue_public_scripts() {
        wp_enqueue_style(
            'rss-aggregator-public',
            RSS_AGGREGATOR_PLUGIN_URL . 'public/css/public.css',
            array(),
            RSS_AGGREGATOR_VERSION
        );
    }
    
    /**
     * Shortcode for displaying feed
     *
     * @param array $atts Shortcode attributes
     * @return string HTML output
     */
    public function shortcode_feed_display($atts) {
        $atts = shortcode_atts(array(
            'feed_id' => '',
            'county' => '',
            'limit' => 10,
            'show_thumbnail' => 'yes',
            'show_excerpt' => 'yes'
        ), $atts);
        
        // This will be handled by RSS_Public class
        if (class_exists('RSS_Public')) {
            return RSS_Public::get_instance()->display_feed($atts);
        }
        
        return '';
    }
    
    /**
     * Get database handler
     */
    public function get_database() {
        return $this->database;
    }
    
    /**
     * Get fetcher
     */
    public function get_fetcher() {
        return $this->fetcher;
    }
    
    /**
     * Get parser
     */
    public function get_parser() {
        return $this->parser;
    }
    
    /**
     * Get cron handler
     */
    public function get_cron() {
        return $this->cron;
    }
    
    /**
     * Get integrations handler
     */
    public function get_integrations() {
        return $this->integrations;
    }

    /**
     * Get thumbnail extractor
     */
    public function get_thumbnail_extractor() {
        return $this->thumbnail_extractor;
    }

    /**
     * Sort RSS items by publication date (newest first)
     *
     * @param array $items RSS items
     * @return array Sorted items
     */
    private function sort_items_by_date($items) {
        usort($items, function($a, $b) {
            $date_a = !empty($a['pub_date']) ? strtotime($a['pub_date']) : 0;
            $date_b = !empty($b['pub_date']) ? strtotime($b['pub_date']) : 0;

            // If dates are invalid, use current time
            if ($date_a === false) $date_a = time();
            if ($date_b === false) $date_b = time();

            // Sort newest first (descending)
            return $date_b - $date_a;
        });

        return $items;
    }
}
