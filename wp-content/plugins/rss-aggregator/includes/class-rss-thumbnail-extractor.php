<?php
/**
 * RSS Thumbnail Extractor Class
 *
 * Extracts thumbnails from RSS items and web pages
 */

if (!defined('ABSPATH')) {
    exit;
}

class RSS_Thumbnail_Extractor {
    
    /**
     * Extract thumbnail from RSS item or web page
     *
     * @param array $item RSS item data
     * @return string|null Thumbnail URL
     */
    public function extract_thumbnail($item) {
        // First try to get thumbnail from RSS item
        $thumbnail_url = $this->get_thumbnail_from_rss($item);

        if ($thumbnail_url) {
            return $thumbnail_url;
        }

        // If no thumbnail in RSS, try to extract from web page
        if (!empty($item['url'])) {
            $thumbnail_url = $this->get_thumbnail_from_webpage($item['url']);
        }

        return $thumbnail_url;
    }
    
    /**
     * Get thumbnail from RSS item data
     *
     * @param array $item RSS item data
     * @return string|null Thumbnail URL
     */
    private function get_thumbnail_from_rss($item) {
        // Check various RSS thumbnail fields
        $thumbnail_fields = array(
            'thumbnail_url',
            'image_url',
            'enclosure_url',
            'media_thumbnail',
            'media_content'
        );
        
        foreach ($thumbnail_fields as $field) {
            if (!empty($item[$field])) {
                $url = $item[$field];
                if ($this->is_valid_image_url($url)) {
                    return $url;
                }
            }
        }
        
        // Check content for images
        if (!empty($item['content']) || !empty($item['description'])) {
            $content = !empty($item['content']) ? $item['content'] : $item['description'];
            $image_url = $this->extract_first_image_from_content($content);
            if ($image_url) {
                return $image_url;
            }
        }
        
        return null;
    }
    
    /**
     * Get thumbnail from web page
     *
     * @param string $url Page URL
     * @return string|null Thumbnail URL
     */
    private function get_thumbnail_from_webpage($url) {
        // Get page content
        $html = $this->fetch_webpage_content($url);

        if (!$html) {
            return null;
        }

        // Try to extract thumbnail using various methods
        $thumbnail_url = $this->extract_og_image($html);

        if (!$thumbnail_url) {
            $thumbnail_url = $this->extract_twitter_image($html);
        }

        if (!$thumbnail_url) {
            $thumbnail_url = $this->extract_meta_image($html);
        }

        if (!$thumbnail_url) {
            $thumbnail_url = $this->extract_first_content_image($html);
        }

        if (!$thumbnail_url) {
            return null;
        }

        // Convert relative URLs to absolute
        if (!filter_var($thumbnail_url, FILTER_VALIDATE_URL)) {
            $thumbnail_url = $this->make_absolute_url($thumbnail_url, $url);
        }

        return $thumbnail_url;
    }
    
    /**
     * Fetch webpage content
     *
     * @param string $url Page URL
     * @return string|false Page HTML content
     */
    private function fetch_webpage_content($url) {
        // Use WordPress HTTP API
        $response = wp_remote_get($url, array(
            'timeout' => 15,
            'user-agent' => 'RSS Aggregator Bot/1.0',
            'headers' => array(
                'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            )
        ));
        
        if (is_wp_error($response)) {
            error_log('RSS Aggregator: Failed to fetch webpage: ' . $response->get_error_message());
            return false;
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        if ($status_code !== 200) {
            error_log('RSS Aggregator: Webpage returned status code: ' . $status_code);
            return false;
        }
        
        return wp_remote_retrieve_body($response);
    }
    
    /**
     * Extract Open Graph image
     *
     * @param string $html Page HTML
     * @return string|null Image URL
     */
    private function extract_og_image($html) {
        if (preg_match('/<meta[^>]+property=["\']og:image["\'][^>]+content=["\']([^"\']+)["\'][^>]*>/i', $html, $matches)) {
            return trim($matches[1]);
        }
        
        if (preg_match('/<meta[^>]+content=["\']([^"\']+)["\'][^>]+property=["\']og:image["\'][^>]*>/i', $html, $matches)) {
            return trim($matches[1]);
        }
        
        return null;
    }
    
    /**
     * Extract Twitter Card image
     *
     * @param string $html Page HTML
     * @return string|null Image URL
     */
    private function extract_twitter_image($html) {
        // Twitter Card image
        if (preg_match('/<meta[^>]+name=["\']twitter:image["\'][^>]+content=["\']([^"\']+)["\'][^>]*>/i', $html, $matches)) {
            return trim($matches[1]);
        }
        
        if (preg_match('/<meta[^>]+content=["\']([^"\']+)["\'][^>]+name=["\']twitter:image["\'][^>]*>/i', $html, $matches)) {
            return trim($matches[1]);
        }
        
        return null;
    }
    
    /**
     * Extract other meta images
     *
     * @param string $html Page HTML
     * @return string|null Image URL
     */
    private function extract_meta_image($html) {
        // Schema.org image
        if (preg_match('/<meta[^>]+itemprop=["\']image["\'][^>]+content=["\']([^"\']+)["\'][^>]*>/i', $html, $matches)) {
            return trim($matches[1]);
        }
        
        // Generic image meta
        if (preg_match('/<meta[^>]+name=["\']image["\'][^>]+content=["\']([^"\']+)["\'][^>]*>/i', $html, $matches)) {
            return trim($matches[1]);
        }
        
        return null;
    }
    
    /**
     * Extract first image from content
     *
     * @param string $html Page HTML
     * @return string|null Image URL
     */
    private function extract_first_content_image($html) {
        // Look for images in article content
        $content_selectors = array(
            'article img',
            '.content img',
            '.post-content img',
            '.entry-content img',
            'main img'
        );
        
        // Simple regex to find first img tag
        if (preg_match('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $html, $matches)) {
            $src = trim($matches[1]);
            
            // Skip common non-content images
            $skip_patterns = array(
                '/avatar/',
                '/logo/',
                '/icon/',
                '/button/',
                '/banner/',
                '/ad/',
                '/advertisement/',
                'data:image',
                '.gif',
                'tracking',
                'pixel'
            );
            
            foreach ($skip_patterns as $pattern) {
                if (stripos($src, $pattern) !== false) {
                    return null;
                }
            }
            
            if ($this->is_valid_image_url($src)) {
                return $src;
            }
        }
        
        return null;
    }
    
    /**
     * Extract first image from HTML content
     *
     * @param string $content HTML content
     * @return string|null Image URL
     */
    private function extract_first_image_from_content($content) {
        if (preg_match('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $content, $matches)) {
            $src = trim($matches[1]);
            if ($this->is_valid_image_url($src)) {
                return $src;
            }
        }
        
        return null;
    }
    
    /**
     * Check if URL is a valid image URL
     *
     * @param string $url URL to check
     * @return bool Is valid image URL
     */
    private function is_valid_image_url($url) {
        if (empty($url) || !is_string($url)) {
            return false;
        }
        
        // Check if it's a valid URL
        if (!filter_var($url, FILTER_VALIDATE_URL) && !preg_match('/^\//', $url)) {
            return false;
        }
        
        // Check file extension
        $image_extensions = array('jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp');
        $extension = strtolower(pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION));
        
        return in_array($extension, $image_extensions);
    }
    
    /**
     * Convert relative URL to absolute URL
     *
     * @param string $relative_url Relative URL
     * @param string $base_url Base URL
     * @return string Absolute URL
     */
    private function make_absolute_url($relative_url, $base_url) {
        if (filter_var($relative_url, FILTER_VALIDATE_URL)) {
            return $relative_url;
        }
        
        $parsed_base = parse_url($base_url);
        
        if (!$parsed_base) {
            return $relative_url;
        }
        
        $scheme = $parsed_base['scheme'] ?? 'https';
        $host = $parsed_base['host'] ?? '';
        
        if (strpos($relative_url, '//') === 0) {
            return $scheme . ':' . $relative_url;
        }
        
        if (strpos($relative_url, '/') === 0) {
            return $scheme . '://' . $host . $relative_url;
        }
        
        $path = $parsed_base['path'] ?? '/';
        $path = rtrim(dirname($path), '/') . '/';
        
        return $scheme . '://' . $host . $path . $relative_url;
    }
}
