<?php
/**
 * RSS Thumbnail Extractor Class
 *
 * Extracts thumbnails from RSS items and web pages
 */

if (!defined('ABSPATH')) {
    exit;
}

class RSS_Thumbnail_Extractor {

    /**
     * Constructor
     */
    public function __construct() {
        error_log("RSS Thumbnail Extractor: *** CLASS INSTANTIATED *** at " . date('Y-m-d H:i:s'));
        error_log("RSS Thumbnail Extractor: Class file: " . __FILE__);
    }

    /**
     * Extract thumbnail from RSS item or web page
     *
     * @param array $item RSS item data
     * @return string|null Thumbnail URL
     */
    public function extract_thumbnail($item) {
        error_log("RSS Thumbnail Extractor: *** EXTRACT_THUMBNAIL CALLED *** for item: " . (!empty($item['title']) ? $item['title'] : 'No title'));
        error_log("RSS Thumbnail Extractor: Class file: " . __FILE__);
        error_log("RSS Thumbnail Extractor: Method called at: " . date('Y-m-d H:i:s'));

        // First try to extract from web page using title-based search
        if (!empty($item['url']) && !empty($item['title'])) {
            error_log("RSS Thumbnail Extractor: Trying title-based webpage search first");
            $thumbnail_url = $this->get_thumbnail_from_webpage($item['url'], $item['title']);

            if ($thumbnail_url) {
                error_log("RSS Thumbnail Extractor: Found thumbnail on webpage: " . $thumbnail_url);
                return $thumbnail_url;
            }
        }

        // If no thumbnail found on webpage, try RSS item (but filter out banners)
        error_log("RSS Thumbnail Extractor: No thumbnail on webpage, trying RSS");
        $thumbnail_url = $this->get_thumbnail_from_rss($item);

        if ($thumbnail_url) {
            error_log("RSS Thumbnail Extractor: Found thumbnail in RSS: " . $thumbnail_url);
            return $thumbnail_url;
        }

        error_log("RSS Thumbnail Extractor: No thumbnail found anywhere for: " . (!empty($item['url']) ? $item['url'] : 'no URL'));
        return null;
    }

    /**
     * Get thumbnail from RSS item data
     *
     * @param array $item RSS item data
     * @return string|null Thumbnail URL
     */
    private function get_thumbnail_from_rss($item) {
        // Check various RSS thumbnail fields
        $thumbnail_fields = array(
            'thumbnail_url',
            'image_url',
            'enclosure_url',
            'media_thumbnail',
            'media_content'
        );

        foreach ($thumbnail_fields as $field) {
            if (!empty($item[$field])) {
                $url = $item[$field];
                error_log("RSS Thumbnail Extractor: Found {$field}: {$url}");
                if ($this->is_valid_image_url($url)) {
                    if ($this->should_skip_image($url)) {
                        error_log("RSS Thumbnail Extractor: Skipping {$field} (filtered): {$url}");
                        continue;
                    }
                    error_log("RSS Thumbnail Extractor: Using {$field}: {$url}");
                    return $url;
                }
            }
        }

        // Check content for images
        if (!empty($item['content']) || !empty($item['description'])) {
            $content = !empty($item['content']) ? $item['content'] : $item['description'];
            $image_url = $this->extract_first_image_from_content($content);
            if ($image_url) {
                return $image_url;
            }
        }

        // Advanced RSS content search if enabled
        if (get_option('rss_aggregator_enable_advanced_rss_search', 1)) {
            $image_url = $this->extract_images_from_rss_content($item);
            if ($image_url) {
                return $image_url;
            }
        }

        return null;
    }

    /**
     * Get thumbnail from web page
     *
     * @param string $url Page URL
     * @param string $title Optional article title for advanced search
     * @return string|null Thumbnail URL
     */
    private function get_thumbnail_from_webpage($url, $title = '') {
        error_log("RSS Thumbnail Extractor: Fetching webpage: {$url}");

        // Get page content
        $html = $this->fetch_webpage_content($url);

        if (!$html) {
            error_log("RSS Thumbnail Extractor: Failed to fetch webpage content");
            return null;
        }

        error_log("RSS Thumbnail Extractor: Fetched webpage, size: " . strlen($html) . " chars");

        // If we have a title, try title-based search first (most accurate)
        if (!empty($title) && get_option('rss_aggregator_enable_advanced_thumbnails', 1)) {
            error_log("RSS Thumbnail Extractor: Trying title-based search first for title: " . $title);
            $thumbnail_url = $this->extract_thumbnail_by_title($html, $title, $url);
            if ($thumbnail_url) {
                error_log("RSS Thumbnail Extractor: Found thumbnail via title search: " . $thumbnail_url);
                return $this->make_absolute_url_if_needed($thumbnail_url, $url);
            }

            // If title-based search failed, try searching for images in article containers
            error_log("RSS Thumbnail Extractor: Title-based search failed, trying article container search");
            $thumbnail_url = $this->extract_images_from_article_containers($html, $url);
            if ($thumbnail_url) {
                error_log("RSS Thumbnail Extractor: Found thumbnail in article container: " . $thumbnail_url);
                return $this->make_absolute_url_if_needed($thumbnail_url, $url);
            }
        }

        // Try standard meta tag methods
        error_log("RSS Thumbnail Extractor: Trying standard meta tag extraction");

        $thumbnail_url = $this->extract_og_image($html);
        if ($thumbnail_url) {
            error_log("RSS Thumbnail Extractor: Found OG image: " . $thumbnail_url);
            return $this->make_absolute_url_if_needed($thumbnail_url, $url);
        }

        $thumbnail_url = $this->extract_twitter_image($html);
        if ($thumbnail_url) {
            error_log("RSS Thumbnail Extractor: Found Twitter image: " . $thumbnail_url);
            return $this->make_absolute_url_if_needed($thumbnail_url, $url);
        }

        $thumbnail_url = $this->extract_meta_image($html);
        if ($thumbnail_url) {
            error_log("RSS Thumbnail Extractor: Found meta image: " . $thumbnail_url);
            return $this->make_absolute_url_if_needed($thumbnail_url, $url);
        }

        $thumbnail_url = $this->extract_first_content_image($html);
        if ($thumbnail_url) {
            error_log("RSS Thumbnail Extractor: Found first content image: " . $thumbnail_url);
            return $this->make_absolute_url_if_needed($thumbnail_url, $url);
        }

        error_log("RSS Thumbnail Extractor: No thumbnail found on webpage");
        return null;
    }

    /**
     * Helper to make URL absolute if needed
     */
    private function make_absolute_url_if_needed($thumbnail_url, $base_url) {
        // Convert relative URLs to absolute
        if (!filter_var($thumbnail_url, FILTER_VALIDATE_URL)) {
            return $this->make_absolute_url($thumbnail_url, $base_url);
        }
        return $thumbnail_url;
    }

    /**
     * Fetch webpage content
     *
     * @param string $url Page URL
     * @return string|false Page HTML content
     */
    private function fetch_webpage_content($url) {
        // Use WordPress HTTP API
        $response = wp_remote_get($url, array(
            'timeout' => 15,
            'user-agent' => 'RSS Aggregator Bot/1.0',
            'headers' => array(
                'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            )
        ));

        if (is_wp_error($response)) {
            error_log('RSS Aggregator: Failed to fetch webpage: ' . $response->get_error_message());
            return false;
        }

        $status_code = wp_remote_retrieve_response_code($response);
        if ($status_code !== 200) {
            error_log('RSS Aggregator: Webpage returned status code: ' . $status_code);
            return false;
        }

        return wp_remote_retrieve_body($response);
    }

    /**
     * Extract Open Graph image
     *
     * @param string $html Page HTML
     * @return string|null Image URL
     */
    private function extract_og_image($html) {
        if (preg_match('/<meta[^>]+property=["\']og:image["\'][^>]+content=["\']([^"\']+)["\'][^>]*>/i', $html, $matches)) {
            return trim($matches[1]);
        }

        if (preg_match('/<meta[^>]+content=["\']([^"\']+)["\'][^>]+property=["\']og:image["\'][^>]*>/i', $html, $matches)) {
            return trim($matches[1]);
        }

        return null;
    }

    /**
     * Extract Twitter Card image
     *
     * @param string $html Page HTML
     * @return string|null Image URL
     */
    private function extract_twitter_image($html) {
        // Twitter Card image
        if (preg_match('/<meta[^>]+name=["\']twitter:image["\'][^>]+content=["\']([^"\']+)["\'][^>]*>/i', $html, $matches)) {
            return trim($matches[1]);
        }

        if (preg_match('/<meta[^>]+content=["\']([^"\']+)["\'][^>]+name=["\']twitter:image["\'][^>]*>/i', $html, $matches)) {
            return trim($matches[1]);
        }

        return null;
    }

    /**
     * Extract other meta images
     *
     * @param string $html Page HTML
     * @return string|null Image URL
     */
    private function extract_meta_image($html) {
        // Schema.org image
        if (preg_match('/<meta[^>]+itemprop=["\']image["\'][^>]+content=["\']([^"\']+)["\'][^>]*>/i', $html, $matches)) {
            return trim($matches[1]);
        }

        // Generic image meta
        if (preg_match('/<meta[^>]+name=["\']image["\'][^>]+content=["\']([^"\']+)["\'][^>]*>/i', $html, $matches)) {
            return trim($matches[1]);
        }

        return null;
    }

    /**
     * Extract first image from content
     *
     * @param string $html Page HTML
     * @return string|null Image URL
     */
    private function extract_first_content_image($html) {
        // Look for images in article content
        $content_selectors = array(
            'article img',
            '.content img',
            '.post-content img',
            '.entry-content img',
            'main img'
        );

        // Simple regex to find first img tag
        if (preg_match('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $html, $matches)) {
            $src = trim($matches[1]);

            // Skip common non-content images
            $skip_patterns = array(
                '/avatar/',
                '/logo/',
                '/icon/',
                '/button/',
                '/banner/',
                '/ad/',
                '/advertisement/',
                'data:image',
                '.gif',
                'tracking',
                'pixel'
            );

            foreach ($skip_patterns as $pattern) {
                if (stripos($src, $pattern) !== false) {
                    return null;
                }
            }

            if ($this->is_valid_image_url($src)) {
                return $src;
            }
        }

        return null;
    }

    /**
     * Extract first image from HTML content
     *
     * @param string $content HTML content
     * @return string|null Image URL
     */
    private function extract_first_image_from_content($content) {
        if (preg_match('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $content, $matches)) {
            $src = trim($matches[1]);
            if ($this->is_valid_image_url($src)) {
                return $src;
            }
        }

        return null;
    }

    /**
     * Check if URL is a valid image URL
     *
     * @param string $url URL to check
     * @return bool Is valid image URL
     */
    private function is_valid_image_url($url) {
        if (empty($url) || !is_string($url)) {
            return false;
        }

        // Check if it's a valid URL
        if (!filter_var($url, FILTER_VALIDATE_URL) && !preg_match('/^\//', $url)) {
            return false;
        }

        // Check file extension
        $image_extensions = array('jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp');
        $extension = strtolower(pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION));

        return in_array($extension, $image_extensions);
    }

    /**
     * Convert relative URL to absolute URL
     *
     * @param string $relative_url Relative URL
     * @param string $base_url Base URL
     * @return string Absolute URL
     */
    private function make_absolute_url($relative_url, $base_url) {
        if (filter_var($relative_url, FILTER_VALIDATE_URL)) {
            return $relative_url;
        }

        $parsed_base = parse_url($base_url);

        if (!$parsed_base) {
            return $relative_url;
        }

        $scheme = $parsed_base['scheme'] ?? 'https';
        $host = $parsed_base['host'] ?? '';

        if (strpos($relative_url, '//') === 0) {
            return $scheme . ':' . $relative_url;
        }

        if (strpos($relative_url, '/') === 0) {
            return $scheme . '://' . $host . $relative_url;
        }

        $path = $parsed_base['path'] ?? '/';
        $path = rtrim(dirname($path), '/') . '/';

        return $scheme . '://' . $host . $path . $relative_url;
    }

    /**
     * Extract thumbnail by searching for article title and nearby images
     *
     * @param string $html Page HTML content
     * @param string $title Article title
     * @param string $base_url Base URL for relative links
     * @return string|null Thumbnail URL
     */
    private function extract_thumbnail_by_title($html, $title, $base_url) {
        error_log("RSS Thumbnail Extractor: Starting title-based search for: " . $title);

        // Clean and prepare title for search
        $clean_title = $this->clean_title_for_search($title);

        if (empty($clean_title)) {
            error_log("RSS Thumbnail Extractor: Empty clean title");
            return null;
        }

        error_log("RSS Thumbnail Extractor: Clean title: " . $clean_title);

        // Try to find the title in the HTML
        $title_position = $this->find_title_in_html($html, $clean_title);

        if ($title_position === false) {
            error_log("RSS Thumbnail Extractor: Title not found in HTML");
            return null;
        }

        error_log("RSS Thumbnail Extractor: Title found at position: " . $title_position);

        // Extract content around the title
        $content_section = $this->extract_content_around_title($html, $title_position);

        if (empty($content_section)) {
            error_log("RSS Thumbnail Extractor: No content section found");
            return null;
        }

        error_log("RSS Thumbnail Extractor: Content section length: " . strlen($content_section));
        error_log("RSS Thumbnail Extractor: Content section preview: " . substr($content_section, 0, 500) . "...");

        // Find images in the content section
        $thumbnail_url = $this->find_suitable_image_in_content($content_section, $base_url);

        if ($thumbnail_url) {
            error_log("RSS Thumbnail Extractor: Found suitable image: " . $thumbnail_url);
        } else {
            error_log("RSS Thumbnail Extractor: No suitable image found in content");
        }

        return $thumbnail_url;
    }

    /**
     * Clean title for search - remove special characters, normalize spaces
     *
     * @param string $title Original title
     * @return string Cleaned title
     */
    private function clean_title_for_search($title) {
        // Remove HTML tags
        $title = strip_tags($title);

        // Normalize whitespace
        $title = preg_replace('/\s+/', ' ', $title);

        // Trim
        $title = trim($title);

        // Remove quotes and special characters that might interfere with search
        $title = str_replace(array('"', "'", '&quot;', '&#039;', '&amp;'), '', $title);

        // Convert to lowercase for better matching
        $title = strtolower($title);

        return $title;
    }

    /**
     * Find title position in HTML
     *
     * @param string $html Page HTML
     * @param string $title Clean title
     * @return int|false Position of title or false if not found
     */
    private function find_title_in_html($html, $title) {
        error_log("RSS Thumbnail Extractor: Searching for title in HTML: " . $title);

        // First priority: Search in heading tags (h1-h6) - most likely location for article titles
        $heading_patterns = array(
            // H1 tags (most common for article titles)
            '/<h1[^>]*>([^<]*)<\/h1>/i',
            // H2 tags (secondary titles)
            '/<h2[^>]*>([^<]*)<\/h2>/i',
            // H3 tags (less common but possible)
            '/<h3[^>]*>([^<]*)<\/h3>/i',
            // H4-H6 (rare but comprehensive)
            '/<h[4-6][^>]*>([^<]*)<\/h[4-6]>/i'
        );

        foreach ($heading_patterns as $pattern) {
            if (preg_match_all($pattern, $html, $matches, PREG_OFFSET_CAPTURE)) {
                foreach ($matches[1] as $match) {
                    $heading_text = trim(strip_tags($match[0]));
                    $heading_text_lower = strtolower($heading_text);

                    error_log("RSS Thumbnail Extractor: Found heading: " . $heading_text);

                    // Check for exact match
                    if ($heading_text_lower === $title) {
                        error_log("RSS Thumbnail Extractor: Exact heading match found at position: " . $match[1]);
                        return $match[1];
                    }

                    // Check for partial match (80% similarity)
                    $similarity = 0;
                    similar_text($heading_text_lower, $title, $similarity);
                    if ($similarity > 80) {
                        error_log("RSS Thumbnail Extractor: Similar heading match ({$similarity}%) found at position: " . $match[1]);
                        return $match[1];
                    }

                    // Check if title contains heading or vice versa
                    if (strpos($title, $heading_text_lower) !== false || strpos($heading_text_lower, $title) !== false) {
                        error_log("RSS Thumbnail Extractor: Partial heading match found at position: " . $match[1]);
                        return $match[1];
                    }
                }
            }
        }

        // Second priority: Search in title-related CSS classes
        $class_patterns = array(
            '/<[^>]*class="[^"]*entry[^"]*title[^"]*"[^>]*>([^<]*)<\/[^>]*>/i',
            '/<[^>]*class="[^"]*post[^"]*title[^"]*"[^>]*>([^<]*)<\/[^>]*>/i',
            '/<[^>]*class="[^"]*article[^"]*title[^"]*"[^>]*>([^<]*)<\/[^>]*>/i',
            '/<[^>]*class="[^"]*title[^"]*"[^>]*>([^<]*)<\/[^>]*>/i'
        );

        foreach ($class_patterns as $pattern) {
            if (preg_match_all($pattern, $html, $matches, PREG_OFFSET_CAPTURE)) {
                foreach ($matches[1] as $match) {
                    $element_text = trim(strip_tags($match[0]));
                    $element_text_lower = strtolower($element_text);

                    if ($element_text_lower === $title || strpos($title, $element_text_lower) !== false) {
                        error_log("RSS Thumbnail Extractor: Title class match found at position: " . $match[1]);
                        return $match[1];
                    }
                }
            }
        }

        // Third priority: Try exact text match in HTML
        $position = stripos($html, $title);
        if ($position !== false) {
            error_log("RSS Thumbnail Extractor: Found exact text match at position: " . $position);
            return $position;
        }

        // Fourth priority: Try partial matches with first few words
        $title_words = explode(' ', $title);
        if (count($title_words) >= 3) {
            for ($i = 3; $i <= min(6, count($title_words)); $i++) {
                $partial_title = implode(' ', array_slice($title_words, 0, $i));
                $position = stripos($html, $partial_title);

                if ($position !== false) {
                    error_log("RSS Thumbnail Extractor: Found partial word match ({$i} words) at position: " . $position);
                    return $position;
                }
            }
        }

        error_log("RSS Thumbnail Extractor: Title not found in HTML");
        return false;
    }

    /**
     * Extract content section around title
     *
     * @param string $html Page HTML
     * @param int $title_position Position of title
     * @return string Content section
     */
    private function extract_content_around_title($html, $title_position) {
        // Extract content from before title position to next 10000 characters
        $before_length = 2000;  // Look 2000 chars before title
        $after_length = 10000;  // Look 10000 chars after title

        $start_position = max(0, $title_position - $before_length);
        $total_length = $before_length + $after_length;

        $content_section = substr($html, $start_position, $total_length);

        // Try to find article/content containers
        $content_patterns = array(
            '/<article[^>]*>.*?<\/article>/is',
            '/<div[^>]*class="[^"]*content[^"]*"[^>]*>.*?<\/div>/is',
            '/<div[^>]*class="[^"]*post[^"]*"[^>]*>.*?<\/div>/is',
            '/<div[^>]*class="[^"]*entry[^"]*"[^>]*>.*?<\/div>/is',
            '/<main[^>]*>.*?<\/main>/is'
        );

        foreach ($content_patterns as $pattern) {
            if (preg_match($pattern, $content_section, $matches)) {
                return $matches[0];
            }
        }

        return $content_section;
    }

    /**
     * Find suitable image in content section
     *
     * @param string $content Content HTML
     * @param string $base_url Base URL for relative links
     * @return string|null Image URL
     */
    private function find_suitable_image_in_content($content, $base_url) {
        error_log("RSS Thumbnail Extractor: Searching for images in content section, length: " . strlen($content));

        // Find all images in content
        preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $content, $matches, PREG_SET_ORDER);

        if (empty($matches)) {
            error_log("RSS Thumbnail Extractor: No img tags found in content");
            return null;
        }

        error_log("RSS Thumbnail Extractor: Found " . count($matches) . " img tags in content");

        $suitable_images = array();

        foreach ($matches as $match) {
            $img_tag = $match[0];
            $img_src = $match[1];

            error_log("RSS Thumbnail Extractor: Checking image: " . $img_src);

            // Skip common non-content images
            if ($this->should_skip_image($img_src)) {
                error_log("RSS Thumbnail Extractor: Skipping image (filtered): " . $img_src);
                continue;
            }

            // Convert relative URL to absolute
            if (!filter_var($img_src, FILTER_VALIDATE_URL)) {
                $img_src = $this->make_absolute_url($img_src, $base_url);
                error_log("RSS Thumbnail Extractor: Made absolute URL: " . $img_src);
            }

            // Check image dimensions if possible
            if ($this->is_suitable_thumbnail_size($img_tag, $img_src)) {
                error_log("RSS Thumbnail Extractor: Found suitable image: " . $img_src);
                $suitable_images[] = $img_src;
            } else {
                error_log("RSS Thumbnail Extractor: Image not suitable size: " . $img_src);
            }
        }

        // Return first suitable image, or if none found, return first non-filtered image
        if (!empty($suitable_images)) {
            return $suitable_images[0];
        }

        // If no suitable images found, try to return any non-filtered image as fallback
        foreach ($matches as $match) {
            $img_src = $match[1];

            if (!$this->should_skip_image($img_src)) {
                if (!filter_var($img_src, FILTER_VALIDATE_URL)) {
                    $img_src = $this->make_absolute_url($img_src, $base_url);
                }
                error_log("RSS Thumbnail Extractor: Using fallback image: " . $img_src);
                return $img_src;
            }
        }

        error_log("RSS Thumbnail Extractor: No suitable images found in content");
        return null;
    }

    /**
     * Check if image should be skipped
     *
     * @param string $img_src Image source URL
     * @return bool True if should skip
     */
    private function should_skip_image($img_src) {
        $skip_patterns = array(
            '/avatar/',
            '/logo/',
            '/icon/',
            '/button/',
            '/banner/',
            '/banery/',  // Polish banners
            '/ad/',
            '/advertisement/',
            '/tracking/',
            '/pixel/',
            '/spacer/',
            '/blank\.',
            'data:image',
            '.gif$',
            'gravatar',
            'facebook',
            'twitter',
            'social',
            'plywak_',   // Skip specific banner files
            'reklama',   // Polish ads
            'sponsor'
        );

        foreach ($skip_patterns as $pattern) {
            if (preg_match($pattern, $img_src)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if image has suitable thumbnail dimensions
     *
     * @param string $img_tag Full img tag
     * @param string $img_src Image source URL
     * @return bool True if suitable
     */
    private function is_suitable_thumbnail_size($img_tag, $img_src) {
        // Extract width and height from img tag attributes
        $width = $this->extract_image_dimension($img_tag, 'width');
        $height = $this->extract_image_dimension($img_tag, 'height');

        // If dimensions are specified in HTML and both are >= 150px, it's good
        if ($width >= 150 && $height >= 150) {
            return true;
        }

        // If dimensions are too small, skip
        if (($width > 0 && $width < 150) || ($height > 0 && $height < 150)) {
            return false;
        }

        // If no dimensions in HTML, try to get actual image size (with timeout)
        if ($width === 0 && $height === 0) {
            return $this->check_actual_image_size($img_src);
        }

        // Default to true if we can't determine size
        return true;
    }

    /**
     * Extract image dimension from img tag
     *
     * @param string $img_tag Image tag HTML
     * @param string $attribute Attribute name (width or height)
     * @return int Dimension value or 0 if not found
     */
    private function extract_image_dimension($img_tag, $attribute) {
        if (preg_match('/' . $attribute . '=["\']?(\d+)["\']?/i', $img_tag, $matches)) {
            return intval($matches[1]);
        }

        // Check CSS style attribute
        if (preg_match('/style=["\'][^"\']*' . $attribute . ':\s*(\d+)px[^"\']*["\']/', $img_tag, $matches)) {
            return intval($matches[1]);
        }

        return 0;
    }

    /**
     * Check actual image size by downloading headers
     *
     * @param string $img_src Image URL
     * @return bool True if suitable size
     */
    private function check_actual_image_size($img_src) {
        // Quick check - if URL suggests it's a thumbnail, accept it
        if (preg_match('/thumb|large|medium|featured/i', $img_src)) {
            return true;
        }

        // Try to get image size without downloading full image
        $headers = wp_remote_head($img_src, array(
            'timeout' => 5,
            'user-agent' => 'RSS Aggregator Bot/1.0'
        ));

        if (is_wp_error($headers)) {
            return true; // Default to true if we can't check
        }

        $content_length = wp_remote_retrieve_header($headers, 'content-length');

        // If image is very small (< 5KB), probably not a good thumbnail
        if ($content_length && intval($content_length) < 5120) {
            return false;
        }

        // If image is very large (> 2MB), might be too big but still accept
        return true;
    }

    /**
     * Extract images from common article containers
     *
     * @param string $html Page HTML
     * @param string $base_url Base URL for relative links
     * @return string|null Image URL
     */
    private function extract_images_from_article_containers($html, $base_url) {
        error_log("RSS Thumbnail Extractor: Searching for images in article containers");

        // Common article container patterns
        $container_patterns = array(
            // Article tags
            '/<article[^>]*>(.*?)<\/article>/is',
            // Main content areas
            '/<main[^>]*>(.*?)<\/main>/is',
            // Content divs
            '/<div[^>]*class="[^"]*content[^"]*"[^>]*>(.*?)<\/div>/is',
            '/<div[^>]*class="[^"]*post[^"]*content[^"]*"[^>]*>(.*?)<\/div>/is',
            '/<div[^>]*class="[^"]*entry[^"]*content[^"]*"[^>]*>(.*?)<\/div>/is',
            // Post body
            '/<div[^>]*class="[^"]*post[^"]*body[^"]*"[^>]*>(.*?)<\/div>/is',
            '/<div[^>]*class="[^"]*entry[^"]*"[^>]*>(.*?)<\/div>/is',
            // WordPress specific
            '/<div[^>]*class="[^"]*wp[^"]*content[^"]*"[^>]*>(.*?)<\/div>/is'
        );

        foreach ($container_patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $container_content = $matches[1];
                error_log("RSS Thumbnail Extractor: Found article container, length: " . strlen($container_content));

                $thumbnail_url = $this->find_suitable_image_in_content($container_content, $base_url);
                if ($thumbnail_url) {
                    error_log("RSS Thumbnail Extractor: Found image in container: " . $thumbnail_url);
                    return $thumbnail_url;
                }
            }
        }

        // If no containers found, search in entire body
        if (preg_match('/<body[^>]*>(.*?)<\/body>/is', $html, $matches)) {
            error_log("RSS Thumbnail Extractor: Searching in entire body as fallback");
            $body_content = $matches[1];
            return $this->find_suitable_image_in_content($body_content, $base_url);
        }

        error_log("RSS Thumbnail Extractor: No images found in article containers");
        return null;
    }

    /**
     * Extract images from RSS content using advanced search
     *
     * @param array $item RSS item data
     * @return string|null Image URL
     */
    private function extract_images_from_rss_content($item) {
        // Collect all possible content fields
        $content_fields = array();

        // Standard content fields
        if (!empty($item['content'])) {
            $content_fields[] = $item['content'];
        }
        if (!empty($item['description'])) {
            $content_fields[] = $item['description'];
        }

        // Additional RSS fields that might contain content
        $additional_fields = array(
            'summary',
            'content:encoded',
            'excerpt',
            'body',
            'text',
            'fulltext',
            'content_encoded'
        );

        foreach ($additional_fields as $field) {
            if (!empty($item[$field])) {
                $content_fields[] = $item[$field];
            }
        }

        // Search for images in all content fields
        foreach ($content_fields as $content) {
            $image_url = $this->search_images_in_rss_text($content);
            if ($image_url) {
                return $image_url;
            }
        }

        return null;
    }

    /**
     * Search for images in RSS text content
     *
     * @param string $content RSS content
     * @return string|null Image URL
     */
    private function search_images_in_rss_text($content) {
        if (empty($content)) {
            return null;
        }

        error_log("RSS Thumbnail Extractor: Searching in RSS content: " . substr($content, 0, 200) . "...");

        // Method 1: Look for img tags with various patterns
        $img_patterns = array(
            // Standard img tags
            '/<img[^>]+src=["\']([^"\']+\.(jpg|jpeg|png|gif|webp|bmp))["\'][^>]*>/i',
            // Img tags without quotes
            '/<img[^>]+src=([^\s>]+\.(jpg|jpeg|png|gif|webp|bmp))[^>]*>/i',
            // Images in CDATA
            '/\[CDATA\[.*?<img[^>]+src=["\']([^"\']+\.(jpg|jpeg|png|gif|webp|bmp))["\'][^>]*>.*?\]\]/is'
        );

        foreach ($img_patterns as $pattern) {
            if (preg_match($pattern, $content, $matches)) {
                $image_url = $matches[1];
                error_log("RSS Thumbnail Extractor: Found img tag: " . $image_url);
                if ($this->is_valid_image_url($image_url) && !$this->should_skip_image($image_url)) {
                    error_log("RSS Thumbnail Extractor: Valid image from img tag: " . $image_url);
                    return $image_url;
                }
            }
        }

        // Method 1.5: Look for WordPress gallery images (markdown style)
        $gallery_patterns = array(
            // WordPress gallery thumbnails like [![z1](thumbs/thumbs_z1.jpg)](z1.jpg)
            '/\[!\[[^\]]*\]\(([^)]+\/thumbs\/[^)]+\.(jpg|jpeg|png|gif|webp|bmp))\)\]\(([^)]+\.(jpg|jpeg|png|gif|webp|bmp))\)/i',
            // Direct gallery links like (https://example.com/gallery/image.jpg)
            '/\]\(([^)]+\/gallery\/[^)]+\.(jpg|jpeg|png|gif|webp|bmp))\)/i',
            // WordPress content gallery images
            '/wp-content\/gallery\/[^"\'>\s]+\.(jpg|jpeg|png|gif|webp|bmp)/i'
        );

        foreach ($gallery_patterns as $pattern) {
            if (preg_match($pattern, $content, $matches)) {
                // For gallery pattern, use the full image (match 3), not thumbnail (match 1)
                $image_url = isset($matches[3]) ? $matches[3] : $matches[1];
                error_log("RSS Thumbnail Extractor: Found gallery image: " . $image_url);
                if ($this->is_valid_image_url($image_url) && !$this->should_skip_image($image_url)) {
                    error_log("RSS Thumbnail Extractor: Valid gallery image: " . $image_url);
                    return $image_url;
                }
            }
        }

        // Method 2: Look for direct image URLs in text
        $url_patterns = array(
            // HTTP/HTTPS image URLs
            '/https?:\/\/[^\s<>"\']+\.(jpg|jpeg|png|gif|webp|bmp)(?:\?[^\s<>"\']*)?/i',
            // Image URLs in quotes
            '/["\']https?:\/\/[^"\']+\.(jpg|jpeg|png|gif|webp|bmp)(?:\?[^"\']*)?["\']/i'
        );

        foreach ($url_patterns as $pattern) {
            if (preg_match_all($pattern, $content, $matches)) {
                foreach ($matches[0] as $match) {
                    // Clean up the URL (remove quotes)
                    $image_url = trim($match, '"\'');

                    if ($this->is_valid_image_url($image_url) && !$this->should_skip_image($image_url)) {
                        return $image_url;
                    }
                }
            }
        }

        // Method 3: Look for base64 encoded images (less common but possible)
        if (preg_match('/data:image\/[^;]+;base64,([A-Za-z0-9+\/=]+)/i', $content, $matches)) {
            // For base64 images, we could save them as files, but for now skip
            // return 'data:image/...'; // Uncomment if you want to support base64
        }

        // Method 4: Look for image URLs in JSON-like structures within RSS
        if (preg_match('/["\'](?:image|thumbnail|photo|picture)["\']:\s*["\']([^"\']+\.(jpg|jpeg|png|gif|webp|bmp))["\']/', $content, $matches)) {
            $image_url = $matches[1];
            if ($this->is_valid_image_url($image_url) && !$this->should_skip_image($image_url)) {
                return $image_url;
            }
        }

        // Method 5: Look for WordPress attachment URLs
        if (preg_match('/wp-content\/uploads\/[^"\s<>]+\.(jpg|jpeg|png|gif|webp|bmp)/i', $content, $matches)) {
            $image_url = $matches[0];

            // Make sure it's a complete URL
            if (!preg_match('/^https?:\/\//', $image_url)) {
                // Try to construct full URL if we have the item URL
                if (!empty($item['url'])) {
                    $parsed_url = parse_url($item['url']);
                    if ($parsed_url && !empty($parsed_url['host'])) {
                        $scheme = !empty($parsed_url['scheme']) ? $parsed_url['scheme'] : 'https';
                        $image_url = $scheme . '://' . $parsed_url['host'] . '/' . ltrim($image_url, '/');
                    }
                }
            }

            if ($this->is_valid_image_url($image_url) && !$this->should_skip_image($image_url)) {
                return $image_url;
            }
        }

        return null;
    }

    /**
     * Download and attach thumbnail to post
     */
    public function attach_thumbnail($post_id, $thumbnail_url) {
        error_log("RSS Thumbnail Extractor: Attaching thumbnail to post {$post_id}: {$thumbnail_url}");

        if (empty($thumbnail_url) || empty($post_id)) {
            error_log("RSS Thumbnail Extractor: Empty thumbnail URL or post ID");
            return false;
        }

        // Check if post already has thumbnail
        if (has_post_thumbnail($post_id)) {
            error_log("RSS Thumbnail Extractor: Post {$post_id} already has thumbnail");
            return true;
        }

        // Download image
        $image_data = $this->download_image($thumbnail_url);
        if (!$image_data) {
            error_log("RSS Thumbnail Extractor: Failed to download image: {$thumbnail_url}");
            return false;
        }

        error_log("RSS Thumbnail Extractor: Successfully downloaded image, size: " . strlen($image_data) . " bytes");
        
        // Get filename
        $filename = $this->get_filename_from_url($thumbnail_url);
        
        // Upload to media library
        $upload = wp_upload_bits($filename, null, $image_data);
        if ($upload['error']) {
            return false;
        }
        
        // Create attachment
        $attachment = array(
            'post_mime_type' => $upload['type'],
            'post_title' => sanitize_file_name(pathinfo($filename, PATHINFO_FILENAME)),
            'post_content' => '',
            'post_status' => 'inherit'
        );
        
        $attachment_id = wp_insert_attachment($attachment, $upload['file'], $post_id);
        if (is_wp_error($attachment_id)) {
            return false;
        }
        
        // Generate metadata
        require_once(ABSPATH . 'wp-admin/includes/image.php');
        $attachment_data = wp_generate_attachment_metadata($attachment_id, $upload['file']);
        wp_update_attachment_metadata($attachment_id, $attachment_data);
        
        // Set as post thumbnail
        set_post_thumbnail($post_id, $attachment_id);
        
        return $attachment_id;
    }
    
    /**
     * Download image from URL
     */
    private function download_image($url) {
        error_log("RSS Thumbnail Extractor: Downloading image: {$url}");

        $response = wp_remote_get($url, array(
            'timeout' => 30,
            'user-agent' => 'RSS Aggregator Pro/2.0.0'
        ));

        if (is_wp_error($response)) {
            error_log("RSS Thumbnail Extractor: WP Error downloading image: " . $response->get_error_message());
            return false;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        error_log("RSS Thumbnail Extractor: HTTP response code: {$response_code}");

        if ($response_code !== 200) {
            error_log("RSS Thumbnail Extractor: HTTP error {$response_code} for URL: {$url}");
            return false;
        }

        $content_type = wp_remote_retrieve_header($response, 'content-type');
        error_log("RSS Thumbnail Extractor: Content type: {$content_type}");

        if (strpos($content_type, 'image/') !== 0) {
            error_log("RSS Thumbnail Extractor: Invalid content type: {$content_type}");
            return false;
        }

        $body = wp_remote_retrieve_body($response);
        error_log("RSS Thumbnail Extractor: Downloaded image size: " . strlen($body) . " bytes");

        return $body;
    }
    
    /**
     * Get filename from URL
     */
    private function get_filename_from_url($url) {
        $path = parse_url($url, PHP_URL_PATH);
        $filename = basename($path);
        
        // If no extension, add .jpg
        if (strpos($filename, '.') === false) {
            $filename .= '.jpg';
        }
        
        // Sanitize filename
        $filename = sanitize_file_name($filename);
        
        // Add timestamp to avoid conflicts
        $info = pathinfo($filename);
        $filename = $info['filename'] . '_' . time() . '.' . $info['extension'];
        
        return $filename;
    }

}
