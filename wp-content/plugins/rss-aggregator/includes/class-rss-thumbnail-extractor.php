<?php
/**
 * RSS Thumbnail Extractor Class
 *
 * @package RSS_Aggregator
 * @version 2.0.0
 * <AUTHOR>
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class RSS_Thumbnail_Extractor {
    
    /**
     * Extract thumbnail from RSS item
     */
    public function extract_thumbnail($item, $content = '') {
        $thumbnail_url = null;

        // 0. If item is array and has thumbnail_url, use it directly
        if (is_array($item) && !empty($item['thumbnail_url'])) {
            error_log("RSS Aggregator: Found thumbnail_url in item array: " . $item['thumbnail_url']);
            return $item['thumbnail_url'];
        }

        // 1. Try RSS enclosure
        if (isset($item->enclosure) && !empty($item->enclosure['url'])) {
            $thumbnail_url = $item->enclosure['url'];
        }
        
        // 2. Try RSS media:content
        if (!$thumbnail_url && isset($item->{'media:content'})) {
            $media_content = $item->{'media:content'};
            if (is_array($media_content)) {
                foreach ($media_content as $media) {
                    if (isset($media['@attributes']['url'])) {
                        $thumbnail_url = $media['@attributes']['url'];
                        break;
                    }
                }
            } elseif (isset($media_content['@attributes']['url'])) {
                $thumbnail_url = $media_content['@attributes']['url'];
            }
        }
        
        // 3. Try RSS media:thumbnail
        if (!$thumbnail_url && isset($item->{'media:thumbnail'})) {
            $media_thumbnail = $item->{'media:thumbnail'};
            if (isset($media_thumbnail['@attributes']['url'])) {
                $thumbnail_url = $media_thumbnail['@attributes']['url'];
            }
        }
        
        // 4. Try to extract from content
        if (!$thumbnail_url && !empty($content)) {
            $thumbnail_url = $this->extract_from_content($content);
        }
        
        // 5. Try to extract from source page
        if (!$thumbnail_url && isset($item->link)) {
            $thumbnail_url = $this->extract_from_page($item->link);
        }
        
        return $thumbnail_url;
    }
    
    /**
     * Extract thumbnail from content
     */
    private function extract_from_content($content) {
        // Look for img tags
        preg_match('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $content, $matches);
        
        if (!empty($matches[1])) {
            return $matches[1];
        }
        
        return null;
    }
    
    /**
     * Extract thumbnail from source page
     */
    private function extract_from_page($url) {
        if (empty($url)) {
            return null;
        }
        
        // Get page content
        $response = wp_remote_get($url, array(
            'timeout' => 30,
            'user-agent' => 'RSS Aggregator Pro/2.0.0'
        ));
        
        if (is_wp_error($response)) {
            return null;
        }
        
        $body = wp_remote_retrieve_body($response);
        if (empty($body)) {
            return null;
        }
        
        // Try og:image
        preg_match('/<meta[^>]+property=["\']og:image["\'][^>]+content=["\']([^"\']+)["\'][^>]*>/i', $body, $matches);
        if (!empty($matches[1])) {
            return $matches[1];
        }
        
        // Try twitter:image
        preg_match('/<meta[^>]+name=["\']twitter:image["\'][^>]+content=["\']([^"\']+)["\'][^>]*>/i', $body, $matches);
        if (!empty($matches[1])) {
            return $matches[1];
        }
        
        // Try first image in content
        preg_match('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $body, $matches);
        if (!empty($matches[1])) {
            return $matches[1];
        }
        
        return null;
    }
    
    /**
     * Download and attach thumbnail to post
     */
    public function attach_thumbnail($post_id, $thumbnail_url) {
        if (empty($thumbnail_url) || empty($post_id)) {
            return false;
        }
        
        // Check if post already has thumbnail
        if (has_post_thumbnail($post_id)) {
            return true;
        }
        
        // Download image
        $image_data = $this->download_image($thumbnail_url);
        if (!$image_data) {
            return false;
        }
        
        // Get filename
        $filename = $this->get_filename_from_url($thumbnail_url);
        
        // Upload to media library
        $upload = wp_upload_bits($filename, null, $image_data);
        if ($upload['error']) {
            return false;
        }
        
        // Create attachment
        $attachment = array(
            'post_mime_type' => $upload['type'],
            'post_title' => sanitize_file_name(pathinfo($filename, PATHINFO_FILENAME)),
            'post_content' => '',
            'post_status' => 'inherit'
        );
        
        $attachment_id = wp_insert_attachment($attachment, $upload['file'], $post_id);
        if (is_wp_error($attachment_id)) {
            return false;
        }
        
        // Generate metadata
        require_once(ABSPATH . 'wp-admin/includes/image.php');
        $attachment_data = wp_generate_attachment_metadata($attachment_id, $upload['file']);
        wp_update_attachment_metadata($attachment_id, $attachment_data);
        
        // Set as post thumbnail
        set_post_thumbnail($post_id, $attachment_id);
        
        return $attachment_id;
    }
    
    /**
     * Download image from URL
     */
    private function download_image($url) {
        $response = wp_remote_get($url, array(
            'timeout' => 30,
            'user-agent' => 'RSS Aggregator Pro/2.0.0'
        ));
        
        if (is_wp_error($response)) {
            return false;
        }
        
        $content_type = wp_remote_retrieve_header($response, 'content-type');
        if (strpos($content_type, 'image/') !== 0) {
            return false;
        }
        
        return wp_remote_retrieve_body($response);
    }
    
    /**
     * Get filename from URL
     */
    private function get_filename_from_url($url) {
        $path = parse_url($url, PHP_URL_PATH);
        $filename = basename($path);
        
        // If no extension, add .jpg
        if (strpos($filename, '.') === false) {
            $filename .= '.jpg';
        }
        
        // Sanitize filename
        $filename = sanitize_file_name($filename);
        
        // Add timestamp to avoid conflicts
        $info = pathinfo($filename);
        $filename = $info['filename'] . '_' . time() . '.' . $info['extension'];
        
        return $filename;
    }
    
    /**
     * Validate image URL
     */
    public function is_valid_image_url($url) {
        if (empty($url)) {
            return false;
        }
        
        // Check if URL is valid
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return false;
        }
        
        // Check file extension
        $path = parse_url($url, PHP_URL_PATH);
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
        $valid_extensions = array('jpg', 'jpeg', 'png', 'gif', 'webp');
        
        if (in_array($extension, $valid_extensions)) {
            return true;
        }
        
        // If no extension, try to check content type
        $headers = wp_remote_head($url, array('timeout' => 10));
        if (!is_wp_error($headers)) {
            $content_type = wp_remote_retrieve_header($headers, 'content-type');
            return strpos($content_type, 'image/') === 0;
        }
        
        return false;
    }
}
