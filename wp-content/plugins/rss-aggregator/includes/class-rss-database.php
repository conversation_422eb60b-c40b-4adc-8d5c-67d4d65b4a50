<?php
/**
 * RSS Database handler class
 *
 * @package RSS_Aggregator
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * RSS Database class
 */
class RSS_Database {
    
    /**
     * WordPress database object
     */
    private $wpdb;
    
    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        $this->wpdb = $wpdb;

        // Define table names if not already defined
        if (!defined('RSS_AGGREGATOR_FEEDS_TABLE')) {
            define('RSS_AGGREGATOR_FEEDS_TABLE', $wpdb->prefix . 'rss_aggregator_feeds');
        }
        if (!defined('RSS_AGGREGATOR_ITEMS_TABLE')) {
            define('RSS_AGGREGATOR_ITEMS_TABLE', $wpdb->prefix . 'rss_aggregator_items');
        }
    }
    
    /**
     * Get all feeds
     *
     * @param array $args Query arguments
     * @return array Feeds
     */
    public function get_feeds($args = array()) {
        $defaults = array(
            'status' => 'active',
            'limit' => 0,
            'offset' => 0,
            'orderby' => 'name',
            'order' => 'ASC'
        );
        
        $args = wp_parse_args($args, $defaults);
        
        $sql = "SELECT * FROM " . RSS_AGGREGATOR_FEEDS_TABLE;
        $where_conditions = array();
        $values = array();
        
        // Status filter
        if (!empty($args['status'])) {
            $where_conditions[] = "status = %s";
            $values[] = $args['status'];
        }
        
        // County filter
        if (!empty($args['county'])) {
            $where_conditions[] = "county = %s";
            $values[] = $args['county'];
        }
        
        // Build WHERE clause
        if (!empty($where_conditions)) {
            $sql .= " WHERE " . implode(' AND ', $where_conditions);
        }
        
        // Order by
        $allowed_orderby = array('id', 'name', 'created_at', 'updated_at', 'last_updated');
        if (in_array($args['orderby'], $allowed_orderby)) {
            $sql .= " ORDER BY " . $args['orderby'];
            $sql .= ($args['order'] === 'DESC') ? ' DESC' : ' ASC';
        }
        
        // Limit
        if ($args['limit'] > 0) {
            $sql .= " LIMIT " . intval($args['limit']);
            if ($args['offset'] > 0) {
                $sql .= " OFFSET " . intval($args['offset']);
            }
        }
        
        if (!empty($values)) {
            return $this->wpdb->get_results($this->wpdb->prepare($sql, $values));
        } else {
            return $this->wpdb->get_results($sql);
        }
    }
    
    /**
     * Get single feed by ID
     *
     * @param int $feed_id Feed ID
     * @return object|null Feed object
     */
    public function get_feed($feed_id) {
        return $this->wpdb->get_row($this->wpdb->prepare(
            "SELECT * FROM " . RSS_AGGREGATOR_FEEDS_TABLE . " WHERE id = %d",
            $feed_id
        ));
    }
    
    /**
     * Get feed by URL
     *
     * @param string $url Feed URL
     * @return object|null Feed object
     */
    public function get_feed_by_url($url) {
        return $this->wpdb->get_row($this->wpdb->prepare(
            "SELECT * FROM " . RSS_AGGREGATOR_FEEDS_TABLE . " WHERE url = %s",
            $url
        ));
    }
    
    /**
     * Get feeds due for update
     *
     * @return array Feeds that need updating
     */
    public function get_feeds_due_for_update() {
        $sql = "SELECT * FROM " . RSS_AGGREGATOR_FEEDS_TABLE . " 
                WHERE status = 'active' 
                AND (
                    last_updated IS NULL 
                    OR (
                        update_frequency = 'every_15_minutes' AND last_updated < DATE_SUB(NOW(), INTERVAL 15 MINUTE)
                    ) OR (
                        update_frequency = 'every_30_minutes' AND last_updated < DATE_SUB(NOW(), INTERVAL 30 MINUTE)
                    ) OR (
                        update_frequency = 'hourly' AND last_updated < DATE_SUB(NOW(), INTERVAL 1 HOUR)
                    ) OR (
                        update_frequency = 'every_2_hours' AND last_updated < DATE_SUB(NOW(), INTERVAL 2 HOUR)
                    ) OR (
                        update_frequency = 'every_6_hours' AND last_updated < DATE_SUB(NOW(), INTERVAL 6 HOUR)
                    ) OR (
                        update_frequency = 'daily' AND last_updated < DATE_SUB(NOW(), INTERVAL 1 DAY)
                    )
                )";
        
        return $this->wpdb->get_results($sql);
    }
    
    /**
     * Save feed
     *
     * @param array $data Feed data
     * @return int|false Feed ID on success, false on failure
     */
    public function save_feed($data) {
        $defaults = array(
            'name' => '',
            'url' => '',
            'geodirectory_place_id' => null,
            'geodirectory_place_name' => '',
            'assigned_user_id' => null,
            'assigned_user_name' => '',
            'county' => '',
            'region' => '',
            'update_frequency' => 'hourly',
            'initial_import_count' => 10,
            'status' => 'active'
        );
        
        $data = wp_parse_args($data, $defaults);
        
        // Validate required fields
        if (empty($data['name']) || empty($data['url'])) {
            return false;
        }
        
        // Check if updating existing feed
        if (!empty($data['id'])) {
            $result = $this->wpdb->update(
                RSS_AGGREGATOR_FEEDS_TABLE,
                array(
                    'name' => sanitize_text_field($data['name']),
                    'url' => esc_url_raw($data['url']),
                    'geodirectory_place_id' => !empty($data['geodirectory_place_id']) ? intval($data['geodirectory_place_id']) : null,
                    'geodirectory_place_name' => sanitize_text_field($data['geodirectory_place_name']),
                    'assigned_user_id' => !empty($data['assigned_user_id']) ? intval($data['assigned_user_id']) : null,
                    'assigned_user_name' => sanitize_text_field($data['assigned_user_name']),
                    'county' => sanitize_text_field($data['county']),
                    'region' => sanitize_text_field($data['region']),
                    'update_frequency' => sanitize_text_field($data['update_frequency']),
                    'initial_import_count' => intval($data['initial_import_count']),
                    'status' => sanitize_text_field($data['status']),
                    'updated_at' => current_time('mysql')
                ),
                array('id' => intval($data['id'])),
                array('%s', '%s', '%d', '%s', '%d', '%s', '%s', '%s', '%s', '%d', '%s', '%s'),
                array('%d')
            );
            
            return $result !== false ? intval($data['id']) : false;
        } else {
            // Insert new feed
            $result = $this->wpdb->insert(
                RSS_AGGREGATOR_FEEDS_TABLE,
                array(
                    'name' => sanitize_text_field($data['name']),
                    'url' => esc_url_raw($data['url']),
                    'geodirectory_place_id' => !empty($data['geodirectory_place_id']) ? intval($data['geodirectory_place_id']) : null,
                    'geodirectory_place_name' => sanitize_text_field($data['geodirectory_place_name']),
                    'assigned_user_id' => !empty($data['assigned_user_id']) ? intval($data['assigned_user_id']) : null,
                    'assigned_user_name' => sanitize_text_field($data['assigned_user_name']),
                    'county' => sanitize_text_field($data['county']),
                    'region' => sanitize_text_field($data['region']),
                    'update_frequency' => sanitize_text_field($data['update_frequency']),
                    'initial_import_count' => intval($data['initial_import_count']),
                    'status' => sanitize_text_field($data['status'])
                ),
                array('%s', '%s', '%d', '%s', '%d', '%s', '%s', '%s', '%s', '%d', '%s')
            );
            
            return $result !== false ? $this->wpdb->insert_id : false;
        }
    }
    
    /**
     * Delete feed
     *
     * @param int $feed_id Feed ID
     * @return bool Success status
     */
    public function delete_feed($feed_id) {
        // Delete associated items first
        $this->wpdb->delete(
            RSS_AGGREGATOR_ITEMS_TABLE,
            array('feed_id' => $feed_id),
            array('%d')
        );
        
        // Delete feed
        $result = $this->wpdb->delete(
            RSS_AGGREGATOR_FEEDS_TABLE,
            array('id' => $feed_id),
            array('%d')
        );
        
        return $result !== false;
    }
    
    /**
     * Update feed timestamp
     *
     * @param int $feed_id Feed ID
     * @return bool Success status
     */
    public function update_feed_timestamp($feed_id) {
        $result = $this->wpdb->update(
            RSS_AGGREGATOR_FEEDS_TABLE,
            array('last_updated' => current_time('mysql')),
            array('id' => $feed_id),
            array('%s'),
            array('%d')
        );
        
        return $result !== false;
    }
    
    /**
     * Get feed items
     *
     * @param int $feed_id Feed ID
     * @param array $args Query arguments
     * @return array Items
     */
    public function get_items($feed_id = 0, $args = array()) {
        $defaults = array(
            'limit' => 50,
            'offset' => 0,
            'processed' => null,
            'orderby' => 'pub_date',
            'order' => 'DESC'
        );
        
        $args = wp_parse_args($args, $defaults);
        
        $sql = "SELECT * FROM " . RSS_AGGREGATOR_ITEMS_TABLE;
        $where_conditions = array();
        $values = array();
        
        // Feed ID filter
        if ($feed_id > 0) {
            $where_conditions[] = "feed_id = %d";
            $values[] = $feed_id;
        }
        
        // Processed filter
        if ($args['processed'] !== null) {
            $where_conditions[] = "processed = %d";
            $values[] = intval($args['processed']);
        }
        
        // Build WHERE clause
        if (!empty($where_conditions)) {
            $sql .= " WHERE " . implode(' AND ', $where_conditions);
        }
        
        // Order by
        $allowed_orderby = array('id', 'title', 'pub_date', 'created_at');
        if (in_array($args['orderby'], $allowed_orderby)) {
            $sql .= " ORDER BY " . $args['orderby'];
            $sql .= ($args['order'] === 'DESC') ? ' DESC' : ' ASC';
        }
        
        // Limit
        if ($args['limit'] > 0) {
            $sql .= " LIMIT " . intval($args['limit']);
            if ($args['offset'] > 0) {
                $sql .= " OFFSET " . intval($args['offset']);
            }
        }
        
        if (!empty($values)) {
            return $this->wpdb->get_results($this->wpdb->prepare($sql, $values));
        } else {
            return $this->wpdb->get_results($sql);
        }
    }
    
    /**
     * Check if item exists
     *
     * @param int $feed_id Feed ID
     * @param string $guid Item GUID
     * @return bool Item exists
     */
    public function item_exists($feed_id, $guid) {
        $count = $this->wpdb->get_var($this->wpdb->prepare(
            "SELECT COUNT(*) FROM " . RSS_AGGREGATOR_ITEMS_TABLE . " WHERE feed_id = %d AND guid = %s",
            $feed_id,
            $guid
        ));
        
        return $count > 0;
    }
    
    /**
     * Save item
     *
     * @param array $item Item data
     * @param int $feed_id Feed ID
     * @return int|false Item ID on success, false on failure
     */
    public function save_item($item, $feed_id) {
        $result = $this->wpdb->insert(
            RSS_AGGREGATOR_ITEMS_TABLE,
            array(
                'feed_id' => intval($feed_id),
                'title' => sanitize_text_field($item['title']),
                'description' => wp_kses_post($item['description']),
                'url' => esc_url_raw($item['url']),
                'guid' => sanitize_text_field($item['guid']),
                'pub_date' => !empty($item['pub_date']) ? date('Y-m-d H:i:s', strtotime($item['pub_date'])) : current_time('mysql'),
                'thumbnail_url' => !empty($item['thumbnail_url']) ? esc_url_raw($item['thumbnail_url']) : null,
                'processed' => 0
            ),
            array('%d', '%s', '%s', '%s', '%s', '%s', '%s', '%d')
        );
        
        return $result !== false ? $this->wpdb->insert_id : false;
    }
    
    /**
     * Update item post ID
     *
     * @param int $item_id Item ID
     * @param int $post_id Post ID
     * @return bool Success status
     */
    public function update_item_post_id($item_id, $post_id) {
        $result = $this->wpdb->update(
            RSS_AGGREGATOR_ITEMS_TABLE,
            array(
                'post_id' => intval($post_id),
                'processed' => 1
            ),
            array('id' => $item_id),
            array('%d', '%d'),
            array('%d')
        );
        
        return $result !== false;
    }
    
    /**
     * Get statistics
     *
     * @return array Statistics
     */
    public function get_statistics() {
        $stats = array();
        
        // Total feeds
        $stats['total_feeds'] = $this->wpdb->get_var(
            "SELECT COUNT(*) FROM " . RSS_AGGREGATOR_FEEDS_TABLE
        );
        
        // Active feeds
        $stats['active_feeds'] = $this->wpdb->get_var(
            "SELECT COUNT(*) FROM " . RSS_AGGREGATOR_FEEDS_TABLE . " WHERE status = 'active'"
        );
        
        // Total items
        $stats['total_items'] = $this->wpdb->get_var(
            "SELECT COUNT(*) FROM " . RSS_AGGREGATOR_ITEMS_TABLE
        );
        
        // Processed items
        $stats['processed_items'] = $this->wpdb->get_var(
            "SELECT COUNT(*) FROM " . RSS_AGGREGATOR_ITEMS_TABLE . " WHERE processed = 1"
        );
        
        // Items from last 24 hours
        $stats['recent_items'] = $this->wpdb->get_var(
            "SELECT COUNT(*) FROM " . RSS_AGGREGATOR_ITEMS_TABLE . " WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 DAY)"
        );
        
        return $stats;
    }
    
    /**
     * Clean old items
     *
     * @param int $days_old Days to keep items
     * @return int Number of deleted items
     */
    public function clean_old_items($days_old = 30) {
        $result = $this->wpdb->query($this->wpdb->prepare(
            "DELETE FROM " . RSS_AGGREGATOR_ITEMS_TABLE . " WHERE created_at < DATE_SUB(NOW(), INTERVAL %d DAY)",
            $days_old
        ));
        
        return $result !== false ? $result : 0;
    }
    
    /**
     * Get counties with feed counts
     *
     * @return array Counties
     */
    public function get_counties_with_counts() {
        return $this->wpdb->get_results(
            "SELECT county, COUNT(*) as feed_count
             FROM " . RSS_AGGREGATOR_FEEDS_TABLE . "
             WHERE county != '' AND status = 'active'
             GROUP BY county
             ORDER BY county ASC"
        );
    }

    /**
     * Search GeoDirectory places for autocomplete
     */
    public function search_geodirectory_places($search_term, $limit = 10) {
        global $wpdb;

        $search_term_clean = trim($search_term);
        $search_term_like = '%' . $wpdb->esc_like($search_term_clean) . '%';

        // Debug logging
        error_log("RSS Aggregator: Searching places with term: '{$search_term_clean}', prefix: '{$wpdb->prefix}'");

        // Always search in WordPress posts for GeoDirectory places
        $results = $this->search_geodirectory_posts($search_term_like, $limit);

        // If no results from GeoDirectory, add sample places that match search
        if (empty($results)) {
            $sample_places = array(
                (object) array('id' => 1, 'name' => 'Warszawa', 'city' => 'Warszawa', 'region' => 'mazowieckie'),
                (object) array('id' => 2, 'name' => 'Kraków', 'city' => 'Kraków', 'region' => 'małopolskie'),
                (object) array('id' => 3, 'name' => 'Gdańsk', 'city' => 'Gdańsk', 'region' => 'pomorskie'),
                (object) array('id' => 4, 'name' => 'Wrocław', 'city' => 'Wrocław', 'region' => 'dolnośląskie'),
                (object) array('id' => 5, 'name' => 'Poznań', 'city' => 'Poznań', 'region' => 'wielkopolskie'),
                (object) array('id' => 6, 'name' => 'Łódź', 'city' => 'Łódź', 'region' => 'łódzkie'),
                (object) array('id' => 7, 'name' => 'Szczecin', 'city' => 'Szczecin', 'region' => 'zachodniopomorskie'),
                (object) array('id' => 8, 'name' => 'Bydgoszcz', 'city' => 'Bydgoszcz', 'region' => 'kujawsko-pomorskie'),
                (object) array('id' => 9, 'name' => 'Lublin', 'city' => 'Lublin', 'region' => 'lubelskie'),
                (object) array('id' => 10, 'name' => 'Foto-Nowak', 'city' => 'Dąbrowa Górnicza', 'region' => 'śląskie'),
            );

            $filtered_places = array();
            foreach ($sample_places as $place) {
                if (stripos($place->name, $search_term_clean) !== false ||
                    stripos($place->city, $search_term_clean) !== false) {
                    $filtered_places[] = $place;
                }
            }

            error_log("RSS Aggregator: Found " . count($filtered_places) . " sample places matching '{$search_term_clean}'");
            return array_slice($filtered_places, 0, $limit);
        }

        error_log("RSS Aggregator: Found " . count($results) . " GeoDirectory places");
        return $results;
    }

    /**
     * Search GeoDirectory places in posts table
     */
    private function search_geodirectory_posts($search_term, $limit = 10) {
        global $wpdb;

        // Debug: Check what tables exist with our prefix
        $tables_query = "SHOW TABLES LIKE '{$wpdb->prefix}%'";
        $tables = $wpdb->get_col($tables_query);
        error_log("RSS Aggregator: Available tables with prefix '{$wpdb->prefix}': " . implode(', ', $tables));

        // Try different GeoDirectory post types
        $post_types = array('gd_place', 'geodir_place', 'place', 'listing');

        // First, check if any GeoDirectory posts exist
        $post_type_placeholders = implode(',', array_fill(0, count($post_types), '%s'));
        $check_sql = "SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type IN ({$post_type_placeholders}) AND post_status = 'publish'";
        $check_params = $post_types;
        $gd_posts_count = $wpdb->get_var($wpdb->prepare($check_sql, $check_params));

        error_log("RSS Aggregator: Found {$gd_posts_count} GeoDirectory posts in database");

        if ($gd_posts_count > 0) {
            // Build the main query
            $post_type_conditions = array();
            foreach ($post_types as $type) {
                $post_type_conditions[] = $wpdb->prepare("p.post_type = %s", $type);
            }
            $post_type_sql = '(' . implode(' OR ', $post_type_conditions) . ')';

            $sql = "SELECT p.ID as id, p.post_title as name,
                           COALESCE(pm1.meta_value, '') as city,
                           COALESCE(pm2.meta_value, '') as region
                    FROM {$wpdb->posts} p
                    LEFT JOIN {$wpdb->postmeta} pm1 ON p.ID = pm1.post_id AND pm1.meta_key IN ('geodir_city', 'city', '_city')
                    LEFT JOIN {$wpdb->postmeta} pm2 ON p.ID = pm2.post_id AND pm2.meta_key IN ('geodir_region', 'region', '_region', 'geodir_state', 'state', '_state')
                    WHERE {$post_type_sql}
                    AND p.post_title LIKE %s
                    AND p.post_status = 'publish'
                    ORDER BY p.post_title ASC
                    LIMIT %d";

            $results = $wpdb->get_results($wpdb->prepare($sql, $search_term, $limit));
            error_log("RSS Aggregator: GeoDirectory query returned " . count($results) . " results");

            return $results ? $results : array();
        }

        error_log("RSS Aggregator: No GeoDirectory posts found, returning empty array");
        return array();
    }

    /**
     * Search WordPress users for autocomplete
     */
    public function search_users($search_term, $limit = 10) {
        $search_term = '%' . $this->wpdb->esc_like($search_term) . '%';

        $results = $this->wpdb->get_results($this->wpdb->prepare(
            "SELECT ID as id, display_name as name, user_login as login, user_email as email
             FROM {$this->wpdb->users}
             WHERE (display_name LIKE %s OR user_login LIKE %s OR user_email LIKE %s)
             AND user_status = 0
             ORDER BY display_name ASC
             LIMIT %d",
            $search_term,
            $search_term,
            $search_term,
            $limit
        ));

        return $results;
    }

    /**
     * Search counties for autocomplete from GeoDirectory
     */
    public function search_counties($search_term, $limit = 10) {
        global $wpdb;

        $search_term_clean = trim($search_term);
        $search_term_like = '%' . $wpdb->esc_like($search_term_clean) . '%';
        $results = array();

        error_log("RSS Aggregator: Searching counties for: '{$search_term_clean}' with prefix: '{$wpdb->prefix}'");

        // Search in GeoDirectory place details table
        $geodir_table = $wpdb->prefix . 'geodir_gd_place_detail';

        // Check if GeoDirectory table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '{$geodir_table}'") == $geodir_table) {
            error_log("RSS Aggregator: Found GeoDirectory table: {$geodir_table}");

            // Get counties from powiaty_uslugi column
            $geodir_results = $wpdb->get_results($wpdb->prepare(
                "SELECT DISTINCT powiaty_uslugi as counties_raw, region
                 FROM {$geodir_table}
                 WHERE powiaty_uslugi IS NOT NULL
                 AND powiaty_uslugi != ''
                 AND powiaty_uslugi LIKE %s
                 ORDER BY powiaty_uslugi ASC
                 LIMIT %d",
                $search_term_like,
                $limit * 3 // Get more to account for splitting
            ));

            error_log("RSS Aggregator: Found " . count($geodir_results) . " raw county records");

            // Process comma-separated counties
            foreach ($geodir_results as $row) {
                if (!empty($row->counties_raw)) {
                    $counties = array_map('trim', explode(',', $row->counties_raw));
                    foreach ($counties as $county) {
                        if (!empty($county) && stripos($county, $search_term_clean) !== false) {
                            $results[] = (object) array(
                                'name' => $county,
                                'region' => $row->region ?? 'Polska'
                            );
                        }
                    }
                }
            }

            error_log("RSS Aggregator: Processed " . count($results) . " counties from GeoDirectory");
        } else {
            error_log("RSS Aggregator: GeoDirectory table not found: {$geodir_table}");
        }

        // If no results from GeoDirectory, add common Polish counties
        if (empty($results)) {
            $polish_counties = array(
                'warszawski', 'krakowski', 'gdański', 'wrocławski', 'poznański',
                'łódzki', 'katowicki', 'bydgoski', 'lubelski', 'białostocki',
                'rzeszowski', 'szczeciński', 'olsztyński', 'zielonogórski',
                'kielecki', 'opolski', 'gorzowski', 'toruński', 'płocki',
                'radomski', 'częstochowski', 'sosnowiecki', 'bytomski',
                'dąbrowski', 'będziński', 'zawierciański', 'myszkowski'
            );

            foreach ($polish_counties as $county) {
                if (stripos($county, $search_term_clean) !== false) {
                    $results[] = (object) array(
                        'name' => $county,
                        'region' => 'Polska'
                    );
                }
            }

            error_log("RSS Aggregator: Added " . count($results) . " fallback Polish counties");
        }

        // Remove duplicates and limit
        $unique_results = array();
        $seen = array();

        foreach ($results as $result) {
            $key = strtolower($result->name);
            if (!isset($seen[$key])) {
                $seen[$key] = true;
                $unique_results[] = $result;

                if (count($unique_results) >= $limit) {
                    break;
                }
            }
        }

        error_log("RSS Aggregator: Returning " . count($unique_results) . " unique counties");
        return $unique_results;
    }

    /**
     * Delete all posts associated with a feed
     *
     * @param int $feed_id Feed ID
     * @return array Result with counts
     */
    public function delete_feed_posts($feed_id) {
        $feed_id = intval($feed_id);

        if (empty($feed_id)) {
            return array('success' => false, 'message' => 'Invalid feed ID');
        }

        // Get all items for this feed
        $items = $this->wpdb->get_results($this->wpdb->prepare(
            "SELECT id, post_id FROM " . RSS_AGGREGATOR_ITEMS_TABLE . "
             WHERE feed_id = %d AND post_id IS NOT NULL",
            $feed_id
        ));

        $deleted_posts = 0;
        $deleted_items = 0;
        $errors = array();

        foreach ($items as $item) {
            if (!empty($item->post_id)) {
                // Delete WordPress post and all its metadata
                $result = wp_delete_post($item->post_id, true); // true = force delete, skip trash

                if ($result) {
                    $deleted_posts++;

                    // Delete post meta
                    $this->wpdb->delete($this->wpdb->postmeta, array('post_id' => $item->post_id));

                    // Delete from any taxonomy relationships
                    $this->wpdb->delete($this->wpdb->term_relationships, array('object_id' => $item->post_id));

                } else {
                    $errors[] = "Failed to delete post ID: {$item->post_id}";
                }
            }

            // Delete RSS item from our table
            $item_deleted = $this->wpdb->delete(
                RSS_AGGREGATOR_ITEMS_TABLE,
                array('id' => $item->id),
                array('%d')
            );

            if ($item_deleted) {
                $deleted_items++;
            } else {
                $errors[] = "Failed to delete RSS item ID: {$item->id}";
            }
        }

        // Clean up any orphaned items (items without post_id)
        $orphaned_deleted = $this->wpdb->delete(
            RSS_AGGREGATOR_ITEMS_TABLE,
            array('feed_id' => $feed_id),
            array('%d')
        );

        if ($orphaned_deleted) {
            $deleted_items += $orphaned_deleted;
        }

        return array(
            'success' => true,
            'deleted_posts' => $deleted_posts,
            'deleted_items' => $deleted_items,
            'errors' => $errors,
            'message' => sprintf(
                'Deleted %d posts and %d RSS items. %s',
                $deleted_posts,
                $deleted_items,
                !empty($errors) ? count($errors) . ' errors occurred.' : ''
            )
        );
    }

    /**
     * Search regions/voivodeships for autocomplete from GeoDirectory
     */
    public function search_regions($search_term, $limit = 10) {
        global $wpdb;

        $search_term_clean = trim($search_term);
        $search_term_like = '%' . $wpdb->esc_like($search_term_clean) . '%';
        $results = array();

        error_log("RSS Aggregator: Searching regions for: '{$search_term_clean}' with prefix: '{$wpdb->prefix}'");

        // Search in GeoDirectory place details table
        $geodir_table = $wpdb->prefix . 'geodir_gd_place_detail';

        // Check if GeoDirectory table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '{$geodir_table}'") == $geodir_table) {
            error_log("RSS Aggregator: Found GeoDirectory table: {$geodir_table}");

            // Get regions from region column
            $geodir_results = $wpdb->get_results($wpdb->prepare(
                "SELECT DISTINCT region as name, region
                 FROM {$geodir_table}
                 WHERE region IS NOT NULL
                 AND region != ''
                 AND region LIKE %s
                 ORDER BY region ASC
                 LIMIT %d",
                $search_term_like,
                $limit
            ));

            foreach ($geodir_results as $row) {
                if (!empty($row->name)) {
                    $results[] = (object) array(
                        'name' => $row->name,
                        'region' => $row->region
                    );
                }
            }

            error_log("RSS Aggregator: Found " . count($results) . " regions from GeoDirectory");
        } else {
            error_log("RSS Aggregator: GeoDirectory table not found: {$geodir_table}");
        }

        // If no results from GeoDirectory, add Polish voivodeships
        if (empty($results)) {
            $polish_regions = array(
                'dolnośląskie', 'kujawsko-pomorskie', 'lubelskie', 'lubuskie',
                'łódzkie', 'małopolskie', 'mazowieckie', 'opolskie',
                'podkarpackie', 'podlaskie', 'pomorskie', 'śląskie',
                'świętokrzyskie', 'warmińsko-mazurskie', 'wielkopolskie', 'zachodniopomorskie'
            );

            foreach ($polish_regions as $region) {
                if (stripos($region, $search_term_clean) !== false) {
                    $results[] = (object) array(
                        'name' => $region,
                        'region' => $region
                    );
                }
            }

            error_log("RSS Aggregator: Added " . count($results) . " fallback Polish regions");
        }

        // Remove duplicates and limit
        $unique_results = array();
        $seen = array();

        foreach ($results as $result) {
            $key = strtolower($result->name);
            if (!isset($seen[$key])) {
                $seen[$key] = true;
                $unique_results[] = $result;

                if (count($unique_results) >= $limit) {
                    break;
                }
            }
        }

        error_log("RSS Aggregator: Returning " . count($unique_results) . " unique regions");
        return $unique_results;
    }

    /**
     * Get posts count for a feed
     *
     * @param int $feed_id Feed ID
     * @return int Posts count
     */
    public function get_feed_posts_count($feed_id) {
        return $this->wpdb->get_var($this->wpdb->prepare(
            "SELECT COUNT(*) FROM " . RSS_AGGREGATOR_ITEMS_TABLE . "
             WHERE feed_id = %d AND post_id IS NOT NULL",
            intval($feed_id)
        ));
    }

    /**
     * Check if GeoDirectory is active
     */
    private function is_geodirectory_active() {
        return class_exists('GeoDirectory') || function_exists('geodir_load_translation');
    }
}
