<?php
/**
 * RSS Database handler class
 *
 * @package RSS_Aggregator
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * RSS Database class
 */
class RSS_Database {
    
    /**
     * WordPress database object
     */
    private $wpdb;
    
    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        $this->wpdb = $wpdb;

        // Define table names if not already defined
        if (!defined('RSS_AGGREGATOR_FEEDS_TABLE')) {
            define('RSS_AGGREGATOR_FEEDS_TABLE', $wpdb->prefix . 'rss_aggregator_feeds');
        }
        if (!defined('RSS_AGGREGATOR_ITEMS_TABLE')) {
            define('RSS_AGGREGATOR_ITEMS_TABLE', $wpdb->prefix . 'rss_aggregator_items');
        }
    }
    
    /**
     * Get all feeds
     *
     * @param array $args Query arguments
     * @return array Feeds
     */
    public function get_feeds($args = array()) {
        $defaults = array(
            'status' => 'active',
            'limit' => 0,
            'offset' => 0,
            'orderby' => 'name',
            'order' => 'ASC'
        );
        
        $args = wp_parse_args($args, $defaults);
        
        $sql = "SELECT * FROM " . RSS_AGGREGATOR_FEEDS_TABLE;
        $where_conditions = array();
        $values = array();
        
        // Status filter
        if (!empty($args['status'])) {
            $where_conditions[] = "status = %s";
            $values[] = $args['status'];
        }
        
        // County filter
        if (!empty($args['county'])) {
            $where_conditions[] = "county = %s";
            $values[] = $args['county'];
        }
        
        // Build WHERE clause
        if (!empty($where_conditions)) {
            $sql .= " WHERE " . implode(' AND ', $where_conditions);
        }
        
        // Order by
        $allowed_orderby = array('id', 'name', 'created_at', 'updated_at', 'last_updated');
        if (in_array($args['orderby'], $allowed_orderby)) {
            $sql .= " ORDER BY " . $args['orderby'];
            $sql .= ($args['order'] === 'DESC') ? ' DESC' : ' ASC';
        }
        
        // Limit
        if ($args['limit'] > 0) {
            $sql .= " LIMIT " . intval($args['limit']);
            if ($args['offset'] > 0) {
                $sql .= " OFFSET " . intval($args['offset']);
            }
        }
        
        if (!empty($values)) {
            return $this->wpdb->get_results($this->wpdb->prepare($sql, $values));
        } else {
            return $this->wpdb->get_results($sql);
        }
    }
    
    /**
     * Get single feed by ID
     *
     * @param int $feed_id Feed ID
     * @return object|null Feed object
     */
    public function get_feed($feed_id) {
        return $this->wpdb->get_row($this->wpdb->prepare(
            "SELECT * FROM " . RSS_AGGREGATOR_FEEDS_TABLE . " WHERE id = %d",
            $feed_id
        ));
    }
    
    /**
     * Get feed by URL
     *
     * @param string $url Feed URL
     * @return object|null Feed object
     */
    public function get_feed_by_url($url) {
        return $this->wpdb->get_row($this->wpdb->prepare(
            "SELECT * FROM " . RSS_AGGREGATOR_FEEDS_TABLE . " WHERE url = %s",
            $url
        ));
    }
    
    /**
     * Get feeds due for update
     *
     * @return array Feeds that need updating
     */
    public function get_feeds_due_for_update() {
        $sql = "SELECT * FROM " . RSS_AGGREGATOR_FEEDS_TABLE . " 
                WHERE status = 'active' 
                AND (
                    last_updated IS NULL 
                    OR (
                        update_frequency = 'every_15_minutes' AND last_updated < DATE_SUB(NOW(), INTERVAL 15 MINUTE)
                    ) OR (
                        update_frequency = 'every_30_minutes' AND last_updated < DATE_SUB(NOW(), INTERVAL 30 MINUTE)
                    ) OR (
                        update_frequency = 'hourly' AND last_updated < DATE_SUB(NOW(), INTERVAL 1 HOUR)
                    ) OR (
                        update_frequency = 'every_2_hours' AND last_updated < DATE_SUB(NOW(), INTERVAL 2 HOUR)
                    ) OR (
                        update_frequency = 'every_6_hours' AND last_updated < DATE_SUB(NOW(), INTERVAL 6 HOUR)
                    ) OR (
                        update_frequency = 'daily' AND last_updated < DATE_SUB(NOW(), INTERVAL 1 DAY)
                    )
                )";
        
        return $this->wpdb->get_results($sql);
    }
    
    /**
     * Save feed
     *
     * @param array $data Feed data
     * @return int|false Feed ID on success, false on failure
     */
    public function save_feed($data) {
        $defaults = array(
            'name' => '',
            'url' => '',
            'geodirectory_place_id' => null,
            'county' => '',
            'update_frequency' => 'hourly',
            'status' => 'active'
        );
        
        $data = wp_parse_args($data, $defaults);
        
        // Validate required fields
        if (empty($data['name']) || empty($data['url'])) {
            return false;
        }
        
        // Check if updating existing feed
        if (!empty($data['id'])) {
            $result = $this->wpdb->update(
                RSS_AGGREGATOR_FEEDS_TABLE,
                array(
                    'name' => sanitize_text_field($data['name']),
                    'url' => esc_url_raw($data['url']),
                    'geodirectory_place_id' => !empty($data['geodirectory_place_id']) ? intval($data['geodirectory_place_id']) : null,
                    'county' => sanitize_text_field($data['county']),
                    'update_frequency' => sanitize_text_field($data['update_frequency']),
                    'status' => sanitize_text_field($data['status']),
                    'updated_at' => current_time('mysql')
                ),
                array('id' => intval($data['id'])),
                array('%s', '%s', '%d', '%s', '%s', '%s', '%s'),
                array('%d')
            );
            
            return $result !== false ? intval($data['id']) : false;
        } else {
            // Insert new feed
            $result = $this->wpdb->insert(
                RSS_AGGREGATOR_FEEDS_TABLE,
                array(
                    'name' => sanitize_text_field($data['name']),
                    'url' => esc_url_raw($data['url']),
                    'geodirectory_place_id' => !empty($data['geodirectory_place_id']) ? intval($data['geodirectory_place_id']) : null,
                    'county' => sanitize_text_field($data['county']),
                    'update_frequency' => sanitize_text_field($data['update_frequency']),
                    'status' => sanitize_text_field($data['status'])
                ),
                array('%s', '%s', '%d', '%s', '%s', '%s')
            );
            
            return $result !== false ? $this->wpdb->insert_id : false;
        }
    }
    
    /**
     * Delete feed
     *
     * @param int $feed_id Feed ID
     * @return bool Success status
     */
    public function delete_feed($feed_id) {
        // Delete associated items first
        $this->wpdb->delete(
            RSS_AGGREGATOR_ITEMS_TABLE,
            array('feed_id' => $feed_id),
            array('%d')
        );
        
        // Delete feed
        $result = $this->wpdb->delete(
            RSS_AGGREGATOR_FEEDS_TABLE,
            array('id' => $feed_id),
            array('%d')
        );
        
        return $result !== false;
    }
    
    /**
     * Update feed timestamp
     *
     * @param int $feed_id Feed ID
     * @return bool Success status
     */
    public function update_feed_timestamp($feed_id) {
        $result = $this->wpdb->update(
            RSS_AGGREGATOR_FEEDS_TABLE,
            array('last_updated' => current_time('mysql')),
            array('id' => $feed_id),
            array('%s'),
            array('%d')
        );
        
        return $result !== false;
    }
    
    /**
     * Get feed items
     *
     * @param int $feed_id Feed ID
     * @param array $args Query arguments
     * @return array Items
     */
    public function get_items($feed_id = 0, $args = array()) {
        $defaults = array(
            'limit' => 50,
            'offset' => 0,
            'processed' => null,
            'orderby' => 'pub_date',
            'order' => 'DESC'
        );
        
        $args = wp_parse_args($args, $defaults);
        
        $sql = "SELECT * FROM " . RSS_AGGREGATOR_ITEMS_TABLE;
        $where_conditions = array();
        $values = array();
        
        // Feed ID filter
        if ($feed_id > 0) {
            $where_conditions[] = "feed_id = %d";
            $values[] = $feed_id;
        }
        
        // Processed filter
        if ($args['processed'] !== null) {
            $where_conditions[] = "processed = %d";
            $values[] = intval($args['processed']);
        }
        
        // Build WHERE clause
        if (!empty($where_conditions)) {
            $sql .= " WHERE " . implode(' AND ', $where_conditions);
        }
        
        // Order by
        $allowed_orderby = array('id', 'title', 'pub_date', 'created_at');
        if (in_array($args['orderby'], $allowed_orderby)) {
            $sql .= " ORDER BY " . $args['orderby'];
            $sql .= ($args['order'] === 'DESC') ? ' DESC' : ' ASC';
        }
        
        // Limit
        if ($args['limit'] > 0) {
            $sql .= " LIMIT " . intval($args['limit']);
            if ($args['offset'] > 0) {
                $sql .= " OFFSET " . intval($args['offset']);
            }
        }
        
        if (!empty($values)) {
            return $this->wpdb->get_results($this->wpdb->prepare($sql, $values));
        } else {
            return $this->wpdb->get_results($sql);
        }
    }
    
    /**
     * Check if item exists
     *
     * @param int $feed_id Feed ID
     * @param string $guid Item GUID
     * @return bool Item exists
     */
    public function item_exists($feed_id, $guid) {
        $count = $this->wpdb->get_var($this->wpdb->prepare(
            "SELECT COUNT(*) FROM " . RSS_AGGREGATOR_ITEMS_TABLE . " WHERE feed_id = %d AND guid = %s",
            $feed_id,
            $guid
        ));
        
        return $count > 0;
    }
    
    /**
     * Save item
     *
     * @param array $item Item data
     * @param int $feed_id Feed ID
     * @return int|false Item ID on success, false on failure
     */
    public function save_item($item, $feed_id) {
        $result = $this->wpdb->insert(
            RSS_AGGREGATOR_ITEMS_TABLE,
            array(
                'feed_id' => intval($feed_id),
                'title' => sanitize_text_field($item['title']),
                'description' => wp_kses_post($item['description']),
                'url' => esc_url_raw($item['url']),
                'guid' => sanitize_text_field($item['guid']),
                'pub_date' => !empty($item['pub_date']) ? date('Y-m-d H:i:s', strtotime($item['pub_date'])) : current_time('mysql'),
                'thumbnail_url' => !empty($item['thumbnail_url']) ? esc_url_raw($item['thumbnail_url']) : null,
                'processed' => 0
            ),
            array('%d', '%s', '%s', '%s', '%s', '%s', '%s', '%d')
        );
        
        return $result !== false ? $this->wpdb->insert_id : false;
    }
    
    /**
     * Update item post ID
     *
     * @param int $item_id Item ID
     * @param int $post_id Post ID
     * @return bool Success status
     */
    public function update_item_post_id($item_id, $post_id) {
        $result = $this->wpdb->update(
            RSS_AGGREGATOR_ITEMS_TABLE,
            array(
                'post_id' => intval($post_id),
                'processed' => 1
            ),
            array('id' => $item_id),
            array('%d', '%d'),
            array('%d')
        );
        
        return $result !== false;
    }
    
    /**
     * Get statistics
     *
     * @return array Statistics
     */
    public function get_statistics() {
        $stats = array();
        
        // Total feeds
        $stats['total_feeds'] = $this->wpdb->get_var(
            "SELECT COUNT(*) FROM " . RSS_AGGREGATOR_FEEDS_TABLE
        );
        
        // Active feeds
        $stats['active_feeds'] = $this->wpdb->get_var(
            "SELECT COUNT(*) FROM " . RSS_AGGREGATOR_FEEDS_TABLE . " WHERE status = 'active'"
        );
        
        // Total items
        $stats['total_items'] = $this->wpdb->get_var(
            "SELECT COUNT(*) FROM " . RSS_AGGREGATOR_ITEMS_TABLE
        );
        
        // Processed items
        $stats['processed_items'] = $this->wpdb->get_var(
            "SELECT COUNT(*) FROM " . RSS_AGGREGATOR_ITEMS_TABLE . " WHERE processed = 1"
        );
        
        // Items from last 24 hours
        $stats['recent_items'] = $this->wpdb->get_var(
            "SELECT COUNT(*) FROM " . RSS_AGGREGATOR_ITEMS_TABLE . " WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 DAY)"
        );
        
        return $stats;
    }
    
    /**
     * Clean old items
     *
     * @param int $days_old Days to keep items
     * @return int Number of deleted items
     */
    public function clean_old_items($days_old = 30) {
        $result = $this->wpdb->query($this->wpdb->prepare(
            "DELETE FROM " . RSS_AGGREGATOR_ITEMS_TABLE . " WHERE created_at < DATE_SUB(NOW(), INTERVAL %d DAY)",
            $days_old
        ));
        
        return $result !== false ? $result : 0;
    }
    
    /**
     * Get counties with feed counts
     *
     * @return array Counties
     */
    public function get_counties_with_counts() {
        return $this->wpdb->get_results(
            "SELECT county, COUNT(*) as feed_count 
             FROM " . RSS_AGGREGATOR_FEEDS_TABLE . " 
             WHERE county != '' AND status = 'active'
             GROUP BY county 
             ORDER BY county ASC"
        );
    }
}
