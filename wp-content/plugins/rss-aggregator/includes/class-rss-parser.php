<?php
/**
 * RSS Parser class
 *
 * @package RSS_Aggregator
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * RSS Parser class
 */
class RSS_Parser {
    
    /**
     * RSS Fetcher instance
     */
    private $fetcher;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->fetcher = new RSS_Fetcher();
    }
    
    /**
     * Parse RSS feed content
     *
     * @param string $content RSS content
     * @param object $feed Feed object
     * @return array Parsed items
     */
    public function parse_feed($content, $feed = null) {
        if (empty($content)) {
            return array();
        }
        
        // Disable libxml errors
        $use_errors = libxml_use_internal_errors(true);
        
        // Parse XML
        $xml = simplexml_load_string($content);
        
        if ($xml === false) {
            libxml_use_internal_errors($use_errors);
            return array();
        }
        
        $items = array();
        
        // Detect feed format and parse accordingly
        if (isset($xml->channel)) {
            // RSS 2.0
            $items = $this->parse_rss_items($xml->channel, $feed);
        } elseif ($xml->getName() === 'feed') {
            // Atom
            $items = $this->parse_atom_items($xml, $feed);
        } elseif (isset($xml->item)) {
            // RDF/RSS 1.0
            $items = $this->parse_rdf_items($xml, $feed);
        }
        
        // Restore error handling
        libxml_use_internal_errors($use_errors);
        
        // Limit items per feed
        $max_items = get_option('rss_aggregator_max_items_per_feed', 50);
        if (count($items) > $max_items) {
            $items = array_slice($items, 0, $max_items);
        }
        
        return $items;
    }
    
    /**
     * Parse RSS 2.0 items
     *
     * @param SimpleXMLElement $channel RSS channel
     * @param object $feed Feed object
     * @return array Parsed items
     */
    private function parse_rss_items($channel, $feed) {
        $items = array();
        
        if (!isset($channel->item)) {
            return $items;
        }
        
        foreach ($channel->item as $item) {
            $parsed_item = array(
                'title' => $this->clean_text((string)$item->title),
                'description' => $this->clean_html((string)$item->description),
                'url' => $this->clean_url((string)$item->link),
                'guid' => $this->get_guid($item),
                'pub_date' => $this->parse_date((string)$item->pubDate),
                'thumbnail_url' => $this->extract_thumbnail_from_rss_item($item)
            );
            
            // If no thumbnail found in RSS, try to extract from page
            if (empty($parsed_item['thumbnail_url']) && !empty($parsed_item['url'])) {
                $parsed_item['thumbnail_url'] = $this->extract_thumbnail_from_page($parsed_item['url']);
            }
            
            // Skip items without required fields
            if (empty($parsed_item['title']) || empty($parsed_item['url'])) {
                continue;
            }
            
            $items[] = $parsed_item;
        }
        
        return $items;
    }
    
    /**
     * Parse Atom items
     *
     * @param SimpleXMLElement $feed Atom feed
     * @param object $feed_obj Feed object
     * @return array Parsed items
     */
    private function parse_atom_items($feed, $feed_obj) {
        $items = array();
        
        if (!isset($feed->entry)) {
            return $items;
        }
        
        foreach ($feed->entry as $entry) {
            $link = '';
            if (isset($entry->link)) {
                if (is_array($entry->link)) {
                    foreach ($entry->link as $l) {
                        if ((string)$l['type'] === 'text/html') {
                            $link = (string)$l['href'];
                            break;
                        }
                    }
                    if (empty($link)) {
                        $link = (string)$entry->link[0]['href'];
                    }
                } else {
                    $link = (string)$entry->link['href'];
                }
            }
            
            $content = '';
            if (isset($entry->content)) {
                $content = (string)$entry->content;
            } elseif (isset($entry->summary)) {
                $content = (string)$entry->summary;
            }
            
            $parsed_item = array(
                'title' => $this->clean_text((string)$entry->title),
                'description' => $this->clean_html($content),
                'url' => $this->clean_url($link),
                'guid' => (string)$entry->id,
                'pub_date' => $this->parse_date((string)$entry->published ?: (string)$entry->updated),
                'thumbnail_url' => $this->extract_thumbnail_from_atom_entry($entry)
            );
            
            // If no thumbnail found in Atom, try to extract from page
            if (empty($parsed_item['thumbnail_url']) && !empty($parsed_item['url'])) {
                $parsed_item['thumbnail_url'] = $this->extract_thumbnail_from_page($parsed_item['url']);
            }
            
            // Skip items without required fields
            if (empty($parsed_item['title']) || empty($parsed_item['url'])) {
                continue;
            }
            
            $items[] = $parsed_item;
        }
        
        return $items;
    }
    
    /**
     * Parse RDF items
     *
     * @param SimpleXMLElement $rdf RDF feed
     * @param object $feed Feed object
     * @return array Parsed items
     */
    private function parse_rdf_items($rdf, $feed) {
        $items = array();
        
        if (!isset($rdf->item)) {
            return $items;
        }
        
        foreach ($rdf->item as $item) {
            $parsed_item = array(
                'title' => $this->clean_text((string)$item->title),
                'description' => $this->clean_html((string)$item->description),
                'url' => $this->clean_url((string)$item->link),
                'guid' => (string)$item->link, // RDF usually doesn't have GUID
                'pub_date' => $this->parse_date((string)$item->children('http://purl.org/dc/elements/1.1/')->date),
                'thumbnail_url' => null
            );
            
            // Try to extract thumbnail from page
            if (!empty($parsed_item['url'])) {
                $parsed_item['thumbnail_url'] = $this->extract_thumbnail_from_page($parsed_item['url']);
            }
            
            // Skip items without required fields
            if (empty($parsed_item['title']) || empty($parsed_item['url'])) {
                continue;
            }
            
            $items[] = $parsed_item;
        }
        
        return $items;
    }
    
    /**
     * Extract thumbnail from RSS item
     *
     * @param SimpleXMLElement $item RSS item
     * @return string|null Thumbnail URL
     */
    private function extract_thumbnail_from_rss_item($item) {
        // Check for media:thumbnail
        $media_ns = $item->children('http://search.yahoo.com/mrss/');
        if (isset($media_ns->thumbnail)) {
            return (string)$media_ns->thumbnail['url'];
        }
        
        // Check for media:content
        if (isset($media_ns->content)) {
            $content = $media_ns->content;
            if ((string)$content['medium'] === 'image' || str_starts_with((string)$content['type'], 'image/')) {
                return (string)$content['url'];
            }
        }
        
        // Check for enclosure
        if (isset($item->enclosure)) {
            $enclosure = $item->enclosure;
            if (str_starts_with((string)$enclosure['type'], 'image/')) {
                return (string)$enclosure['url'];
            }
        }
        
        // Check for image in description
        $description = (string)$item->description;
        if (!empty($description)) {
            return $this->extract_image_from_html($description);
        }
        
        return null;
    }
    
    /**
     * Extract thumbnail from Atom entry
     *
     * @param SimpleXMLElement $entry Atom entry
     * @return string|null Thumbnail URL
     */
    private function extract_thumbnail_from_atom_entry($entry) {
        // Check for link with rel="enclosure" and image type
        if (isset($entry->link)) {
            foreach ($entry->link as $link) {
                if ((string)$link['rel'] === 'enclosure' && str_starts_with((string)$link['type'], 'image/')) {
                    return (string)$link['href'];
                }
            }
        }
        
        // Check for image in content
        $content = (string)$entry->content ?: (string)$entry->summary;
        if (!empty($content)) {
            return $this->extract_image_from_html($content);
        }
        
        return null;
    }
    
    /**
     * Extract thumbnail from page content
     *
     * @param string $page_url Page URL
     * @return string|null Thumbnail URL
     */
    private function extract_thumbnail_from_page($page_url) {
        if (!get_option('rss_aggregator_enable_thumbnails', 1)) {
            return null;
        }
        
        $page_content = $this->fetcher->fetch_page_content($page_url);
        
        if ($page_content === false) {
            return null;
        }
        
        // Try to extract from meta tags first
        $thumbnail = $this->extract_thumbnail_from_meta_tags($page_content);
        
        if (!empty($thumbnail)) {
            return $this->resolve_relative_url($thumbnail, $page_url);
        }
        
        // Try to extract from first image in content
        $thumbnail = $this->extract_image_from_html($page_content);
        
        if (!empty($thumbnail)) {
            return $this->resolve_relative_url($thumbnail, $page_url);
        }
        
        return null;
    }
    
    /**
     * Extract thumbnail from meta tags
     *
     * @param string $html HTML content
     * @return string|null Thumbnail URL
     */
    private function extract_thumbnail_from_meta_tags($html) {
        // Try og:image first
        if (preg_match('/<meta[^>]+property=["\']og:image["\'][^>]+content=["\']([^"\']+)["\'][^>]*>/i', $html, $matches)) {
            return $matches[1];
        }
        
        // Try twitter:image
        if (preg_match('/<meta[^>]+name=["\']twitter:image["\'][^>]+content=["\']([^"\']+)["\'][^>]*>/i', $html, $matches)) {
            return $matches[1];
        }
        
        // Try article:image
        if (preg_match('/<meta[^>]+property=["\']article:image["\'][^>]+content=["\']([^"\']+)["\'][^>]*>/i', $html, $matches)) {
            return $matches[1];
        }
        
        return null;
    }
    
    /**
     * Extract image from HTML content
     *
     * @param string $html HTML content
     * @return string|null Image URL
     */
    private function extract_image_from_html($html) {
        if (preg_match('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $html, $matches)) {
            return $matches[1];
        }
        
        return null;
    }
    
    /**
     * Get GUID from RSS item
     *
     * @param SimpleXMLElement $item RSS item
     * @return string GUID
     */
    private function get_guid($item) {
        if (isset($item->guid)) {
            return (string)$item->guid;
        }
        
        // Fallback to link if no GUID
        return (string)$item->link;
    }
    
    /**
     * Parse date string
     *
     * @param string $date_string Date string
     * @return string|null Formatted date
     */
    private function parse_date($date_string) {
        if (empty($date_string)) {
            return null;
        }
        
        $timestamp = strtotime($date_string);
        
        if ($timestamp === false) {
            return null;
        }
        
        return date('Y-m-d H:i:s', $timestamp);
    }
    
    /**
     * Clean text content
     *
     * @param string $text Text to clean
     * @return string Cleaned text
     */
    private function clean_text($text) {
        $text = html_entity_decode($text, ENT_QUOTES, 'UTF-8');
        $text = strip_tags($text);
        $text = trim($text);
        
        return $text;
    }
    
    /**
     * Clean HTML content
     *
     * @param string $html HTML to clean
     * @return string Cleaned HTML
     */
    private function clean_html($html) {
        $html = html_entity_decode($html, ENT_QUOTES, 'UTF-8');
        
        // Remove script and style tags
        $html = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $html);
        $html = preg_replace('/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/mi', '', $html);
        
        // Clean with WordPress function
        $html = wp_kses_post($html);
        
        return trim($html);
    }
    
    /**
     * Clean URL
     *
     * @param string $url URL to clean
     * @return string Cleaned URL
     */
    private function clean_url($url) {
        $url = trim($url);
        
        if (empty($url)) {
            return '';
        }
        
        // Add protocol if missing
        if (!preg_match('/^https?:\/\//', $url)) {
            $url = 'http://' . $url;
        }
        
        return esc_url_raw($url);
    }
    
    /**
     * Resolve relative URL to absolute
     *
     * @param string $url Relative or absolute URL
     * @param string $base_url Base URL
     * @return string Absolute URL
     */
    private function resolve_relative_url($url, $base_url) {
        if (empty($url)) {
            return '';
        }
        
        // Already absolute
        if (preg_match('/^https?:\/\//', $url)) {
            return $url;
        }
        
        $base_parts = parse_url($base_url);
        
        if ($base_parts === false) {
            return $url;
        }
        
        $scheme = $base_parts['scheme'];
        $host = $base_parts['host'];
        $port = isset($base_parts['port']) ? ':' . $base_parts['port'] : '';
        
        // Protocol relative
        if (str_starts_with($url, '//')) {
            return $scheme . ':' . $url;
        }
        
        // Absolute path
        if (str_starts_with($url, '/')) {
            return $scheme . '://' . $host . $port . $url;
        }
        
        // Relative path
        $path = isset($base_parts['path']) ? dirname($base_parts['path']) : '';
        $path = rtrim($path, '/');
        
        return $scheme . '://' . $host . $port . $path . '/' . $url;
    }
}
