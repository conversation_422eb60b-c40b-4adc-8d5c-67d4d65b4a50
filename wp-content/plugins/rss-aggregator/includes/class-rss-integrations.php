<?php
/**
 * RSS Integrations class for BuddyBoss and GeoDirectory
 *
 * @package RSS_Aggregator
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * RSS Integrations class
 */
class RSS_Integrations {
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init();
    }
    
    /**
     * Initialize integrations
     */
    private function init() {
        // BuddyBoss integration
        if ($this->is_buddyboss_active()) {
            $this->init_buddyboss_integration();
        }
        
        // GeoDirectory integration
        if ($this->is_geodirectory_active()) {
            $this->init_geodirectory_integration();
        }
    }
    
    /**
     * Check if <PERSON><PERSON><PERSON> is active
     *
     * @return bool
     */
    private function is_buddyboss_active() {
        return class_exists('BuddyPress') && function_exists('bp_activity_add');
    }
    
    /**
     * Check if GeoDirectory is active
     *
     * @return bool
     */
    private function is_geodirectory_active() {
        return class_exists('GeoDirectory') && function_exists('geodir_get_posttypes');
    }
    
    /**
     * Initialize BuddyBoss integration
     */
    private function init_buddyboss_integration() {
        // Hook into post publication for activity stream
        add_action('transition_post_status', array($this, 'handle_buddyboss_activity'), 10, 3);
        
        // Modify activity content for RSS posts
        add_filter('bp_get_activity_content_body', array($this, 'modify_activity_content'), 10, 1);
        
        // Add RSS source info to activity
        add_filter('bp_activity_get_activity_id', array($this, 'modify_activity_action'), 10, 1);
    }
    
    /**
     * Initialize GeoDirectory integration
     */
    private function init_geodirectory_integration() {
        // Add custom fields for RSS integration
        add_action('init', array($this, 'add_geodirectory_custom_fields'));
        
        // Hook into place creation/update
        add_action('geodir_post_saved', array($this, 'handle_geodirectory_place_update'), 10, 2);
    }
    
    /**
     * Handle new RSS post for integrations
     *
     * @param int $post_id Post ID
     * @param object $feed Feed object
     * @param array $item RSS item data
     */
    public function handle_new_post($post_id, $feed, $item) {
        // BuddyBoss integration
        if ($this->is_buddyboss_active() && get_option('rss_aggregator_enable_buddyboss', 1)) {
            $this->create_buddyboss_activity($post_id, $feed, $item);
        }
        
        // GeoDirectory integration
        if ($this->is_geodirectory_active() && get_option('rss_aggregator_enable_geodirectory', 1)) {
            $this->link_to_geodirectory_place($post_id, $feed);
        }
    }
    
    /**
     * Create BuddyBoss activity for RSS post
     *
     * @param int $post_id Post ID
     * @param object $feed Feed object
     * @param array $item RSS item data
     */
    private function create_buddyboss_activity($post_id, $feed, $item) {
        $post = get_post($post_id);
        
        if (!$post || $post->post_status !== 'publish') {
            return;
        }
        
        // Create activity action
        $user_link = bp_core_get_userlink($post->post_author);
        $post_link = '<a href="' . get_permalink($post_id) . '">' . $post->post_title . '</a>';
        $source_name = !empty($feed->name) ? $feed->name : __('RSS Feed', 'rss-aggregator');
        
        $action = sprintf(
            __('%1$s shared news from %2$s: %3$s', 'rss-aggregator'),
            $user_link,
            '<strong>' . esc_html($source_name) . '</strong>',
            $post_link
        );
        
        // Prepare activity content
        $content = $this->prepare_activity_content($post, $item);
        
        // Create activity
        $activity_id = bp_activity_add(array(
            'action' => $action,
            'content' => $content,
            'component' => 'blogs',
            'type' => 'new_blog_post',
            'primary_link' => get_permalink($post_id),
            'user_id' => $post->post_author,
            'item_id' => get_current_blog_id(),
            'secondary_item_id' => $post_id,
            'recorded_time' => $post->post_date_gmt,
            'hide_sitewide' => false
        ));
        
        if ($activity_id) {
            // Add meta data
            bp_activity_update_meta($activity_id, 'rss_aggregator_feed_id', $feed->id);
            bp_activity_update_meta($activity_id, 'rss_aggregator_source_url', $item['url']);
            bp_activity_update_meta($activity_id, 'rss_aggregator_county', $feed->county);
            
            // Add thumbnail meta if available
            if (!empty($item['thumbnail_url'])) {
                bp_activity_update_meta($activity_id, 'rss_aggregator_thumbnail', $item['thumbnail_url']);
            }
        }
    }
    
    /**
     * Prepare activity content with thumbnail and excerpt
     *
     * @param WP_Post $post Post object
     * @param array $item RSS item data
     * @return string Activity content
     */
    private function prepare_activity_content($post, $item) {
        $content = '';
        
        // Add thumbnail if available
        if (has_post_thumbnail($post->ID)) {
            $thumbnail = get_the_post_thumbnail($post->ID, 'medium', array(
                'class' => 'rss-aggregator-activity-thumbnail'
            ));
            $content .= '<div class="rss-aggregator-thumbnail">' . $thumbnail . '</div>';
        }
        
        // Add excerpt
        $excerpt = get_the_excerpt($post->ID);
        if (!empty($excerpt)) {
            $content .= '<div class="rss-aggregator-excerpt">' . wpautop($excerpt) . '</div>';
        }
        
        // Add source link
        if (!empty($item['url'])) {
            $content .= '<div class="rss-aggregator-source">';
            $content .= '<a href="' . esc_url($item['url']) . '" target="_blank" rel="noopener">';
            $content .= __('Read original article', 'rss-aggregator');
            $content .= '</a></div>';
        }
        
        return $content;
    }
    
    /**
     * Handle BuddyBoss activity on post status change
     *
     * @param string $new_status New status
     * @param string $old_status Old status
     * @param WP_Post $post Post object
     */
    public function handle_buddyboss_activity($new_status, $old_status, $post) {
        // Only handle RSS aggregated posts
        $feed_id = get_post_meta($post->ID, '_rss_aggregator_feed_id', true);
        if (empty($feed_id)) {
            return;
        }
        
        // Handle publish to unpublish
        if ($old_status === 'publish' && $new_status !== 'publish') {
            $this->remove_buddyboss_activity($post->ID);
        }
        
        // Handle unpublish to publish
        if ($old_status !== 'publish' && $new_status === 'publish') {
            // Get feed and item data
            $database = new RSS_Database();
            $feed = $database->get_feed($feed_id);
            
            if ($feed) {
                $item = array(
                    'url' => get_post_meta($post->ID, '_rss_aggregator_source_url', true),
                    'thumbnail_url' => get_post_meta($post->ID, '_rss_aggregator_thumbnail', true)
                );
                
                $this->create_buddyboss_activity($post->ID, $feed, $item);
            }
        }
    }
    
    /**
     * Remove BuddyBoss activity for post
     *
     * @param int $post_id Post ID
     */
    private function remove_buddyboss_activity($post_id) {
        if (!function_exists('bp_activity_delete_by_item_id')) {
            return;
        }
        
        bp_activity_delete_by_item_id(array(
            'component' => 'blogs',
            'type' => 'new_blog_post',
            'item_id' => get_current_blog_id(),
            'secondary_item_id' => $post_id
        ));
    }
    
    /**
     * Modify activity content for RSS posts
     *
     * @param string $content Activity content
     * @return string Modified content
     */
    public function modify_activity_content($content) {
        global $activities_template;
        
        if (empty($activities_template->activity)) {
            return $content;
        }
        
        $activity = $activities_template->activity;
        
        // Check if this is an RSS activity
        $feed_id = bp_activity_get_meta($activity->id, 'rss_aggregator_feed_id');
        
        if (empty($feed_id)) {
            return $content;
        }
        
        // Add RSS-specific styling
        $content = '<div class="rss-aggregator-activity">' . $content . '</div>';
        
        // Add county badge if available
        $county = bp_activity_get_meta($activity->id, 'rss_aggregator_county');
        if (!empty($county)) {
            $content .= '<div class="rss-aggregator-county-badge">';
            $content .= '<span class="county-label">' . sprintf(__('Powiat %s', 'rss-aggregator'), esc_html($county)) . '</span>';
            $content .= '</div>';
        }
        
        return $content;
    }
    
    /**
     * Link RSS post to GeoDirectory place
     *
     * @param int $post_id Post ID
     * @param object $feed Feed object
     */
    private function link_to_geodirectory_place($post_id, $feed) {
        if (empty($feed->geodirectory_place_id)) {
            return;
        }
        
        // Verify the place exists
        $place = get_post($feed->geodirectory_place_id);
        if (!$place || !geodir_is_gd_post_type($place->post_type)) {
            return;
        }
        
        // Add relationship meta
        add_post_meta($post_id, '_geodirectory_place_id', $feed->geodirectory_place_id);
        add_post_meta($feed->geodirectory_place_id, '_rss_aggregator_post_' . $post_id, $post_id);
        
        // Add to place's related posts if custom field exists
        $related_posts = get_post_meta($feed->geodirectory_place_id, '_rss_aggregator_posts', true);
        if (!is_array($related_posts)) {
            $related_posts = array();
        }
        
        if (!in_array($post_id, $related_posts)) {
            $related_posts[] = $post_id;
            update_post_meta($feed->geodirectory_place_id, '_rss_aggregator_posts', $related_posts);
        }
    }
    
    /**
     * Add GeoDirectory custom fields for RSS integration
     */
    public function add_geodirectory_custom_fields() {
        if (!function_exists('geodir_get_posttypes')) {
            return;
        }
        
        $post_types = geodir_get_posttypes();
        
        foreach ($post_types as $post_type) {
            // Add RSS feed URL field
            $this->add_geodir_custom_field($post_type, array(
                'field_type' => 'url',
                'htmlvar_name' => 'rss_feed_url',
                'admin_title' => __('RSS Feed URL', 'rss-aggregator'),
                'frontend_title' => __('RSS Feed URL', 'rss-aggregator'),
                'frontend_desc' => __('URL of the RSS feed for this place', 'rss-aggregator'),
                'is_required' => 0,
                'show_in' => array('detail'),
                'tab_parent' => 'general'
            ));
            
            // Add county field
            $this->add_geodir_custom_field($post_type, array(
                'field_type' => 'text',
                'htmlvar_name' => 'county',
                'admin_title' => __('County', 'rss-aggregator'),
                'frontend_title' => __('County', 'rss-aggregator'),
                'frontend_desc' => __('County where this place is located', 'rss-aggregator'),
                'is_required' => 0,
                'show_in' => array('detail', 'listing'),
                'tab_parent' => 'general'
            ));
        }
    }
    
    /**
     * Add custom field to GeoDirectory post type
     *
     * @param string $post_type Post type
     * @param array $field_data Field data
     */
    private function add_geodir_custom_field($post_type, $field_data) {
        if (!function_exists('geodir_custom_field_save')) {
            return;
        }
        
        // Check if field already exists
        global $wpdb;
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM " . GEODIR_CUSTOM_FIELDS_TABLE . " WHERE post_type = %s AND htmlvar_name = %s",
            $post_type,
            $field_data['htmlvar_name']
        ));
        
        if ($existing) {
            return; // Field already exists
        }
        
        // Add default values
        $defaults = array(
            'post_type' => $post_type,
            'data_type' => 'VARCHAR',
            'field_type_key' => $field_data['field_type'],
            'sort_order' => 100,
            'is_active' => 1,
            'is_default' => 0,
            'for_admin_use' => 0
        );
        
        $field_data = wp_parse_args($field_data, $defaults);
        
        // Save field
        geodir_custom_field_save($field_data);
    }
    
    /**
     * Handle GeoDirectory place update
     *
     * @param int $post_id Place ID
     * @param array $post_data Post data
     */
    public function handle_geodirectory_place_update($post_id, $post_data) {
        // Check if this place has an RSS feed URL
        $rss_url = get_post_meta($post_id, 'rss_feed_url', true);
        
        if (empty($rss_url)) {
            return;
        }
        
        // Check if feed already exists in our system
        $database = new RSS_Database();
        $existing_feed = $database->get_feed_by_url($rss_url);
        
        if ($existing_feed) {
            // Update existing feed with place ID
            $database->save_feed(array(
                'id' => $existing_feed->id,
                'geodirectory_place_id' => $post_id,
                'county' => get_post_meta($post_id, 'county', true)
            ));
        } else {
            // Create new feed
            $place_title = get_the_title($post_id);
            $county = get_post_meta($post_id, 'county', true);
            
            $database->save_feed(array(
                'name' => sprintf(__('RSS Feed for %s', 'rss-aggregator'), $place_title),
                'url' => $rss_url,
                'geodirectory_place_id' => $post_id,
                'county' => $county,
                'update_frequency' => 'hourly',
                'status' => 'active'
            ));
        }
    }
    
    /**
     * Get GeoDirectory places for dropdown
     *
     * @param string $post_type GeoDirectory post type
     * @return array Places
     */
    public function get_geodirectory_places($post_type = '') {
        if (!$this->is_geodirectory_active()) {
            return array();
        }
        
        $args = array(
            'post_status' => 'publish',
            'posts_per_page' => -1,
            'orderby' => 'title',
            'order' => 'ASC'
        );
        
        if (!empty($post_type) && geodir_is_gd_post_type($post_type)) {
            $args['post_type'] = $post_type;
        } else {
            $args['post_type'] = geodir_get_posttypes();
        }
        
        $places = get_posts($args);
        $options = array();
        
        foreach ($places as $place) {
            $options[$place->ID] = $place->post_title . ' (' . $place->post_type . ')';
        }
        
        return $options;
    }
    
    /**
     * Get counties from GeoDirectory places
     *
     * @return array Counties
     */
    public function get_geodirectory_counties() {
        if (!$this->is_geodirectory_active()) {
            return array();
        }
        
        global $wpdb;
        
        $counties = $wpdb->get_col("
            SELECT DISTINCT meta_value 
            FROM {$wpdb->postmeta} 
            WHERE meta_key = 'county' 
            AND meta_value != '' 
            ORDER BY meta_value ASC
        ");
        
        return array_filter($counties);
    }
}
