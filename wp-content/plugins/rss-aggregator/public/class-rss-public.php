<?php
/**
 * RSS Public class
 *
 * @package RSS_Aggregator
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * RSS Public class
 */
class RSS_Public {
    
    /**
     * Plugin instance
     */
    private static $instance = null;
    
    /**
     * Database handler
     */
    private $database;
    
    /**
     * Get plugin instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->database = new RSS_Database();
        $this->init();
    }
    
    /**
     * Initialize public functionality
     */
    private function init() {
        // Enqueue public assets
        add_action('wp_enqueue_scripts', array($this, 'enqueue_public_assets'));
        
        // Add RSS aggregator content to posts
        add_filter('the_content', array($this, 'add_rss_source_info'));
        add_filter('get_the_excerpt', array($this, 'add_rss_source_info'));
        add_filter('the_excerpt', array($this, 'add_rss_source_info'));

        // Add RSS source info before title
        add_filter('the_title', array($this, 'add_rss_source_before_title'), 10, 2);

        // Hook to modify post links to point to original articles on archive pages
        add_filter('post_link', array($this, 'modify_rss_post_link'), 10, 2);
        add_filter('the_permalink', array($this, 'modify_rss_post_permalink'), 10, 1);

        // Add RSS meta to post head
        add_action('wp_head', array($this, 'add_rss_meta_tags'));
        
        // Register shortcodes
        add_shortcode('rss_aggregator_feed', array($this, 'shortcode_feed_display'));
        add_shortcode('rss_aggregator_county', array($this, 'shortcode_county_display'));
        add_shortcode('rss_aggregator_recent', array($this, 'shortcode_recent_display'));
        
        // Add RSS posts to main query if needed
        add_action('pre_get_posts', array($this, 'modify_main_query'));

        // Add RSS post classes
        add_filter('post_class', array($this, 'add_rss_post_classes'));


        
        // Add custom post classes
        add_filter('post_class', array($this, 'add_rss_post_classes'));
    }
    
    /**
     * Enqueue public assets
     */
    public function enqueue_public_assets() {
        wp_enqueue_style(
            'rss-aggregator-public',
            RSS_AGGREGATOR_PLUGIN_URL . 'public/css/public.css',
            array(),
            RSS_AGGREGATOR_VERSION
        );
        
        wp_enqueue_script(
            'rss-aggregator-public',
            RSS_AGGREGATOR_PLUGIN_URL . 'public/js/public.js',
            array('jquery'),
            RSS_AGGREGATOR_VERSION,
            true
        );
        
        wp_localize_script('rss-aggregator-public', 'rssAggregatorPublic', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('rss_aggregator_public')
        ));
    }
    
    /**
     * Add RSS source information to post content
     *
     * @param string $content Post content
     * @return string Modified content
     */
    public function add_rss_source_info($content) {
        global $post;

        if (!$post) {
            return $content;
        }

        $feed_id = get_post_meta($post->ID, '_rss_aggregator_feed_id', true);

        if (empty($feed_id)) {
            return $content;
        }

        $feed = $this->database->get_feed($feed_id);
        $source_url = get_post_meta($post->ID, '_rss_aggregator_source_url', true);
        $county = get_post_meta($post->ID, '_rss_aggregator_county', true);

        // Source info will be added before title via separate hook

        // For single posts, show full content with source info
        if (is_single()) {
            $source_footer = '';
            if (!empty($source_url)) {
                $source_footer = '<div style="margin-top: 20px;">';
                $source_footer .= '<a href="' . esc_url($source_url) . '" target="_blank" rel="noopener nofollow" class="rss-read-original">';
                $source_footer .= __('Czytaj oryginalny artykuł', 'rss-aggregator');
                $source_footer .= ' <span class="dashicons dashicons-external"></span></a>';
                $source_footer .= '</div>';
            }

            return $content . $source_footer;
        }

        // For post lists, show longer excerpt with source info and read more button
        if (is_home() || is_archive() || is_search() || is_front_page()) {
            // Longer excerpt - 100 words for more content
            $excerpt = wp_trim_words($content, 100, '...');

            $read_more = '';
            if (!empty($source_url)) {
                $read_more = '<div style="margin-top: 15px;">';
                $read_more .= '<a href="' . esc_url($source_url) . '" target="_blank" rel="noopener nofollow" class="rss-read-more-btn">';
                $read_more .= __('Czytaj dalej na stronie partnera', 'rss-aggregator');
                $read_more .= ' <span class="dashicons dashicons-external"></span></a>';
                $read_more .= '</div>';
            }

            return $excerpt . $read_more;
        }

        // For other contexts, return content as is
        return $content;
    }

    /**
     * Add RSS source info before post title
     */
    public function add_rss_source_before_title($title, $post_id = null) {
        if (!$post_id) {
            global $post;
            if (!$post) return $title;
            $post_id = $post->ID;
        }

        $feed_id = get_post_meta($post_id, '_rss_aggregator_feed_id', true);

        if (empty($feed_id)) {
            return $title;
        }

        $feed = $this->database->get_feed($feed_id);
        $county = get_post_meta($post_id, '_rss_aggregator_county', true);

        if (!$feed) {
            return $title;
        }

        // Only add source info on archive pages, home, front page
        if (is_home() || is_archive() || is_search() || is_front_page()) {
            $source_info = '<div class="rss-source-info-before-title" style="margin-bottom: 8px; font-size: 14px; color: #666;">';
            $source_info .= '<span class="dashicons dashicons-rss" style="font-size: 16px; vertical-align: middle; margin-right: 5px;"></span>';
            $source_info .= '<span style="font-weight: 500;">Źródło: ' . esc_html($feed->name) . '</span>';

            if (!empty($county)) {
                $source_info .= '<span class="rss-county-badge" style="margin-left: 10px; background: #e3f2fd; padding: 2px 8px; border-radius: 12px; font-size: 12px;">' . esc_html($county) . '</span>';
            }

            $source_info .= '</div>';

            return $source_info . $title;
        }

        return $title;
    }

    /**
     * Add RSS meta tags to post head
     */
    public function add_rss_meta_tags() {
        if (!is_single()) {
            return;
        }
        
        global $post;
        
        $feed_id = get_post_meta($post->ID, '_rss_aggregator_feed_id', true);
        
        if (empty($feed_id)) {
            return;
        }
        
        $feed = $this->database->get_feed($feed_id);
        $source_url = get_post_meta($post->ID, '_rss_aggregator_source_url', true);
        
        if ($feed) {
            echo '<meta name="rss-aggregator-source" content="' . esc_attr($feed->name) . '">' . "\n";
        }
        
        if ($source_url) {
            echo '<meta name="rss-aggregator-original-url" content="' . esc_url($source_url) . '">' . "\n";
            echo '<link rel="canonical" href="' . esc_url($source_url) . '">' . "\n";
        }
    }
    
    /**
     * Shortcode to display feed items
     *
     * @param array $atts Shortcode attributes
     * @return string HTML output
     */
    public function shortcode_feed_display($atts) {
        $atts = shortcode_atts(array(
            'feed_id' => '',
            'county' => '',
            'limit' => 10,
            'show_thumbnail' => 'yes',
            'show_excerpt' => 'yes',
            'show_date' => 'yes',
            'show_source' => 'yes',
            'order' => 'DESC',
            'class' => ''
        ), $atts);
        
        $args = array(
            'post_type' => 'post',
            'posts_per_page' => intval($atts['limit']),
            'order' => $atts['order'] === 'ASC' ? 'ASC' : 'DESC',
            'orderby' => 'date',
            'meta_query' => array(
                array(
                    'key' => '_rss_aggregator_feed_id',
                    'compare' => 'EXISTS'
                )
            )
        );
        
        // Filter by specific feed
        if (!empty($atts['feed_id'])) {
            $args['meta_query'][] = array(
                'key' => '_rss_aggregator_feed_id',
                'value' => intval($atts['feed_id']),
                'compare' => '='
            );
        }
        
        // Filter by county
        if (!empty($atts['county'])) {
            $args['meta_query'][] = array(
                'key' => '_rss_aggregator_county',
                'value' => sanitize_text_field($atts['county']),
                'compare' => '='
            );
        }
        
        $query = new WP_Query($args);
        
        if (!$query->have_posts()) {
            return '<p class="rss-aggregator-no-items">' . __('No RSS items found.', 'rss-aggregator') . '</p>';
        }
        
        $output = '<div class="rss-aggregator-feed-display ' . esc_attr($atts['class']) . '">';
        
        while ($query->have_posts()) {
            $query->the_post();
            
            $feed_id = get_post_meta(get_the_ID(), '_rss_aggregator_feed_id', true);
            $feed = $this->database->get_feed($feed_id);
            $source_url = get_post_meta(get_the_ID(), '_rss_aggregator_source_url', true);
            $county = get_post_meta(get_the_ID(), '_rss_aggregator_county', true);
            
            $output .= '<article class="rss-aggregator-item">';
            
            // Thumbnail
            if ($atts['show_thumbnail'] === 'yes' && has_post_thumbnail()) {
                $output .= '<div class="rss-item-thumbnail">';
                $output .= get_the_post_thumbnail(get_the_ID(), 'medium');
                $output .= '</div>';
            }
            
            $output .= '<div class="rss-item-content">';
            
            // Title
            $output .= '<h3 class="rss-item-title">';
            $output .= '<a href="' . get_permalink() . '">' . get_the_title() . '</a>';
            $output .= '</h3>';
            
            // Meta information
            $output .= '<div class="rss-item-meta">';
            
            if ($atts['show_date'] === 'yes') {
                $output .= '<span class="rss-item-date">' . get_the_date() . '</span>';
            }
            
            if ($atts['show_source'] === 'yes' && $feed) {
                $output .= '<span class="rss-item-source">' . esc_html($feed->name) . '</span>';
            }
            
            if (!empty($county)) {
                $output .= '<span class="rss-item-county">' . sprintf(__('Powiat %s', 'rss-aggregator'), esc_html($county)) . '</span>';
            }
            
            $output .= '</div>';
            
            // Excerpt
            if ($atts['show_excerpt'] === 'yes') {
                $output .= '<div class="rss-item-excerpt">' . get_the_excerpt() . '</div>';
            }
            
            // Read more link
            $output .= '<div class="rss-item-actions">';
            $output .= '<a href="' . get_permalink() . '" class="rss-read-more">' . __('Read more', 'rss-aggregator') . '</a>';
            
            if (!empty($source_url)) {
                $output .= ' <a href="' . esc_url($source_url) . '" target="_blank" rel="noopener nofollow" class="rss-original-link">';
                $output .= __('Original article', 'rss-aggregator') . ' <span class="dashicons dashicons-external"></span></a>';
            }
            
            $output .= '</div>';
            
            $output .= '</div>'; // .rss-item-content
            $output .= '</article>';
        }
        
        $output .= '</div>';
        
        wp_reset_postdata();
        
        return $output;
    }
    
    /**
     * Shortcode to display items by county
     *
     * @param array $atts Shortcode attributes
     * @return string HTML output
     */
    public function shortcode_county_display($atts) {
        $atts = shortcode_atts(array(
            'county' => '',
            'limit' => 10,
            'show_thumbnail' => 'yes',
            'show_excerpt' => 'yes',
            'class' => ''
        ), $atts);
        
        if (empty($atts['county'])) {
            return '<p class="rss-aggregator-error">' . __('County parameter is required.', 'rss-aggregator') . '</p>';
        }
        
        return $this->shortcode_feed_display($atts);
    }
    
    /**
     * Shortcode to display recent RSS items
     *
     * @param array $atts Shortcode attributes
     * @return string HTML output
     */
    public function shortcode_recent_display($atts) {
        $atts = shortcode_atts(array(
            'limit' => 5,
            'days' => 7,
            'show_thumbnail' => 'no',
            'show_excerpt' => 'no',
            'show_date' => 'yes',
            'show_source' => 'yes',
            'class' => 'rss-recent-items'
        ), $atts);
        
        $args = array(
            'post_type' => 'post',
            'posts_per_page' => intval($atts['limit']),
            'orderby' => 'date',
            'order' => 'DESC',
            'date_query' => array(
                array(
                    'after' => intval($atts['days']) . ' days ago'
                )
            ),
            'meta_query' => array(
                array(
                    'key' => '_rss_aggregator_feed_id',
                    'compare' => 'EXISTS'
                )
            )
        );
        
        $query = new WP_Query($args);
        
        if (!$query->have_posts()) {
            return '<p class="rss-aggregator-no-items">' . __('No recent RSS items found.', 'rss-aggregator') . '</p>';
        }
        
        $output = '<div class="rss-aggregator-recent-display ' . esc_attr($atts['class']) . '">';
        $output .= '<ul class="rss-recent-list">';
        
        while ($query->have_posts()) {
            $query->the_post();
            
            $feed_id = get_post_meta(get_the_ID(), '_rss_aggregator_feed_id', true);
            $feed = $this->database->get_feed($feed_id);
            
            $output .= '<li class="rss-recent-item">';
            
            if ($atts['show_thumbnail'] === 'yes' && has_post_thumbnail()) {
                $output .= '<div class="rss-recent-thumbnail">';
                $output .= get_the_post_thumbnail(get_the_ID(), 'thumbnail');
                $output .= '</div>';
            }
            
            $output .= '<div class="rss-recent-content">';
            $output .= '<h4><a href="' . get_permalink() . '">' . get_the_title() . '</a></h4>';
            
            if ($atts['show_excerpt'] === 'yes') {
                $output .= '<p>' . wp_trim_words(get_the_excerpt(), 15) . '</p>';
            }
            
            $output .= '<div class="rss-recent-meta">';
            
            if ($atts['show_date'] === 'yes') {
                $output .= '<span class="rss-recent-date">' . human_time_diff(get_the_time('U'), current_time('timestamp')) . ' ' . __('ago', 'rss-aggregator') . '</span>';
            }
            
            if ($atts['show_source'] === 'yes' && $feed) {
                $output .= '<span class="rss-recent-source">' . esc_html($feed->name) . '</span>';
            }
            
            $output .= '</div>';
            $output .= '</div>';
            $output .= '</li>';
        }
        
        $output .= '</ul>';
        $output .= '</div>';
        
        wp_reset_postdata();
        
        return $output;
    }
    
    /**
     * Modify main query to include RSS posts if needed
     *
     * @param WP_Query $query Query object
     */
    public function modify_main_query($query) {
        // Only modify main query on frontend
        if (is_admin() || !$query->is_main_query()) {
            return;
        }
        
        // Add any main query modifications here if needed
        // For example, to include RSS posts in category archives
    }
    
    /**
     * Add custom CSS classes to RSS posts
     *
     * @param array $classes Post classes
     * @return array Modified classes
     */
    public function add_rss_post_classes($classes) {
        global $post;
        
        if (!$post) {
            return $classes;
        }
        
        $feed_id = get_post_meta($post->ID, '_rss_aggregator_feed_id', true);
        
        if (!empty($feed_id)) {
            $classes[] = 'rss-aggregator-post';
            $classes[] = 'rss-feed-' . $feed_id;
            
            $county = get_post_meta($post->ID, '_rss_aggregator_county', true);
            if (!empty($county)) {
                $classes[] = 'rss-county-' . sanitize_html_class($county);
            }
        }
        
        return $classes;
    }

    /**
     * Modify RSS post links to point to original articles on archive pages
     *
     * @param string $permalink Post permalink
     * @param WP_Post $post Post object
     * @return string Modified permalink
     */
    public function modify_rss_post_link($permalink, $post) {
        // Only modify on archive pages, not single posts
        if (is_single() || is_admin()) {
            return $permalink;
        }

        $feed_id = get_post_meta($post->ID, '_rss_aggregator_feed_id', true);

        if (!empty($feed_id)) {
            $source_url = get_post_meta($post->ID, '_rss_aggregator_source_url', true);
            if (!empty($source_url)) {
                return $source_url;
            }
        }

        return $permalink;
    }

    /**
     * Modify the_permalink for RSS posts on archive pages
     *
     * @param string $permalink Post permalink
     * @return string Modified permalink
     */
    public function modify_rss_post_permalink($permalink) {
        global $post;

        // Only modify on archive pages, not single posts
        if (is_single() || is_admin() || !$post) {
            return $permalink;
        }

        $feed_id = get_post_meta($post->ID, '_rss_aggregator_feed_id', true);

        if (!empty($feed_id)) {
            $source_url = get_post_meta($post->ID, '_rss_aggregator_source_url', true);
            if (!empty($source_url)) {
                return $source_url;
            }
        }

        return $permalink;
    }

}
