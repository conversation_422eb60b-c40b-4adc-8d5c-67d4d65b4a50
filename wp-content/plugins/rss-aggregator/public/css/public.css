/**
 * RSS Aggregator Public Styles
 *
 * @package RSS_Aggregator
 * @since 1.0.0
 */

/* RSS Source Information */
.rss-aggregator-source-info {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin: 20px 0;
    font-size: 14px;
}

.rss-source-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.rss-source-icon {
    color: #ff6600;
    margin-right: 8px;
}

.rss-source-label {
    font-weight: 600;
    color: #495057;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 0.5px;
}

.rss-source-details {
    color: #6c757d;
}

.rss-source-details strong {
    color: #212529;
}

.rss-county-badge {
    background: #007cba;
    color: #fff;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    margin-left: 8px;
    text-transform: uppercase;
}

.rss-source-details a {
    color: #007cba;
    text-decoration: none;
    font-weight: 500;
}

.rss-source-details a:hover {
    text-decoration: underline;
}

.rss-source-details .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
    vertical-align: middle;
}

/* Feed Display Shortcode */
.rss-aggregator-feed-display {
    margin: 20px 0;
}

.rss-aggregator-item {
    display: flex;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.rss-aggregator-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.rss-item-thumbnail {
    flex-shrink: 0;
    margin-right: 20px;
    width: 150px;
}

.rss-item-thumbnail img {
    width: 100%;
    height: auto;
    border-radius: 4px;
}

.rss-item-content {
    flex: 1;
}

.rss-item-title {
    margin: 0 0 10px 0;
    font-size: 18px;
    line-height: 1.4;
}

.rss-item-title a {
    color: #212529;
    text-decoration: none;
}

.rss-item-title a:hover {
    color: #007cba;
}

.rss-item-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 10px;
    font-size: 13px;
    color: #6c757d;
}

.rss-item-date,
.rss-item-source,
.rss-item-county {
    display: flex;
    align-items: center;
}

.rss-item-source {
    font-weight: 500;
    color: #495057;
}

.rss-item-county {
    background: #e9ecef;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.rss-item-excerpt {
    color: #495057;
    line-height: 1.6;
    margin-bottom: 15px;
}

.rss-item-actions {
    display: flex;
    gap: 15px;
}

.rss-read-more,
.rss-original-link {
    color: #007cba;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
}

.rss-read-more:hover,
.rss-original-link:hover {
    text-decoration: underline;
}

.rss-original-link .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
    vertical-align: middle;
}

/* Recent Items Shortcode */
.rss-aggregator-recent-display {
    margin: 20px 0;
}

.rss-recent-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.rss-recent-item {
    display: flex;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.rss-recent-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.rss-recent-thumbnail {
    flex-shrink: 0;
    margin-right: 15px;
    width: 60px;
}

.rss-recent-thumbnail img {
    width: 100%;
    height: auto;
    border-radius: 4px;
}

.rss-recent-content {
    flex: 1;
}

.rss-recent-content h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
    line-height: 1.4;
}

.rss-recent-content h4 a {
    color: #212529;
    text-decoration: none;
}

.rss-recent-content h4 a:hover {
    color: #007cba;
}

.rss-recent-content p {
    margin: 0 0 8px 0;
    color: #6c757d;
    font-size: 14px;
    line-height: 1.5;
}

.rss-recent-meta {
    display: flex;
    gap: 10px;
    font-size: 12px;
    color: #6c757d;
}

.rss-recent-date,
.rss-recent-source {
    font-weight: 500;
}

.rss-recent-source {
    color: #495057;
}

/* No Items Message */
.rss-aggregator-no-items {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 4px;
    margin: 20px 0;
}

.rss-aggregator-error {
    text-align: center;
    color: #dc3545;
    font-weight: 500;
    padding: 15px;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    margin: 20px 0;
}

/* Post Classes for RSS Posts */
.rss-aggregator-post {
    position: relative;
}

.rss-aggregator-post::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: #ff6600;
    border-radius: 2px;
}

/* BuddyBoss Activity Styles */
.rss-aggregator-activity {
    position: relative;
}

.rss-aggregator-activity .rss-aggregator-thumbnail {
    margin-bottom: 15px;
}

.rss-aggregator-activity .rss-aggregator-thumbnail img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
}

.rss-aggregator-activity .rss-aggregator-excerpt {
    margin-bottom: 15px;
    color: #666;
    line-height: 1.6;
}

.rss-aggregator-activity .rss-aggregator-source {
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 4px solid #ff6600;
}

.rss-aggregator-activity .rss-aggregator-source a {
    color: #007cba;
    text-decoration: none;
    font-weight: 500;
}

.rss-aggregator-activity .rss-aggregator-source a:hover {
    text-decoration: underline;
}

.rss-aggregator-county-badge {
    display: inline-block;
    background: #007cba;
    color: #fff;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    margin-top: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .rss-aggregator-item {
        flex-direction: column;
    }

    .rss-item-thumbnail {
        margin-right: 0;
        margin-bottom: 15px;
        width: 100%;
        max-width: 200px;
    }

    .rss-item-meta {
        flex-direction: column;
        gap: 8px;
    }

    .rss-item-actions {
        flex-direction: column;
        gap: 10px;
    }

    .rss-recent-item {
        flex-direction: column;
    }

    .rss-recent-thumbnail {
        margin-right: 0;
        margin-bottom: 10px;
        width: 100%;
        max-width: 120px;
    }

    .rss-aggregator-source-info {
        padding: 12px;
    }

    .rss-source-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    /* RSS Post responsive layout */
    .post.rss-aggregator-post {
        flex-direction: column;
        gap: 15px;
        padding: 15px;
    }

    .post.rss-aggregator-post .post-thumbnail {
        width: 100%;
        max-width: 100%;
    }

    .post.rss-aggregator-post .post-thumbnail img {
        max-height: 200px;
    }

    .rss-read-more-btn {
        padding: 10px 16px;
        font-size: 16px;
        width: 100%;
        justify-content: center;
    }

    .rss-read-original {
        padding: 12px 20px;
        width: 100%;
        justify-content: center;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .rss-item-title {
        font-size: 16px;
    }
    
    .rss-recent-content h4 {
        font-size: 14px;
    }
    
    .rss-aggregator-source-info {
        padding: 10px;
        font-size: 13px;
    }
    
    .rss-county-badge {
        display: block;
        margin: 5px 0;
        text-align: center;
        width: fit-content;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .rss-aggregator-source-info {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .rss-source-details strong,
    .rss-item-title a {
        color: #f7fafc;
    }
    
    .rss-item-meta,
    .rss-item-excerpt {
        color: #a0aec0;
    }
    
    .rss-aggregator-no-items {
        background: #2d3748;
        color: #a0aec0;
    }
    
    .rss-aggregator-activity .rss-aggregator-source {
        background: #2d3748;
        border-left-color: #ff6600;
    }
}

/* RSS Post Excerpt Layout */
.rss-aggregator-excerpt-info {
    margin: 15px 0;
}

.rss-source-meta {
    margin-bottom: 8px;
    font-size: 13px;
    color: #666;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}

.rss-source-name {
    font-weight: 600;
    color: #007cba;
}

.rss-county-small {
    background: #f0f0f0;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 11px;
    margin-left: 8px;
    color: #666;
}

.rss-excerpt-content {
    margin: 10px 0;
    line-height: 1.6;
    color: #333;
}

.rss-read-more {
    margin-top: 12px;
}

.rss-read-more-btn {
    display: inline-flex;
    align-items: center;
    background: #007cba;
    color: #fff !important;
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.rss-read-more-btn:hover {
    background: #005a87;
    text-decoration: none;
}

.rss-read-more-btn .dashicons {
    margin-left: 5px;
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.rss-read-original {
    display: inline-flex;
    align-items: center;
    background: #28a745;
    color: #fff !important;
    padding: 10px 20px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 600;
    margin-top: 15px;
    transition: background-color 0.3s ease;
}

.rss-read-original:hover {
    background: #218838;
    text-decoration: none;
}

.rss-read-original .dashicons {
    margin-left: 8px;
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* WordPress Post Layout with Thumbnail on Left */
.post.rss-aggregator-post {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fff;
}

.post.rss-aggregator-post .post-thumbnail {
    flex-shrink: 0;
    width: 200px;
    max-width: 200px;
}

.post.rss-aggregator-post .post-thumbnail img {
    width: 100%;
    height: auto;
    border-radius: 6px;
    object-fit: cover;
    max-height: 150px;
}

.post.rss-aggregator-post .entry-content-wrap {
    flex: 1;
    min-width: 0;
}

.post.rss-aggregator-post .entry-title {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 20px;
    line-height: 1.4;
}

.post.rss-aggregator-post .entry-title a {
    color: #333;
    text-decoration: none;
}

.post.rss-aggregator-post .entry-title a:hover {
    color: #007cba;
}

/* Archive/Home page layout adjustments */
.home .post.rss-aggregator-post,
.archive .post.rss-aggregator-post,
.search .post.rss-aggregator-post {
    border-left: 4px solid #007cba;
}

/* Single post layout */
.single .post.rss-aggregator-post {
    display: block;
    border-left: 4px solid #007cba;
}

.single .post.rss-aggregator-post .post-thumbnail {
    width: 100%;
    max-width: 400px;
    margin-bottom: 20px;
}

.single .post.rss-aggregator-post .post-thumbnail img {
    max-height: 300px;
}

/* Print Styles */
@media print {
    .rss-aggregator-source-info,
    .rss-item-actions,
    .rss-read-more,
    .rss-read-original {
        display: none;
    }

    .rss-aggregator-item,
    .post.rss-aggregator-post {
        break-inside: avoid;
        page-break-inside: avoid;
    }

    .post.rss-aggregator-post {
        display: block;
        border: 1px solid #ccc;
    }

    .post.rss-aggregator-post .post-thumbnail {
        width: 100%;
        margin-bottom: 15px;
    }
}
