-- Manual SQL commands to fix RSS Aggregator database structure
-- Execute these commands in phpMyAdmin or your database management tool

-- First, check current table structure
SHOW COLUMNS FROM `stg_rss_aggregator_feeds`;

-- Add missing columns if they don't exist
-- Replace 'stg_' with your actual WordPress table prefix

-- Add geodirectory_place_name column
ALTER TABLE `stg_rss_aggregator_feeds` 
ADD COLUMN `geodirectory_place_name` varchar(255) DEFAULT NULL 
AFTER `geodirectory_place_id`;

-- Add assigned_user_id column  
ALTER TABLE `stg_rss_aggregator_feeds` 
ADD COLUMN `assigned_user_id` bigint(20) DEFAULT NULL 
AFTER `geodirectory_place_name`;

-- Add assigned_user_name column
ALTER TABLE `stg_rss_aggregator_feeds` 
ADD COLUMN `assigned_user_name` varchar(255) DEFAULT NULL 
AFTER `assigned_user_id`;

-- Add index for better performance
ALTER TABLE `stg_rss_aggregator_feeds` 
ADD INDEX `idx_assigned_user` (`assigned_user_id`);

-- Verify the changes
SHOW COLUMNS FROM `stg_rss_aggregator_feeds`;

-- Expected columns after migration:
-- id, name, url, geodirectory_place_id, geodirectory_place_name, assigned_user_id, assigned_user_name, county, update_frequency, initial_import_count, status, created_at, last_updated

-- If you get errors about columns already existing, that's normal - it means they're already there
-- You can also run these commands one by one to see which ones are needed:

-- Check if geodirectory_place_name exists:
-- SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'stg_rss_aggregator_feeds' AND COLUMN_NAME = 'geodirectory_place_name';

-- Check if assigned_user_id exists:
-- SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'stg_rss_aggregator_feeds' AND COLUMN_NAME = 'assigned_user_id';

-- Check if assigned_user_name exists:
-- SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'stg_rss_aggregator_feeds' AND COLUMN_NAME = 'assigned_user_name';
