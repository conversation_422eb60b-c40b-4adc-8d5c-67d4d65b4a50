<?php
/**
 * Simple syntax test for RSS Aggregator
 */

echo "Testing syntax...\n";

// Test main plugin file
$main_file = __DIR__ . '/rss-aggregator.php';
if (file_exists($main_file)) {
    echo "Main file exists: " . $main_file . "\n";
    
    // Check syntax
    $output = array();
    $return_var = 0;
    exec("php -l " . escapeshellarg($main_file) . " 2>&1", $output, $return_var);
    
    if ($return_var === 0) {
        echo "Main file syntax: OK\n";
    } else {
        echo "Main file syntax: ERROR\n";
        echo implode("\n", $output) . "\n";
    }
} else {
    echo "Main file not found\n";
}

// Test admin class
$admin_file = __DIR__ . '/admin/class-rss-admin.php';
if (file_exists($admin_file)) {
    echo "Admin file exists: " . $admin_file . "\n";
    
    // Check syntax
    $output = array();
    $return_var = 0;
    exec("php -l " . escapeshellarg($admin_file) . " 2>&1", $output, $return_var);
    
    if ($return_var === 0) {
        echo "Admin file syntax: OK\n";
    } else {
        echo "Admin file syntax: ERROR\n";
        echo implode("\n", $output) . "\n";
    }
} else {
    echo "Admin file not found\n";
}

// Test database class
$db_file = __DIR__ . '/includes/class-rss-database.php';
if (file_exists($db_file)) {
    echo "Database file exists: " . $db_file . "\n";
    
    // Check syntax
    $output = array();
    $return_var = 0;
    exec("php -l " . escapeshellarg($db_file) . " 2>&1", $output, $return_var);
    
    if ($return_var === 0) {
        echo "Database file syntax: OK\n";
    } else {
        echo "Database file syntax: ERROR\n";
        echo implode("\n", $output) . "\n";
    }
} else {
    echo "Database file not found\n";
}

echo "Syntax test complete\n";
?>
