# RSS Aggregator Language Template
# Copyright (C) 2024 RSS Aggregator
# This file is distributed under the same license as the RSS Aggregator package.
msgid ""
msgstr ""
"Project-Id-Version: RSS Aggregator 1.1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-01 12:00+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"

#: admin/class-rss-admin.php:45
msgid "RSS Aggregator"
msgstr ""

#: admin/class-rss-admin.php:51
msgid "Feeds"
msgstr ""

#: admin/class-rss-admin.php:59
msgid "Add New"
msgstr ""

#: admin/class-rss-admin.php:67
msgid "Settings"
msgstr ""

#: admin/class-rss-admin.php:75
msgid "Status"
msgstr ""

#: admin/partials/feeds-list.php:15
msgid "Total Feeds"
msgstr ""

#: admin/partials/feeds-list.php:19
msgid "Active Feeds"
msgstr ""

#: admin/partials/feeds-list.php:23
msgid "Total Items"
msgstr ""

#: admin/partials/feeds-list.php:27
msgid "Last 24h"
msgstr ""

#: admin/partials/feeds-list.php:33
msgid "No RSS feeds yet"
msgstr ""

#: admin/partials/feeds-list.php:34
msgid "Start by adding your first RSS feed to begin aggregating content."
msgstr ""

#: admin/partials/feeds-list.php:36
msgid "Add Your First Feed"
msgstr ""

#: admin/partials/feeds-list.php:48
msgid "Bulk Actions"
msgstr ""

#: admin/partials/feeds-list.php:49
msgid "Delete"
msgstr ""

#: admin/partials/feeds-list.php:53
msgid "Apply"
msgstr ""

#: admin/partials/feeds-list.php:57
msgid "Update All Feeds"
msgstr ""

#: admin/partials/feeds-list.php:68
msgid "Name"
msgstr ""

#: admin/partials/feeds-list.php:71
msgid "URL"
msgstr ""

#: admin/partials/feeds-list.php:74
msgid "County"
msgstr ""

#: admin/partials/feeds-list.php:77
msgid "Frequency"
msgstr ""

#: admin/partials/feeds-list.php:80
msgid "Initial Count"
msgstr ""

#: admin/partials/feeds-list.php:145
msgid "(not imported yet)"
msgstr ""

#: admin/partials/feeds-list.php:147
msgid "(imported)"
msgstr ""

#: admin/partials/feeds-list.php:80
msgid "Status"
msgstr ""

#: admin/partials/feeds-list.php:83
msgid "Last Updated"
msgstr ""

#: admin/partials/feeds-list.php:97
msgid "Edit"
msgstr ""

#: admin/partials/feeds-list.php:101
msgid "Test"
msgstr ""

#: admin/partials/feeds-list.php:105
msgid "Update Now"
msgstr ""

#: admin/partials/feeds-list.php:109
msgid "Delete"
msgstr ""

#: admin/partials/feeds-list.php:120
msgid "Not set"
msgstr ""

#: admin/partials/feeds-list.php:133
msgid "Active"
msgstr ""

#: admin/partials/feeds-list.php:139
msgid "Inactive"
msgstr ""

#: admin/partials/feeds-list.php:147
msgid "ago"
msgstr ""

#: admin/partials/feeds-list.php:149
msgid "Never"
msgstr ""

#: admin/partials/feed-form.php:19
msgid "Edit RSS Feed"
msgstr ""

#: admin/partials/feed-form.php:21
msgid "Add New RSS Feed"
msgstr ""

#: admin/partials/feed-form.php:35
msgid "Feed Name"
msgstr ""

#: admin/partials/feed-form.php:44
msgid "A descriptive name for this RSS feed."
msgstr ""

#: admin/partials/feed-form.php:50
msgid "Feed URL"
msgstr ""

#: admin/partials/feed-form.php:60
msgid "Test Feed"
msgstr ""

#: admin/partials/feed-form.php:63
msgid "The full URL to the RSS feed."
msgstr ""

#: admin/partials/feed-form.php:70
msgid "County"
msgstr ""

#: admin/partials/feed-form.php:75
msgid "Select County"
msgstr ""

#: admin/partials/feed-form.php:85
msgid "Or enter custom county"
msgstr ""

#: admin/partials/feed-form.php:91
msgid "Enter county name"
msgstr ""

#: admin/partials/feed-form.php:95
msgid "The county this feed represents. Used for categorization and BuddyBoss integration."
msgstr ""

#: admin/partials/feed-form.php:102
msgid "GeoDirectory Place"
msgstr ""

#: admin/partials/feed-form.php:106
msgid "Select Place"
msgstr ""

#: admin/partials/feed-form.php:115
msgid "Link this feed to a specific place in GeoDirectory."
msgstr ""

#: admin/partials/feed-form.php:122
msgid "Update Frequency"
msgstr ""

#: admin/partials/feed-form.php:133
msgid "How often should this feed be checked for new items?"
msgstr ""

#: admin/partials/feed-form.php:140
msgid "Status"
msgstr ""

#: admin/partials/feed-form.php:145
msgid "Active"
msgstr ""

#: admin/partials/feed-form.php:149
msgid "Inactive"
msgstr ""

#: admin/partials/feed-form.php:153
msgid "Inactive feeds will not be updated automatically."
msgstr ""

#: admin/partials/feed-form.php:158
msgid "Update Feed"
msgstr ""

#: admin/partials/feed-form.php:142
msgid "Initial Import Count"
msgstr ""

#: admin/partials/feed-form.php:153
msgid "Number of newest posts to import when this feed is first added (1-100). This only applies to the first import - subsequent updates will import all new items."
msgstr ""

#: admin/partials/feed-form.php:158
msgid "Update Feed"
msgstr ""

#: admin/partials/feed-form.php:158
msgid "Add Feed"
msgstr ""

#: admin/partials/settings.php:25
msgid "RSS Aggregator Settings"
msgstr ""

#: admin/partials/settings.php:32
msgid "General"
msgstr ""

#: admin/partials/settings.php:33
msgid "Integrations"
msgstr ""

#: admin/partials/settings.php:34
msgid "Posts"
msgstr ""

#: admin/partials/settings.php:35
msgid "Maintenance"
msgstr ""

#: admin/partials/settings.php:39
msgid "General Settings"
msgstr ""

#: admin/partials/settings.php:44
msgid "Default Update Frequency"
msgstr ""

#: admin/partials/settings.php:54
msgid "Default frequency for new feeds. Can be overridden per feed."
msgstr ""

#: admin/partials/settings.php:60
msgid "Max Items per Feed"
msgstr ""

#: admin/partials/settings.php:71
msgid "Maximum number of items to process from each feed update."
msgstr ""

#: admin/partials/settings.php:78
msgid "Default Initial Import Count"
msgstr ""

#: admin/partials/settings.php:88
msgid "Default number of newest posts to import when a new feed is added. This can be overridden for each individual feed."
msgstr ""

#: admin/partials/settings.php:77
msgid "Enable Thumbnails"
msgstr ""

#: admin/partials/settings.php:84
msgid "Automatically download and set featured images from RSS feeds"
msgstr ""

#: admin/partials/settings.php:87
msgid "When enabled, the plugin will try to extract thumbnails from RSS feeds and article pages."
msgstr ""

#: admin/partials/settings.php:95
msgid "Integration Settings"
msgstr ""

#: admin/partials/settings.php:100
msgid "BuddyBoss Integration"
msgstr ""

#: admin/partials/settings.php:107
msgid "Create activity stream entries for RSS posts"
msgstr ""

#: admin/partials/settings.php:111
msgid "BuddyBoss Platform is not active. This feature requires BuddyBoss to be installed and activated."
msgstr ""

#: admin/partials/settings.php:114
msgid "RSS posts will appear in the BuddyBoss activity stream with source information."
msgstr ""

#: admin/partials/settings.php:121
msgid "GeoDirectory Integration"
msgstr ""

#: admin/partials/settings.php:128
msgid "Link RSS feeds to GeoDirectory places"
msgstr ""

#: admin/partials/settings.php:132
msgid "GeoDirectory is not active. This feature requires GeoDirectory to be installed and activated."
msgstr ""

#: admin/partials/settings.php:135
msgid "RSS feeds can be associated with specific places and counties in GeoDirectory."
msgstr ""

#: admin/partials/settings.php:143
msgid "Post Settings"
msgstr ""

#: admin/partials/settings.php:148
msgid "Default Post Status"
msgstr ""

#: admin/partials/settings.php:157
msgid "Status for posts created from RSS feeds."
msgstr ""

#: admin/partials/settings.php:164
msgid "Default Post Author"
msgstr ""

#: admin/partials/settings.php:174
msgid "User to assign as author for posts created from RSS feeds."
msgstr ""

#: admin/partials/settings.php:181
msgid "Maintenance Settings"
msgstr ""

#: admin/partials/settings.php:186
msgid "Cleanup Old Items"
msgstr ""

#: admin/partials/settings.php:196
msgid "days"
msgstr ""

#: admin/partials/settings.php:198
msgid "Automatically delete RSS items older than this many days. Runs daily via cron."
msgstr ""

#: admin/partials/settings.php:204
msgid "Manual Cleanup"
msgstr ""

#: admin/partials/settings.php:208
msgid "Run Cleanup Now"
msgstr ""

#: admin/partials/settings.php:211
msgid "Manually run the cleanup process to remove old RSS items."
msgstr ""

#: admin/partials/settings.php:217
msgid "Reset Cron Schedules"
msgstr ""

#: admin/partials/settings.php:221
msgid "Reset All Schedules"
msgstr ""

#: admin/partials/settings.php:224
msgid "Reset all cron schedules if feeds are not updating properly."
msgstr ""

#: admin/partials/settings.php:231
msgid "Save Settings"
msgstr ""

#: admin/partials/status.php:15
msgid "RSS Aggregator Status"
msgstr ""

#: admin/partials/status.php:21
msgid "System Status"
msgstr ""

#: admin/partials/status.php:24
msgid "WordPress Version:"
msgstr ""

#: admin/partials/status.php:28
msgid "PHP Version:"
msgstr ""

#: admin/partials/status.php:32
msgid "Plugin Version:"
msgstr ""

#: admin/partials/status.php:36
msgid "WP-Cron Status:"
msgstr ""

#: admin/partials/status.php:41
msgid "Enabled"
msgstr ""

#: admin/partials/status.php:47
msgid "Disabled"
msgstr ""

#: admin/partials/status.php:53
msgid "BuddyBoss:"
msgstr ""

#: admin/partials/status.php:58
msgid "Active"
msgstr ""

#: admin/partials/status.php:64
msgid "Not Active"
msgstr ""

#: admin/partials/status.php:70
msgid "GeoDirectory:"
msgstr ""

#: admin/partials/status.php:75
msgid "Active"
msgstr ""

#: admin/partials/status.php:81
msgid "Not Active"
msgstr ""

#: includes/class-rss-cron.php:45
msgid "Every 15 minutes"
msgstr ""

#: includes/class-rss-cron.php:49
msgid "Every 30 minutes"
msgstr ""

#: includes/class-rss-cron.php:53
msgid "Every 2 hours"
msgstr ""

#: includes/class-rss-cron.php:57
msgid "Every 6 hours"
msgstr ""

#: includes/class-rss-integrations.php:150
msgid "shared news from"
msgstr ""

#: includes/class-rss-integrations.php:225
msgid "Read original article"
msgstr ""

#: includes/class-rss-integrations.php:350
msgid "RSS Feed URL"
msgstr ""

#: includes/class-rss-integrations.php:353
msgid "URL of the RSS feed for this place"
msgstr ""

#: includes/class-rss-integrations.php:361
msgid "County"
msgstr ""

#: includes/class-rss-integrations.php:364
msgid "County where this place is located"
msgstr ""

#: includes/class-rss-integrations.php:420
msgid "RSS Feed for %s"
msgstr ""

#: public/class-rss-public.php:85
msgid "Source:"
msgstr ""

#: public/class-rss-public.php:93
msgid "Powiat %s"
msgstr ""

#: public/class-rss-public.php:98
msgid "Read original article"
msgstr ""

#: public/class-rss-public.php:150
msgid "No RSS items found."
msgstr ""

#: public/class-rss-public.php:200
msgid "Read more"
msgstr ""

#: public/class-rss-public.php:204
msgid "Original article"
msgstr ""

#: public/class-rss-public.php:225
msgid "County parameter is required."
msgstr ""

#: public/class-rss-public.php:245
msgid "No recent RSS items found."
msgstr ""

#: public/class-rss-public.php:270
msgid "ago"
msgstr ""
