# RSS Aggregator - Instrukcja Instalacji

## Wymagania Systemowe

### Minimalne wymagania:
- **WordPress**: 5.0 lub nowszy
- **PHP**: 7.4 lub nowszy
- **MySQL**: 5.6 lub nowszy / MariaDB: 10.1 lub nowszy
- **Pamięć PHP**: minimum 128MB (zalecane 256MB)
- **Rozszerzenia PHP**: 
  - `curl` (do pobierania RSS)
  - `xml` (do parsowania RSS)
  - `dom` (do parsowania HTML)
  - `mbstring` (do obsługi znaków UTF-8)

### Zalecane wtyczki:
- **BuddyBoss Platform** (dla integracji z aktywnościami)
- **GeoDirectory** (dla integracji z miejscami i powiatami)

## Instrukcja Instalacji

### Krok 1: Przygotowanie plików
1. Skopiuj cały folder `rss-aggregator` do katalogu `/wp-content/plugins/` na serwerze
2. Up<PERSON><PERSON>j się, że wszystkie pliki zostały skopiowane poprawnie
3. Sprawdź uprawnienia - folder powinien mieć uprawnienia 755, pliki 644

### Krok 2: Aktywacja wtyczki
1. Zaloguj się do panelu administracyjnego WordPress
2. Przejdź do **Wtyczki** → **Zainstalowane wtyczki**
3. Znajdź **RSS Aggregator** na liście
4. Kliknij **Aktywuj**

### Krok 3: Automatyczna konfiguracja
Po aktywacji wtyczka automatycznie:
- Utworzy tabele w bazie danych:
  - `wp_rss_aggregator_feeds` (kanały RSS)
  - `wp_rss_aggregator_items` (elementy RSS)
- Ustawi domyślne opcje
- Zaplanuje zadania cron

### Krok 4: Sprawdzenie instalacji
1. Przejdź do **RSS Aggregator** → **Status** w menu WordPress
2. Sprawdź czy wszystkie komponenty są aktywne:
   - ✅ WP-Cron: Enabled
   - ✅ BuddyBoss: Active (jeśli zainstalowany)
   - ✅ GeoDirectory: Active (jeśli zainstalowany)

## Konfiguracja Początkowa

### 1. Ustawienia Podstawowe
Przejdź do **RSS Aggregator** → **Settings**:

**Zakładka General:**
- **Default Update Frequency**: Ustaw domyślną częstotliwość (zalecane: "Every hour")
- **Max Items per Feed**: Maksymalna liczba elementów (zalecane: 50)
- **Default Initial Import Count**: Liczba najnowszych wpisów przy pierwszym imporcie (zalecane: 10)
- **Enable Thumbnails**: Zaznacz dla automatycznego pobierania miniaturek

**Zakładka Integrations:**
- **BuddyBoss Integration**: Zaznacz jeśli chcesz publikować w strumieniu aktywności
- **GeoDirectory Integration**: Zaznacz dla integracji z miejscami

**Zakładka Posts:**
- **Default Post Status**: Ustaw status postów (zalecane: "Publish")
- **Default Post Author**: Wybierz domyślnego autora

### 2. Dodanie Pierwszego Kanału RSS
1. Przejdź do **RSS Aggregator** → **Add New**
2. Wypełnij formularz:
   - **Feed Name**: Nazwa kanału (np. "Portal Lokalny")
   - **Feed URL**: Pełny URL do RSS (np. "https://example.com/rss")
   - **GeoDirectory Place**: Zacznij wpisywać nazwę miejsca (opcjonalne)
   - **Assigned User**: Zacznij wpisywać nazwę użytkownika (opcjonalne)
   - **County**: Powiat (opcjonalne)
   - **Update Frequency**: Częstotliwość aktualizacji
   - **Initial Import Count**: Liczba najnowszych wpisów do pobrania przy pierwszym imporcie (1-100)
3. Kliknij **Test Feed** aby sprawdzić poprawność
4. Zapisz kanał

### 3. Testowanie Funkcjonalności
1. **Test ręczny**: Kliknij "Update Now" przy kanale
2. **Sprawdź posty**: Przejdź do **Posts** i sprawdź czy utworzono nowe wpisy
3. **Sprawdź cron**: W **RSS Aggregator** → **Status** sprawdź harmonogram

## Rozwiązywanie Problemów

### Problem: Wtyczka nie aktywuje się
**Rozwiązanie:**
1. Sprawdź logi błędów PHP
2. Upewnij się, że spełnione są wymagania systemowe
3. Sprawdź czy wszystkie pliki zostały skopiowane

### Problem: Kanały RSS nie aktualizują się automatycznie
**Rozwiązanie:**
1. Sprawdź czy WP-Cron jest włączony
2. W **RSS Aggregator** → **Settings** → **Maintenance** kliknij "Reset All Schedules"
3. Sprawdź czy serwer obsługuje WP-Cron (niektóre hostingi wyłączają)

### Problem: Nie pobierają się miniaturki
**Rozwiązanie:**
1. Sprawdź czy włączone jest "Enable Thumbnails" w ustawieniach
2. Upewnij się, że PHP ma rozszerzenie `curl`
3. Sprawdź czy serwer może pobierać pliki z zewnętrznych URL

### Problem: Błędy przy pobieraniu RSS
**Rozwiązanie:**
1. Sprawdź URL kanału RSS w przeglądarce
2. Użyj funkcji "Test Feed" w formularzu
3. Sprawdź czy serwer ma dostęp do internetu

## Konfiguracja Zaawansowana

### WP-Cron na Serwerach OVH WebCloud
Jeśli WP-Cron nie działa poprawnie:

1. **Opcja 1: Użyj zewnętrznego cron**
   ```bash
   # Dodaj do crontab serwera:
   */15 * * * * wget -q -O - https://twoja-domena.pl/wp-cron.php?doing_wp_cron >/dev/null 2>&1
   ```

2. **Opcja 2: Wyłącz WP-Cron i użyj systemowego**
   ```php
   // Dodaj do wp-config.php:
   define('DISABLE_WP_CRON', true);
   ```

### Optymalizacja Wydajności
1. **Cache RSS**: Wtyczka automatycznie cache'uje kanały na 15 minut
2. **Limit elementów**: Nie ustawiaj więcej niż 100 elementów na kanał
3. **Częstotliwość**: Dla większości kanałów wystarczy aktualizacja co godzinę
4. **Pierwszy import**: Ustaw rozsądną liczbę wpisów do pierwszego importu (5-20) aby uniknąć przeciążenia

### Funkcja Pierwszego Importu
Wtyczka oferuje specjalną funkcję kontroli pierwszego importu:

**Jak działa:**
- Przy dodaniu nowego kanału RSS, wtyczka pobiera tylko określoną liczbę najnowszych wpisów
- Wpisy są sortowane od najnowszego do najstarszego
- Kolejne aktualizacje pobierają wszystkie nowe wpisy normalnie

**Zalety:**
- Unika zaśmiecania strony starymi wpisami
- Przyspiesza pierwszy import
- Pozwala na kontrolowane wprowadzenie nowego źródła

**Konfiguracja:**
- **Globalnie**: W ustawieniach → General → "Default Initial Import Count"
- **Per kanał**: W formularzu kanału → "Initial Import Count"
- **Zakres**: 1-100 wpisów (zalecane: 5-20)

### Przypisywanie Kanałów
Wtyczka oferuje elastyczny system przypisywania kanałów:

**Opcje przypisywania:**
- **Do miejsca GeoDirectory**: Kanał może być powiązany z konkretnym miejscem
- **Do użytkownika WordPress**: Kanał może być przypisany do konkretnego użytkownika
- **Podwójne przypisywanie**: Możliwość jednoczesnego przypisania do miejsca i użytkownika

**Jak używać:**
1. **Pole "GeoDirectory Place"**: Zacznij wpisywać nazwę miejsca - pojawią się podpowiedzi
2. **Pole "Assigned User"**: Zacznij wpisywać nazwę użytkownika - pojawią się podpowiedzi
3. **Autocomplete**: Kliknij na sugestię aby wybrać
4. **Oba pola**: Możesz wypełnić oba pola jednocześnie

**Korzyści:**
- Lepsze kategoryzowanie treści
- Łatwiejsze zarządzanie kanałami
- Integracja z BuddyBoss (posty przypisane do użytkowników)
- Integracja z GeoDirectory (posty powiązane z miejscami)

### Backup i Przywracanie
**Backup:**
- Tabele: `wp_rss_aggregator_feeds`, `wp_rss_aggregator_items`
- Opcje: wszystkie zaczynające się od `rss_aggregator_`
- Pliki: cały folder `/wp-content/plugins/rss-aggregator/`

**Przywracanie:**
1. Przywróć pliki wtyczki
2. Przywróć tabele bazy danych
3. Przywróć opcje WordPress
4. Aktywuj wtyczkę

## Shortcodes

### Wyświetlanie kanałów RSS
```php
// Wszystkie kanały RSS
[rss_aggregator_feed limit="10"]

// Konkretny kanał
[rss_aggregator_feed feed_id="1" limit="5"]

// Kanały z konkretnego powiatu
[rss_aggregator_county county="krakowski" limit="8"]

// Najnowsze wpisy
[rss_aggregator_recent limit="5" days="7"]
```

## Wsparcie

W przypadku problemów:
1. Sprawdź logi błędów WordPress
2. Przejdź do **RSS Aggregator** → **Status** dla diagnostyki
3. Sprawdź czy spełnione są wszystkie wymagania systemowe

## Dezinstalacja

**UWAGA: Dezinstalacja usunie wszystkie dane!**

1. Dezaktywuj wtyczkę
2. Usuń wtyczkę przez panel WordPress
3. Automatycznie zostaną usunięte:
   - Wszystkie tabele bazy danych
   - Wszystkie posty RSS
   - Wszystkie ustawienia
   - Wszystkie zadania cron
