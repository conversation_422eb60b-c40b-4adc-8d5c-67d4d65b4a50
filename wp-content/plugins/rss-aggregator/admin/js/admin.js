/**
 * RSS Aggregator Admin JavaScript
 *
 * @package RSS_Aggregator
 * @since 1.0.0
 */

(function($) {
    'use strict';
    
    // Main admin object
    var RSSAggregatorAdmin = {
        
        /**
         * Initialize admin functionality
         */
        init: function() {
            this.bindEvents();
            this.initTooltips();
            this.initModals();
        },
        
        /**
         * Bind event handlers
         */
        bindEvents: function() {
            // Test feed button
            $(document).on('click', '.test-feed, #test-feed-btn', this.testFeed);
            
            // Force update buttons
            $(document).on('click', '.force-update', this.forceUpdateFeed);
            $(document).on('click', '#force-update-all', this.forceUpdateAllFeeds);
            
            // Delete feed buttons
            $(document).on('click', '.delete-feed', this.showDeleteModal);
            
            // Modal close handlers
            $(document).on('click', '.rss-modal-close', this.closeModal);
            $(document).on('click', '.rss-modal', this.closeModalOnOutsideClick);
            
            // Form validation
            $(document).on('submit', '.rss-aggregator-form', this.validateForm);
            
            // Auto-generate feed name
            $(document).on('blur', '#feed-url', this.autoGenerateFeedName);
            
            // County selection handlers
            $(document).on('input', '#feed-county-custom', this.handleCustomCounty);
            $(document).on('change', '#feed-county', this.handleCountySelect);
            
            // Settings tabs
            $(document).on('click', '.nav-tab', this.switchTab);
            
            // Bulk actions
            $(document).on('change', '#bulk-action-selector-top', this.handleBulkAction);
            $(document).on('change', '#cb-select-all-1', this.toggleSelectAll);
            
            // Status page actions
            $(document).on('click', '#trigger-cron', this.triggerCron);
            $(document).on('click', '#reset-cron', this.resetCron);
            $(document).on('click', '#manual-cleanup', this.manualCleanup);

            // Initialize autocomplete
            this.initAutocomplete();
        },
        
        /**
         * Initialize tooltips
         */
        initTooltips: function() {
            $('[title]').each(function() {
                var $this = $(this);
                var title = $this.attr('title');
                
                if (title) {
                    $this.removeAttr('title').on('mouseenter', function() {
                        $this.attr('data-tooltip', title);
                    }).on('mouseleave', function() {
                        $this.removeAttr('data-tooltip');
                    });
                }
            });
        },
        
        /**
         * Initialize modals
         */
        initModals: function() {
            // Ensure modals are hidden on page load
            $('.rss-modal').hide();
        },
        
        /**
         * Test RSS feed
         */
        testFeed: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var feedUrl = $button.data('feed-url') || $('#feed-url').val();
            
            if (!feedUrl) {
                alert(rssAggregatorAdmin.strings.error + ': ' + 'Please enter a feed URL first.');
                return;
            }
            
            var originalText = $button.text();
            var $results = $('#test-feed-results');
            
            $button.text(rssAggregatorAdmin.strings.testing).prop('disabled', true);
            
            if ($results.length) {
                $results.html('<div class="spinner is-active" style="float: none; margin: 0;"></div>');
            }
            
            $.post(rssAggregatorAdmin.ajaxUrl, {
                action: 'rss_aggregator_test_feed',
                nonce: rssAggregatorAdmin.nonce,
                feed_url: feedUrl
            })
            .done(function(response) {
                $button.text(originalText).prop('disabled', false);
                
                var resultsHtml = '';
                if (response.success) {
                    resultsHtml = '<div class="notice notice-success inline"><p><strong>' + 
                                 rssAggregatorAdmin.strings.success + ':</strong> ' + 
                                 response.data.message + '</p></div>';
                    
                    if (response.data.sample_items && response.data.sample_items.length > 0) {
                        resultsHtml += '<div class="rss-sample-items">';
                        resultsHtml += '<h4>Sample Items:</h4>';
                        resultsHtml += '<ul>';
                        response.data.sample_items.forEach(function(item) {
                            resultsHtml += '<li>';
                            resultsHtml += '<strong>' + RSSAggregatorAdmin.escapeHtml(item.title) + '</strong><br>';
                            resultsHtml += '<small><a href="' + item.url + '" target="_blank">' + item.url + '</a></small>';
                            if (item.pub_date) {
                                resultsHtml += '<br><small>Published: ' + item.pub_date + '</small>';
                            }
                            resultsHtml += '</li>';
                        });
                        resultsHtml += '</ul>';
                        resultsHtml += '</div>';
                    }
                } else {
                    resultsHtml = '<div class="notice notice-error inline"><p><strong>' + 
                                 rssAggregatorAdmin.strings.error + ':</strong> ' + 
                                 response.data + '</p></div>';
                }
                
                if ($results.length) {
                    $results.html(resultsHtml);
                } else {
                    // Show in modal for feed list page
                    $('#test-feed-results').html(resultsHtml);
                    $('#test-feed-modal').show();
                }
            })
            .fail(function() {
                $button.text(originalText).prop('disabled', false);
                var errorHtml = '<div class="notice notice-error inline"><p>Failed to test feed. Please try again.</p></div>';
                
                if ($results.length) {
                    $results.html(errorHtml);
                } else {
                    $('#test-feed-results').html(errorHtml);
                    $('#test-feed-modal').show();
                }
            });
        },
        
        /**
         * Force update single feed
         */
        forceUpdateFeed: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var feedId = $button.data('feed-id');
            var originalText = $button.text();
            
            $button.text(rssAggregatorAdmin.strings.updating).prop('disabled', true);
            
            $.post(rssAggregatorAdmin.ajaxUrl, {
                action: 'rss_aggregator_force_update',
                nonce: rssAggregatorAdmin.nonce,
                feed_id: feedId
            })
            .done(function(response) {
                $button.text(originalText).prop('disabled', false);
                
                if (response.success) {
                    RSSAggregatorAdmin.showNotice('success', response.data.message);
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    RSSAggregatorAdmin.showNotice('error', response.data);
                }
            })
            .fail(function() {
                $button.text(originalText).prop('disabled', false);
                RSSAggregatorAdmin.showNotice('error', 'Failed to update feed. Please try again.');
            });
        },
        
        /**
         * Force update all feeds
         */
        forceUpdateAllFeeds: function(e) {
            e.preventDefault();
            
            if (!confirm('This will update all active feeds. Continue?')) {
                return;
            }
            
            var $button = $(this);
            var originalText = $button.text();
            
            $button.text('Updating...').prop('disabled', true);
            
            // Get all feed IDs
            var feedIds = [];
            $('input[name="feeds[]"]').each(function() {
                feedIds.push($(this).val());
            });
            
            if (feedIds.length === 0) {
                $button.text(originalText).prop('disabled', false);
                RSSAggregatorAdmin.showNotice('error', 'No feeds found to update.');
                return;
            }
            
            // Update feeds sequentially to prevent timeout
            var updateNext = function(index) {
                if (index >= feedIds.length) {
                    $button.text(originalText).prop('disabled', false);
                    RSSAggregatorAdmin.showNotice('success', 'All feeds updated successfully.');
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                    return;
                }
                
                $.post(rssAggregatorAdmin.ajaxUrl, {
                    action: 'rss_aggregator_force_update',
                    nonce: rssAggregatorAdmin.nonce,
                    feed_id: feedIds[index]
                })
                .always(function() {
                    updateNext(index + 1);
                });
            };
            
            updateNext(0);
        },
        
        /**
         * Show delete confirmation modal
         */
        showDeleteModal: function(e) {
            e.preventDefault();
            
            var feedId = $(this).data('feed-id');
            $('#delete-feed-id').val(feedId);
            $('#delete-feed-modal').show();
        },
        
        /**
         * Close modal
         */
        closeModal: function(e) {
            e.preventDefault();
            $(this).closest('.rss-modal').hide();
        },
        
        /**
         * Close modal on outside click
         */
        closeModalOnOutsideClick: function(e) {
            if (e.target === this) {
                $(this).hide();
            }
        },
        
        /**
         * Validate form before submission
         */
        validateForm: function(e) {
            var $form = $(this);
            var isValid = true;
            
            // Check required fields
            $form.find('[required]').each(function() {
                var $field = $(this);
                var value = $field.val().trim();
                
                if (!value) {
                    isValid = false;
                    $field.focus();
                    alert('Please fill in all required fields.');
                    return false;
                }
                
                // URL validation
                if ($field.attr('type') === 'url') {
                    var urlPattern = /^https?:\/\/.+/i;
                    if (!urlPattern.test(value)) {
                        isValid = false;
                        $field.focus();
                        alert('Please enter a valid URL starting with http:// or https://');
                        return false;
                    }
                }
            });
            
            // Handle custom county
            var customCounty = $('#feed-county-custom').val();
            if (customCounty) {
                $('#feed-county').val(customCounty);
            }
            
            return isValid;
        },
        
        /**
         * Auto-generate feed name from URL
         */
        autoGenerateFeedName: function() {
            var url = $(this).val();
            var $nameField = $('#feed-name');
            var currentName = $nameField.val();
            
            if (url && !currentName) {
                try {
                    var hostname = new URL(url).hostname;
                    var suggestedName = hostname.replace('www.', '').replace(/\./g, ' ').toUpperCase() + ' RSS Feed';
                    $nameField.val(suggestedName);
                } catch (e) {
                    // Invalid URL, ignore
                }
            }
        },
        
        /**
         * Handle custom county input
         */
        handleCustomCounty: function() {
            if ($(this).val()) {
                $('#feed-county').val('');
            }
        },
        
        /**
         * Handle county select
         */
        handleCountySelect: function() {
            if ($(this).val()) {
                $('#feed-county-custom').val('');
            }
        },
        
        /**
         * Switch settings tabs
         */
        switchTab: function(e) {
            e.preventDefault();
            
            var target = $(this).attr('href');
            
            // Update active tab
            $('.nav-tab').removeClass('nav-tab-active');
            $(this).addClass('nav-tab-active');
            
            // Show target content
            $('.tab-content').removeClass('active');
            $(target).addClass('active');
        },
        
        /**
         * Handle bulk actions
         */
        handleBulkAction: function() {
            if ($(this).val() === 'bulk_delete') {
                if (!confirm(rssAggregatorAdmin.strings.confirmDelete)) {
                    $(this).val('-1');
                }
            }
        },
        
        /**
         * Toggle select all checkbox
         */
        toggleSelectAll: function() {
            $('input[name="feeds[]"]').prop('checked', $(this).prop('checked'));
        },
        
        /**
         * Trigger cron manually
         */
        triggerCron: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var originalText = $button.text();
            
            $button.text('Running...').prop('disabled', true);
            
            $.post(rssAggregatorAdmin.ajaxUrl, {
                action: 'rss_aggregator_trigger_cron',
                nonce: rssAggregatorAdmin.nonce
            })
            .done(function(response) {
                $button.text(originalText).prop('disabled', false);
                
                if (response.success) {
                    RSSAggregatorAdmin.showNotice('success', response.data.message);
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    RSSAggregatorAdmin.showNotice('error', response.data);
                }
            })
            .fail(function() {
                $button.text(originalText).prop('disabled', false);
                RSSAggregatorAdmin.showNotice('error', 'Failed to trigger cron.');
            });
        },
        
        /**
         * Reset cron schedules
         */
        resetCron: function(e) {
            e.preventDefault();
            
            if (!confirm('This will reset all cron schedules. Continue?')) {
                return;
            }
            
            var $button = $(this);
            var originalText = $button.text();
            
            $button.text('Resetting...').prop('disabled', true);
            
            $.post(rssAggregatorAdmin.ajaxUrl, {
                action: 'rss_aggregator_reset_cron',
                nonce: rssAggregatorAdmin.nonce
            })
            .done(function(response) {
                $button.text(originalText).prop('disabled', false);
                
                if (response.success) {
                    RSSAggregatorAdmin.showNotice('success', response.data.message);
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    RSSAggregatorAdmin.showNotice('error', response.data);
                }
            })
            .fail(function() {
                $button.text(originalText).prop('disabled', false);
                RSSAggregatorAdmin.showNotice('error', 'Failed to reset cron schedules.');
            });
        },
        
        /**
         * Manual cleanup
         */
        manualCleanup: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var originalText = $button.text();
            var $results = $('#cleanup-results');
            
            $button.text('Running...').prop('disabled', true);
            
            $.post(rssAggregatorAdmin.ajaxUrl, {
                action: 'rss_aggregator_manual_cleanup',
                nonce: rssAggregatorAdmin.nonce
            })
            .done(function(response) {
                $button.text(originalText).prop('disabled', false);
                
                if (response.success) {
                    $results.html('<div class="notice notice-success inline"><p>' + response.data.message + '</p></div>');
                } else {
                    $results.html('<div class="notice notice-error inline"><p>' + response.data + '</p></div>');
                }
            })
            .fail(function() {
                $button.text(originalText).prop('disabled', false);
                $results.html('<div class="notice notice-error inline"><p>Failed to run cleanup.</p></div>');
            });
        },
        
        /**
         * Show admin notice
         */
        showNotice: function(type, message) {
            var noticeClass = type === 'success' ? 'notice-success' : 'notice-error';
            var $notice = $('<div class="notice ' + noticeClass + ' is-dismissible"><p>' + message + '</p></div>');
            
            $notice.insertAfter('.wp-header-end');
            
            setTimeout(function() {
                $notice.fadeOut();
            }, 3000);
        },
        
        /**
         * Initialize autocomplete functionality
         */
        initAutocomplete: function() {
            var self = this;

            // Place autocomplete
            $(document).on('input', '.autocomplete-place', function() {
                var $input = $(this);
                var searchTerm = $input.val();
                var $suggestions = $('#place-suggestions');

                if (searchTerm.length < 2) {
                    $suggestions.hide().empty();
                    return;
                }

                $.ajax({
                    url: rssAggregatorAdmin.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'rss_aggregator_search_places',
                        search: searchTerm,
                        nonce: rssAggregatorAdmin.nonce
                    },
                    success: function(response) {
                        if (response.success && response.data.length > 0) {
                            self.showPlaceSuggestions(response.data, $suggestions, $input);
                        } else {
                            $suggestions.hide().empty();
                        }
                    }
                });
            });

            // User autocomplete
            $(document).on('input', '.autocomplete-user', function() {
                var $input = $(this);
                var searchTerm = $input.val();
                var $suggestions = $('#user-suggestions');

                if (searchTerm.length < 2) {
                    $suggestions.hide().empty();
                    return;
                }

                $.ajax({
                    url: rssAggregatorAdmin.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'rss_aggregator_search_users',
                        search: searchTerm,
                        nonce: rssAggregatorAdmin.nonce
                    },
                    success: function(response) {
                        if (response.success && response.data.length > 0) {
                            self.showUserSuggestions(response.data, $suggestions, $input);
                        } else {
                            $suggestions.hide().empty();
                        }
                    }
                });
            });

            // County autocomplete
            $(document).on('input', '.autocomplete-county', function() {
                var $input = $(this);
                var searchTerm = $input.val();
                var $suggestions = $('#county-suggestions');

                if (searchTerm.length < 2) {
                    $suggestions.hide().empty();
                    return;
                }

                $.ajax({
                    url: rssAggregatorAdmin.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'rss_aggregator_search_counties',
                        search: searchTerm,
                        nonce: rssAggregatorAdmin.nonce
                    },
                    success: function(response) {
                        if (response.success && response.data.length > 0) {
                            self.showCountySuggestions(response.data, $suggestions, $input);
                        } else {
                            $suggestions.hide().empty();
                        }
                    }
                });
            });

            // Region autocomplete
            $(document).on('input', '.autocomplete-region', function() {
                var $input = $(this);
                var searchTerm = $input.val();
                var $suggestions = $('#region-suggestions');

                if (searchTerm.length < 2) {
                    $suggestions.hide().empty();
                    return;
                }

                $.ajax({
                    url: rssAggregatorAdmin.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'rss_aggregator_search_regions',
                        search: searchTerm,
                        nonce: rssAggregatorAdmin.nonce
                    },
                    success: function(response) {
                        if (response.success && response.data.length > 0) {
                            self.showRegionSuggestions(response.data, $suggestions, $input);
                        } else {
                            $suggestions.hide().empty();
                        }
                    }
                });
            });

            // Hide suggestions when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.autocomplete-place, .autocomplete-user, .autocomplete-county, .autocomplete-region, .autocomplete-suggestions').length) {
                    $('.autocomplete-suggestions').hide();
                }
            });
        },

        /**
         * Show place suggestions
         */
        showPlaceSuggestions: function(places, $suggestions, $input) {
            var self = this;
            var html = '';

            places.forEach(function(place) {
                var details = '';
                if (place.city && place.region) {
                    details = place.city + ', ' + place.region;
                } else if (place.city) {
                    details = place.city;
                } else if (place.region) {
                    details = place.region;
                }

                html += '<div class="autocomplete-suggestion autocomplete-place" data-id="' + place.id + '" data-name="' + self.escapeHtml(place.name) + '">';
                html += '<div class="name">' + self.escapeHtml(place.name) + '</div>';
                if (details) {
                    html += '<div class="details">' + self.escapeHtml(details) + '</div>';
                }
                html += '</div>';
            });

            $suggestions.html(html).show();

            // Handle suggestion clicks
            $suggestions.off('click', '.autocomplete-suggestion').on('click', '.autocomplete-suggestion', function() {
                var $suggestion = $(this);
                var id = $suggestion.data('id');
                var name = $suggestion.data('name');

                $input.val(name);
                $('#geodirectory-place-id').val(id);
                $suggestions.hide();
            });
        },

        /**
         * Show user suggestions
         */
        showUserSuggestions: function(users, $suggestions, $input) {
            var self = this;
            var html = '';

            users.forEach(function(user) {
                var details = user.login;
                if (user.email && user.email !== user.login) {
                    details += ' (' + user.email + ')';
                }

                html += '<div class="autocomplete-suggestion autocomplete-user" data-id="' + user.id + '" data-name="' + self.escapeHtml(user.name) + '">';
                html += '<div class="name">' + self.escapeHtml(user.name) + '</div>';
                html += '<div class="details">' + self.escapeHtml(details) + '</div>';
                html += '</div>';
            });

            $suggestions.html(html).show();

            // Handle suggestion clicks
            $suggestions.off('click', '.autocomplete-suggestion').on('click', '.autocomplete-suggestion', function() {
                var $suggestion = $(this);
                var id = $suggestion.data('id');
                var name = $suggestion.data('name');

                $input.val(name);
                $('#assigned-user-id').val(id);
                $suggestions.hide();
            });
        },

        /**
         * Show county suggestions
         */
        showCountySuggestions: function(counties, $suggestions, $input) {
            var self = this;
            var html = '';

            counties.forEach(function(county) {
                var details = county.region || '';

                html += '<div class="autocomplete-suggestion autocomplete-county" data-name="' + self.escapeHtml(county.name) + '">';
                html += '<div class="name">' + self.escapeHtml(county.name) + '</div>';
                if (details) {
                    html += '<div class="details">' + self.escapeHtml(details) + '</div>';
                }
                html += '</div>';
            });

            $suggestions.html(html).show();

            // Handle suggestion clicks
            $suggestions.off('click', '.autocomplete-suggestion').on('click', '.autocomplete-suggestion', function() {
                var $suggestion = $(this);
                var name = $suggestion.data('name');

                $input.val(name);
                $suggestions.hide();
            });
        },

        /**
         * Show region suggestions
         */
        showRegionSuggestions: function(regions, $suggestions, $input) {
            var self = this;
            var html = '';

            regions.forEach(function(region) {
                html += '<div class="autocomplete-suggestion autocomplete-region" data-name="' + self.escapeHtml(region.name) + '">';
                html += '<div class="name">' + self.escapeHtml(region.name) + '</div>';
                html += '</div>';
            });

            $suggestions.html(html).show();

            // Handle suggestion clicks
            $suggestions.off('click', '.autocomplete-suggestion').on('click', '.autocomplete-suggestion', function() {
                var $suggestion = $(this);
                var name = $suggestion.data('name');

                $input.val(name);
                $suggestions.hide();
            });
        },

        /**
         * Escape HTML
         */
        escapeHtml: function(text) {
            var map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };

            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        RSSAggregatorAdmin.init();
    });
    
    // Make available globally
    window.RSSAggregatorAdmin = RSSAggregatorAdmin;
    
})(jQuery);
