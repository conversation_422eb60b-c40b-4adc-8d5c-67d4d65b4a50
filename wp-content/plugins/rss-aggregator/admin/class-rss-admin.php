<?php
/**
 * RSS Aggregator Admin Class
 *
 * @package RSS_Aggregator
 * @version 2.0.0
 * <AUTHOR>
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class RSS_Admin {
    
    private $database;
    private $current_page;
    
    public function __construct() {
        $this->database = new RSS_Database();
        $this->init_hooks();
    }
    
    private function init_hooks() {
        // Admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // Admin scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        
        // Handle form submissions
        add_action('admin_init', array($this, 'handle_form_submission'));
        
        // AJAX handlers
        add_action('wp_ajax_rss_aggregator_search_places', array($this, 'ajax_search_places'));
        add_action('wp_ajax_rss_aggregator_search_users', array($this, 'ajax_search_users'));
        add_action('wp_ajax_rss_aggregator_search_counties', array($this, 'ajax_search_counties'));
        add_action('wp_ajax_rss_aggregator_search_regions', array($this, 'ajax_search_regions'));
        add_action('wp_ajax_rss_aggregator_delete_feed_posts', array($this, 'ajax_delete_feed_posts'));
        add_action('wp_ajax_rss_aggregator_manual_cleanup', array($this, 'ajax_manual_cleanup'));
        add_action('wp_ajax_rss_aggregator_reset_cron', array($this, 'ajax_reset_cron'));
        add_action('wp_ajax_rss_aggregator_trigger_cron', array($this, 'ajax_trigger_cron'));
        add_action('wp_ajax_rss_aggregator_test_author', array($this, 'ajax_test_author'));
    }
    
    public function add_admin_menu() {
        $capability = 'manage_options';
        
        // Main menu page
        add_menu_page(
            __('RSS Aggregator', 'rss-aggregator'),
            __('RSS Aggregator', 'rss-aggregator'),
            $capability,
            'rss-aggregator',
            array($this, 'display_feeds_page'),
            'dashicons-rss',
            30
        );
        
        // Submenu pages
        add_submenu_page(
            'rss-aggregator',
            __('All Feeds', 'rss-aggregator'),
            __('All Feeds', 'rss-aggregator'),
            $capability,
            'rss-aggregator',
            array($this, 'display_feeds_page')
        );
        
        add_submenu_page(
            'rss-aggregator',
            __('Add New Feed', 'rss-aggregator'),
            __('Add New', 'rss-aggregator'),
            $capability,
            'rss-aggregator-add',
            array($this, 'display_add_feed_page')
        );
        
        // Hidden statistics page (accessed via direct link)
        add_submenu_page(
            null, // Hidden from menu
            __('Feed Statistics', 'rss-aggregator'),
            __('Statistics', 'rss-aggregator'),
            $capability,
            'rss-aggregator-statistics',
            array($this, 'display_statistics_page')
        );
        
        add_submenu_page(
            'rss-aggregator',
            __('Settings', 'rss-aggregator'),
            __('Settings', 'rss-aggregator'),
            $capability,
            'rss-aggregator-settings',
            array($this, 'display_settings_page')
        );
        
        // Debug page (only for administrators)
        if (current_user_can('administrator')) {
            add_submenu_page(
                'rss-aggregator',
                __('Debug', 'rss-aggregator'),
                __('Debug', 'rss-aggregator'),
                'administrator',
                'rss-aggregator-debug',
                array($this, 'display_debug_page')
            );
        }
        
        add_submenu_page(
            'rss-aggregator',
            __('Status', 'rss-aggregator'),
            __('Status', 'rss-aggregator'),
            $capability,
            'rss-aggregator-status',
            array($this, 'display_status_page')
        );
    }
    
    public function enqueue_admin_scripts($hook) {
        // Only load on our admin pages
        if (strpos($hook, 'rss-aggregator') === false) {
            return;
        }
        
        wp_enqueue_script('jquery');
        wp_enqueue_script('jquery-ui-autocomplete');
        
        wp_enqueue_script(
            'rss-aggregator-admin',
            RSS_AGGREGATOR_PLUGIN_URL . 'admin/js/admin.js',
            array('jquery', 'jquery-ui-autocomplete'),
            RSS_AGGREGATOR_VERSION,
            true
        );
        
        wp_enqueue_style(
            'rss-aggregator-admin',
            RSS_AGGREGATOR_PLUGIN_URL . 'admin/css/admin.css',
            array(),
            RSS_AGGREGATOR_VERSION
        );
        
        // Localize script for AJAX
        wp_localize_script('rss-aggregator-admin', 'rssAggregator', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('rss_aggregator_admin'),
            'strings' => array(
                'confirm_delete' => __('Are you sure you want to delete this feed?', 'rss-aggregator'),
                'confirm_delete_posts' => __('Are you sure you want to delete all posts from this feed?', 'rss-aggregator'),
                'testing_feed' => __('Testing feed...', 'rss-aggregator'),
                'updating_feed' => __('Updating feed...', 'rss-aggregator'),
                'deleting_feed' => __('Deleting feed...', 'rss-aggregator'),
            )
        ));
    }
    
    // Display methods for each page
    public function display_feeds_page() {
        $this->current_page = 'feeds';
        
        // Handle actions
        if (isset($_GET['action']) && isset($_GET['id'])) {
            $action = sanitize_text_field($_GET['action']);
            $feed_id = intval($_GET['id']);
            
            if ($action === 'delete' && wp_verify_nonce($_GET['_wpnonce'], 'delete_feed_' . $feed_id)) {
                $this->handle_delete_feed($feed_id);
            } elseif ($action === 'edit') {
                $this->display_edit_feed_page($feed_id);
                return;
            }
        }
        
        // Get all feeds
        $feeds = $this->database->get_all_feeds();
        $statistics = $this->database->get_statistics();
        
        include RSS_AGGREGATOR_PLUGIN_DIR . 'admin/partials/feeds-list.php';
    }
    
    public function display_add_feed_page() {
        $this->current_page = 'add';
        
        // Check if editing
        $feed = null;
        if (isset($_GET['edit'])) {
            $feed_id = intval($_GET['edit']);
            $feed = $this->database->get_feed($feed_id);
            if (!$feed) {
                wp_die(__('Feed not found.', 'rss-aggregator'));
            }
        }
        
        include RSS_AGGREGATOR_PLUGIN_DIR . 'admin/partials/add-feed.php';
    }
    
    public function display_edit_feed_page($feed_id) {
        $this->current_page = 'edit';
        $feed = $this->database->get_feed($feed_id);
        
        if (!$feed) {
            wp_die(__('Feed not found.', 'rss-aggregator'));
        }
        
        include RSS_AGGREGATOR_PLUGIN_DIR . 'admin/partials/add-feed.php';
    }
    
    public function display_settings_page() {
        $this->current_page = 'settings';
        include RSS_AGGREGATOR_PLUGIN_DIR . 'admin/partials/settings.php';
    }
    
    public function display_debug_page() {
        $this->current_page = 'debug';
        include RSS_AGGREGATOR_PLUGIN_DIR . 'admin/partials/debug-page.php';
    }
    
    public function display_status_page() {
        $this->current_page = 'status';
        
        // Get cron status
        $cron = new RSS_Cron();
        $cron_status = $cron->get_cron_status();
        
        // Get database statistics
        $statistics = $this->database->get_statistics();
        
        include RSS_AGGREGATOR_PLUGIN_DIR . 'admin/partials/status.php';
    }
    
    public function display_statistics_page() {
        $this->current_page = 'statistics';
        include RSS_AGGREGATOR_PLUGIN_DIR . 'admin/partials/feed-statistics.php';
    }
    
    // Form handling methods
    public function handle_form_submission() {
        if (!isset($_POST['rss_aggregator_nonce'])) {
            return;
        }
        
        if (!wp_verify_nonce($_POST['rss_aggregator_nonce'], 'rss_aggregator_save_feed')) {
            wp_die(__('Security check failed.', 'rss-aggregator'));
        }
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions.', 'rss-aggregator'));
        }
        
        if (isset($_POST['save_feed'])) {
            $this->handle_save_feed();
        } elseif (isset($_POST['save_settings'])) {
            $this->handle_save_settings();
        }
    }
    
    private function handle_save_feed() {
        $feed_data = array(
            'name' => sanitize_text_field($_POST['name']),
            'url' => esc_url_raw($_POST['url']),
            'geodirectory_place_id' => !empty($_POST['geodirectory_place_id']) ? intval($_POST['geodirectory_place_id']) : null,
            'geodirectory_place_name' => sanitize_text_field($_POST['geodirectory_place_name']),
            'assigned_user_id' => !empty($_POST['assigned_user_id']) ? intval($_POST['assigned_user_id']) : null,
            'assigned_user_name' => sanitize_text_field($_POST['assigned_user_name']),
            'county' => sanitize_text_field($_POST['county']),
            'region' => sanitize_text_field($_POST['region']),
            'update_frequency' => sanitize_text_field($_POST['update_frequency']),
            'initial_import_count' => !empty($_POST['initial_import_count']) ? max(1, min(100, intval($_POST['initial_import_count']))) : 10,
            'status' => sanitize_text_field($_POST['status'])
        );
        
        // Form data prepared
        
        $feed_id = isset($_POST['feed_id']) ? intval($_POST['feed_id']) : 0;
        
        if ($feed_id > 0) {
            // Update existing feed
            $result = $this->database->update_feed($feed_id, $feed_data);
            $message = $result ? __('Feed updated successfully.', 'rss-aggregator') : __('Failed to update feed.', 'rss-aggregator');
        } else {
            // Create new feed
            $result = $this->database->save_feed($feed_data);
            $message = $result ? __('Feed created successfully.', 'rss-aggregator') : __('Failed to create feed.', 'rss-aggregator');
            
            if ($result) {
                $feed_id = $result;
                // Trigger initial import
                $rss_aggregator = RSS_Aggregator::get_instance();
                $rss_aggregator->update_single_feed($feed_id);
            }
        }
        
        // Redirect with message
        $redirect_url = add_query_arg(array(
            'page' => 'rss-aggregator',
            'message' => $result ? 'success' : 'error',
            'text' => urlencode($message)
        ), admin_url('admin.php'));
        
        wp_redirect($redirect_url);
        exit;
    }
    
    // AJAX handlers
    public function ajax_search_places() {
        try {
            check_ajax_referer('rss_aggregator_admin', 'nonce');
        } catch (Exception $e) {
            wp_send_json_error(__('Security check failed', 'rss-aggregator'));
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'rss-aggregator'));
            return;
        }
        
        $search_term = sanitize_text_field($_POST['search'] ?? '');
        
        if (empty($search_term)) {
            wp_send_json_error(__('Search term is required', 'rss-aggregator'));
            return;
        }
        
        $places = $this->database->search_places($search_term, 10);
        
        wp_send_json_success($places);
    }
    
    public function ajax_search_users() {
        try {
            check_ajax_referer('rss_aggregator_admin', 'nonce');
        } catch (Exception $e) {
            wp_send_json_error(__('Security check failed', 'rss-aggregator'));
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'rss-aggregator'));
            return;
        }
        
        $search_term = sanitize_text_field($_POST['search'] ?? '');
        
        if (empty($search_term)) {
            wp_send_json_error(__('Search term is required', 'rss-aggregator'));
            return;
        }
        
        $users = $this->database->search_users($search_term, 10);
        
        wp_send_json_success($users);
    }
    
    public function ajax_search_counties() {
        try {
            check_ajax_referer('rss_aggregator_admin', 'nonce');
        } catch (Exception $e) {
            wp_send_json_error(__('Security check failed', 'rss-aggregator'));
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'rss-aggregator'));
            return;
        }
        
        $search_term = sanitize_text_field($_POST['search'] ?? '');
        
        if (empty($search_term)) {
            wp_send_json_error(__('Search term is required', 'rss-aggregator'));
            return;
        }
        
        $counties = $this->database->search_counties($search_term, 10);
        
        wp_send_json_success($counties);
    }
    
    public function ajax_search_regions() {
        try {
            check_ajax_referer('rss_aggregator_admin', 'nonce');
        } catch (Exception $e) {
            wp_send_json_error(__('Security check failed', 'rss-aggregator'));
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'rss-aggregator'));
            return;
        }
        
        $search_term = sanitize_text_field($_POST['search'] ?? '');
        
        if (empty($search_term)) {
            wp_send_json_error(__('Search term is required', 'rss-aggregator'));
            return;
        }
        
        $regions = $this->database->search_regions($search_term, 10);
        
        wp_send_json_success($regions);
    }
    
    /**
     * AJAX handler for testing author determination
     */
    public function ajax_test_author() {
        if (!current_user_can('administrator')) {
            wp_send_json_error(__('Insufficient permissions', 'rss-aggregator'));
            return;
        }
        
        $feed_id = intval($_POST['feed_id'] ?? 0);
        
        if (empty($feed_id)) {
            wp_send_json_error(__('Feed ID is required', 'rss-aggregator'));
            return;
        }
        
        // Get feed
        $feed = $this->database->get_feed($feed_id);
        if (!$feed) {
            wp_send_json_error(__('Feed not found', 'rss-aggregator'));
            return;
        }
        
        // Test author determination
        $rss_aggregator = RSS_Aggregator::get_instance();
        $reflection = new ReflectionClass($rss_aggregator);
        $method = $reflection->getMethod('determine_post_author');
        $method->setAccessible(true);
        $author_id = $method->invoke($rss_aggregator, $feed);
        
        $author = get_user_by('id', $author_id);
        
        $result = array(
            'feed_id' => $feed_id,
            'feed_name' => $feed->name,
            'geodirectory_place_id' => $feed->geodirectory_place_id,
            'assigned_user_id' => $feed->assigned_user_id,
            'determined_author_id' => $author_id,
            'determined_author_name' => $author ? $author->display_name : 'Unknown',
            'default_author_setting' => get_option('rss_aggregator_post_author', 1)
        );
        
        wp_send_json_success($result);
    }
}
