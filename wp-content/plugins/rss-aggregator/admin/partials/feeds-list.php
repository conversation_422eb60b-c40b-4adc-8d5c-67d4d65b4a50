<?php
/**
 * Admin feeds list view
 *
 * @package RSS_Aggregator
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Display admin notices
$this->display_admin_notices();
?>

<div class="wrap">
    <h1 class="wp-heading-inline"><?php _e('RSS Feeds', 'rss-aggregator'); ?></h1>
    <a href="<?php echo admin_url('admin.php?page=rss-aggregator-add'); ?>" class="page-title-action">
        <?php _e('Add New', 'rss-aggregator'); ?>
    </a>
    
    <hr class="wp-header-end">
    
    <!-- Statistics Cards -->
    <div class="rss-aggregator-stats">
        <div class="rss-stat-card">
            <div class="rss-stat-number"><?php echo $statistics['total_feeds']; ?></div>
            <div class="rss-stat-label"><?php _e('Total Feeds', 'rss-aggregator'); ?></div>
        </div>
        <div class="rss-stat-card">
            <div class="rss-stat-number"><?php echo $statistics['active_feeds']; ?></div>
            <div class="rss-stat-label"><?php _e('Active Feeds', 'rss-aggregator'); ?></div>
        </div>
        <div class="rss-stat-card">
            <div class="rss-stat-number"><?php echo $statistics['total_items']; ?></div>
            <div class="rss-stat-label"><?php _e('Total Items', 'rss-aggregator'); ?></div>
        </div>
        <div class="rss-stat-card">
            <div class="rss-stat-number"><?php echo $statistics['recent_items']; ?></div>
            <div class="rss-stat-label"><?php _e('Last 24h', 'rss-aggregator'); ?></div>
        </div>
    </div>
    
    <?php if (empty($feeds)): ?>
        <div class="rss-aggregator-empty-state">
            <div class="rss-empty-icon">
                <span class="dashicons dashicons-rss"></span>
            </div>
            <h2><?php _e('No RSS feeds yet', 'rss-aggregator'); ?></h2>
            <p><?php _e('Start by adding your first RSS feed to begin aggregating content.', 'rss-aggregator'); ?></p>
            <a href="<?php echo admin_url('admin.php?page=rss-aggregator-add'); ?>" class="button button-primary">
                <?php _e('Add Your First Feed', 'rss-aggregator'); ?>
            </a>
        </div>
    <?php else: ?>
        <form method="post" action="">
            <?php wp_nonce_field('bulk-feeds'); ?>
            
            <div class="tablenav top">
                <div class="alignleft actions bulkactions">
                    <label for="bulk-action-selector-top" class="screen-reader-text">
                        <?php _e('Select bulk action', 'rss-aggregator'); ?>
                    </label>
                    <select name="action" id="bulk-action-selector-top">
                        <option value="-1"><?php _e('Bulk Actions', 'rss-aggregator'); ?></option>
                        <option value="bulk_delete"><?php _e('Delete', 'rss-aggregator'); ?></option>
                    </select>
                    <input type="submit" class="button action" value="<?php _e('Apply', 'rss-aggregator'); ?>">
                </div>
                
                <div class="alignright actions">
                    <button type="button" class="button" id="force-update-all">
                        <?php _e('Update All Feeds', 'rss-aggregator'); ?>
                    </button>
                </div>
            </div>
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <td class="manage-column column-cb check-column">
                            <label class="screen-reader-text" for="cb-select-all-1">
                                <?php _e('Select All', 'rss-aggregator'); ?>
                            </label>
                            <input id="cb-select-all-1" type="checkbox">
                        </td>
                        <th class="manage-column column-name column-primary">
                            <?php _e('Name', 'rss-aggregator'); ?>
                        </th>
                        <th class="manage-column column-url">
                            <?php _e('URL', 'rss-aggregator'); ?>
                        </th>
                        <th class="manage-column column-assignments">
                            <?php _e('Assignments', 'rss-aggregator'); ?>
                        </th>
                        <th class="manage-column column-county">
                            <?php _e('County', 'rss-aggregator'); ?>
                        </th>
                        <th class="manage-column column-frequency">
                            <?php _e('Frequency', 'rss-aggregator'); ?>
                        </th>
                        <th class="manage-column column-initial-count">
                            <?php _e('Initial Count', 'rss-aggregator'); ?>
                        </th>
                        <th class="manage-column column-status">
                            <?php _e('Status', 'rss-aggregator'); ?>
                        </th>
                        <th class="manage-column column-last-updated">
                            <?php _e('Last Updated', 'rss-aggregator'); ?>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($feeds as $feed): ?>
                        <tr>
                            <th scope="row" class="check-column">
                                <input type="checkbox" name="feeds[]" value="<?php echo $feed->id; ?>">
                            </th>
                            <td class="column-name column-primary">
                                <strong>
                                    <a href="<?php echo admin_url('admin.php?page=rss-aggregator-add&edit=' . $feed->id); ?>">
                                        <?php echo esc_html($feed->name); ?>
                                    </a>
                                </strong>
                                <div class="row-actions">
                                    <span class="edit">
                                        <a href="<?php echo admin_url('admin.php?page=rss-aggregator-add&edit=' . $feed->id); ?>">
                                            <?php _e('Edit', 'rss-aggregator'); ?>
                                        </a> |
                                    </span>
                                    <span class="test">
                                        <a href="#" class="test-feed" data-feed-url="<?php echo esc_attr($feed->url); ?>">
                                            <?php _e('Test', 'rss-aggregator'); ?>
                                        </a> |
                                    </span>
                                    <span class="update">
                                        <a href="#" class="force-update" data-feed-id="<?php echo $feed->id; ?>">
                                            <?php _e('Update Now', 'rss-aggregator'); ?>
                                        </a> |
                                    </span>
                                    <span class="delete">
                                        <a href="#" class="delete-feed" data-feed-id="<?php echo $feed->id; ?>">
                                            <?php _e('Delete', 'rss-aggregator'); ?>
                                        </a>
                                    </span>
                                </div>
                                <button type="button" class="toggle-row">
                                    <span class="screen-reader-text"><?php _e('Show more details', 'rss-aggregator'); ?></span>
                                </button>
                            </td>
                            <td class="column-url" data-colname="<?php _e('URL', 'rss-aggregator'); ?>">
                                <a href="<?php echo esc_url($feed->url); ?>" target="_blank" rel="noopener">
                                    <?php echo esc_html($feed->url); ?>
                                    <span class="dashicons dashicons-external"></span>
                                </a>
                            </td>
                            <td class="column-assignments" data-colname="<?php _e('Assignments', 'rss-aggregator'); ?>">
                                <div class="assignments-list">
                                    <?php if (!empty($feed->geodirectory_place_name)): ?>
                                        <div class="assignment-item place-assignment">
                                            <span class="dashicons dashicons-location-alt"></span>
                                            <span class="assignment-text"><?php echo esc_html($feed->geodirectory_place_name); ?></span>
                                        </div>
                                    <?php endif; ?>

                                    <?php if (!empty($feed->assigned_user_name)): ?>
                                        <div class="assignment-item user-assignment">
                                            <span class="dashicons dashicons-admin-users"></span>
                                            <span class="assignment-text"><?php echo esc_html($feed->assigned_user_name); ?></span>
                                        </div>
                                    <?php endif; ?>

                                    <?php if (empty($feed->geodirectory_place_name) && empty($feed->assigned_user_name)): ?>
                                        <span class="no-assignments"><?php _e('No assignments', 'rss-aggregator'); ?></span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="column-county" data-colname="<?php _e('County', 'rss-aggregator'); ?>">
                                <?php if (!empty($feed->county)): ?>
                                    <span class="county-badge">
                                        <?php echo esc_html($feed->county); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="no-county"><?php _e('Not set', 'rss-aggregator'); ?></span>
                                <?php endif; ?>
                            </td>
                            <td class="column-frequency" data-colname="<?php _e('Frequency', 'rss-aggregator'); ?>">
                                <?php
                                $cron = new RSS_Cron();
                                $frequencies = $cron->get_available_frequencies();
                                echo isset($frequencies[$feed->update_frequency]) ? $frequencies[$feed->update_frequency] : $feed->update_frequency;
                                ?>
                            </td>
                            <td class="column-initial-count" data-colname="<?php _e('Initial Count', 'rss-aggregator'); ?>">
                                <span class="initial-count-badge">
                                    <?php echo intval($feed->initial_import_count); ?>
                                    <?php if (empty($feed->last_updated)): ?>
                                        <small class="not-imported"><?php _e('(not imported yet)', 'rss-aggregator'); ?></small>
                                    <?php else: ?>
                                        <small class="imported"><?php _e('(imported)', 'rss-aggregator'); ?></small>
                                    <?php endif; ?>
                                </span>
                            </td>
                            <td class="column-status" data-colname="<?php _e('Status', 'rss-aggregator'); ?>">
                                <?php if ($feed->status === 'active'): ?>
                                    <span class="status-active">
                                        <span class="dashicons dashicons-yes-alt"></span>
                                        <?php _e('Active', 'rss-aggregator'); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="status-inactive">
                                        <span class="dashicons dashicons-dismiss"></span>
                                        <?php _e('Inactive', 'rss-aggregator'); ?>
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="column-last-updated" data-colname="<?php _e('Last Updated', 'rss-aggregator'); ?>">
                                <?php if ($feed->last_updated): ?>
                                    <span title="<?php echo esc_attr($feed->last_updated); ?>">
                                        <?php echo human_time_diff(strtotime($feed->last_updated), current_time('timestamp')) . ' ' . __('ago', 'rss-aggregator'); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="never-updated"><?php _e('Never', 'rss-aggregator'); ?></span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </form>
    <?php endif; ?>
</div>

<!-- Delete confirmation modal -->
<div id="delete-feed-modal" class="rss-modal" style="display: none;">
    <div class="rss-modal-content">
        <div class="rss-modal-header">
            <h3><?php _e('Delete Feed', 'rss-aggregator'); ?></h3>
            <button type="button" class="rss-modal-close">&times;</button>
        </div>
        <div class="rss-modal-body">
            <p><?php _e('Are you sure you want to delete this feed? This action cannot be undone.', 'rss-aggregator'); ?></p>
            <p><strong><?php _e('Note:', 'rss-aggregator'); ?></strong> <?php _e('All items from this feed will also be deleted.', 'rss-aggregator'); ?></p>
        </div>
        <div class="rss-modal-footer">
            <form method="post" action="<?php echo admin_url('admin-post.php'); ?>">
                <?php wp_nonce_field('rss_aggregator_delete_feed'); ?>
                <input type="hidden" name="action" value="rss_aggregator_delete_feed">
                <input type="hidden" name="feed_id" id="delete-feed-id" value="">
                <button type="button" class="button rss-modal-close">
                    <?php _e('Cancel', 'rss-aggregator'); ?>
                </button>
                <button type="submit" class="button button-primary button-delete">
                    <?php _e('Delete Feed', 'rss-aggregator'); ?>
                </button>
            </form>
        </div>
    </div>
</div>

<!-- Test feed results modal -->
<div id="test-feed-modal" class="rss-modal" style="display: none;">
    <div class="rss-modal-content">
        <div class="rss-modal-header">
            <h3><?php _e('Test Feed Results', 'rss-aggregator'); ?></h3>
            <button type="button" class="rss-modal-close">&times;</button>
        </div>
        <div class="rss-modal-body">
            <div id="test-feed-results"></div>
        </div>
        <div class="rss-modal-footer">
            <button type="button" class="button rss-modal-close">
                <?php _e('Close', 'rss-aggregator'); ?>
            </button>
        </div>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Handle delete feed
    $('.delete-feed').on('click', function(e) {
        e.preventDefault();
        var feedId = $(this).data('feed-id');
        $('#delete-feed-id').val(feedId);
        $('#delete-feed-modal').show();
    });
    
    // Handle test feed
    $('.test-feed').on('click', function(e) {
        e.preventDefault();
        var feedUrl = $(this).data('feed-url');
        var $button = $(this);
        var originalText = $button.text();
        
        $button.text(rssAggregatorAdmin.strings.testing);
        
        $.post(rssAggregatorAdmin.ajaxUrl, {
            action: 'rss_aggregator_test_feed',
            nonce: rssAggregatorAdmin.nonce,
            feed_url: feedUrl
        }, function(response) {
            $button.text(originalText);
            
            var resultsHtml = '';
            if (response.success) {
                resultsHtml = '<div class="notice notice-success"><p>' + response.data.message + '</p></div>';
                if (response.data.sample_items && response.data.sample_items.length > 0) {
                    resultsHtml += '<h4>' + '<?php _e('Sample Items:', 'rss-aggregator'); ?>' + '</h4>';
                    resultsHtml += '<ul>';
                    response.data.sample_items.forEach(function(item) {
                        resultsHtml += '<li><strong>' + item.title + '</strong><br><small>' + item.url + '</small></li>';
                    });
                    resultsHtml += '</ul>';
                }
            } else {
                resultsHtml = '<div class="notice notice-error"><p>' + response.data + '</p></div>';
            }
            
            $('#test-feed-results').html(resultsHtml);
            $('#test-feed-modal').show();
        });
    });
    
    // Handle force update
    $('.force-update').on('click', function(e) {
        e.preventDefault();
        var feedId = $(this).data('feed-id');
        var $button = $(this);
        var originalText = $button.text();

        $button.text(rssAggregatorAdmin.strings.updating);

        $.post(rssAggregatorAdmin.ajaxUrl, {
            action: 'rss_aggregator_force_update',
            nonce: rssAggregatorAdmin.nonce,
            feed_id: feedId
        }, function(response) {
            $button.text(originalText);

            if (response.success) {
                // Show success message
                $('<div class="notice notice-success is-dismissible"><p>' + response.data.message + '</p></div>')
                    .insertAfter('.wp-header-end')
                    .delay(3000)
                    .fadeOut();

                // Update last updated time
                location.reload();
            } else {
                $('<div class="notice notice-error is-dismissible"><p>' + response.data + '</p></div>')
                    .insertAfter('.wp-header-end')
                    .delay(3000)
                    .fadeOut();
            }
        });
    });

    // Handle force update all
    $('#force-update-all').on('click', function(e) {
        e.preventDefault();
        var $button = $(this);
        var originalText = $button.text();

        if (!confirm('<?php _e('This will update all active feeds. Continue?', 'rss-aggregator'); ?>')) {
            return;
        }

        $button.text('<?php _e('Updating...', 'rss-aggregator'); ?>').prop('disabled', true);

        // Update feeds one by one to prevent timeout
        var feedIds = [];
        $('input[name="feeds[]"]').each(function() {
            feedIds.push($(this).val());
        });

        var updateNext = function(index) {
            if (index >= feedIds.length) {
                $button.text(originalText).prop('disabled', false);
                location.reload();
                return;
            }

            $.post(rssAggregatorAdmin.ajaxUrl, {
                action: 'rss_aggregator_force_update',
                nonce: rssAggregatorAdmin.nonce,
                feed_id: feedIds[index]
            }, function(response) {
                updateNext(index + 1);
            });
        };

        updateNext(0);
    });
    
    // Modal close handlers
    $('.rss-modal-close').on('click', function() {
        $(this).closest('.rss-modal').hide();
    });
    
    // Close modal on outside click
    $('.rss-modal').on('click', function(e) {
        if (e.target === this) {
            $(this).hide();
        }
    });
    
    // Handle bulk actions
    $('#bulk-action-selector-top').on('change', function() {
        if ($(this).val() === 'bulk_delete') {
            if (!confirm(rssAggregatorAdmin.strings.confirmDelete)) {
                $(this).val('-1');
            }
        }
    });
    
    // Select all checkbox
    $('#cb-select-all-1').on('change', function() {
        $('input[name="feeds[]"]').prop('checked', $(this).prop('checked'));
    });
});
</script>
