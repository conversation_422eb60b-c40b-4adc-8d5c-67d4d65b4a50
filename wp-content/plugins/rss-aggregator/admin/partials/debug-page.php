<?php
/**
 * Debug Page
 *
 * @package RSS_Aggregator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

global $wpdb;
$database = new RSS_Database();
?>

<div class="wrap">
    <h1><?php _e('RSS Aggregator Debug', 'rss-aggregator'); ?></h1>
    
    <div class="debug-sections">
        
        <!-- Table Structure -->
        <div class="debug-section" style="background: #fff; padding: 20px; margin: 20px 0; border: 1px solid #ddd;">
            <h2><?php _e('1. Table Structure', 'rss-aggregator'); ?></h2>
            
            <?php
            $table_name = $wpdb->prefix . 'rss_aggregator_feeds';
            $columns = $wpdb->get_results("SHOW COLUMNS FROM {$table_name}");
            ?>
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('Field', 'rss-aggregator'); ?></th>
                        <th><?php _e('Type', 'rss-aggregator'); ?></th>
                        <th><?php _e('Null', 'rss-aggregator'); ?></th>
                        <th><?php _e('Key', 'rss-aggregator'); ?></th>
                        <th><?php _e('Default', 'rss-aggregator'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($columns as $column): ?>
                        <tr>
                            <td><strong><?php echo $column->Field; ?></strong></td>
                            <td><?php echo $column->Type; ?></td>
                            <td><?php echo $column->Null; ?></td>
                            <td><?php echo $column->Key; ?></td>
                            <td><?php echo $column->Default ?? 'NULL'; ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Required Columns Check -->
        <div class="debug-section" style="background: #fff; padding: 20px; margin: 20px 0; border: 1px solid #ddd;">
            <h2><?php _e('2. Required Columns Check', 'rss-aggregator'); ?></h2>
            
            <?php
            $required_columns = ['assigned_user_id', 'assigned_user_name', 'geodirectory_place_id', 'geodirectory_place_name', 'region'];
            $existing_columns = array_column($columns, 'Field');
            ?>
            
            <ul>
                <?php foreach ($required_columns as $col): ?>
                    <?php 
                    $exists = in_array($col, $existing_columns);
                    $status = $exists ? '✅ EXISTS' : '❌ MISSING';
                    $color = $exists ? 'green' : 'red';
                    ?>
                    <li style="color: <?php echo $color; ?>; font-weight: bold;">
                        <strong><?php echo $col; ?>:</strong> <?php echo $status; ?>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
        
        <!-- Sample Feed Data -->
        <div class="debug-section" style="background: #fff; padding: 20px; margin: 20px 0; border: 1px solid #ddd;">
            <h2><?php _e('3. Sample Feed Data', 'rss-aggregator'); ?></h2>
            
            <?php
            $feeds = $wpdb->get_results("SELECT * FROM {$table_name} LIMIT 3");
            ?>
            
            <?php if ($feeds): ?>
                <div style="overflow-x: auto;">
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <?php foreach ($feeds[0] as $key => $value): ?>
                                    <th><?php echo $key; ?></th>
                                <?php endforeach; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($feeds as $feed): ?>
                                <tr>
                                    <?php foreach ($feed as $key => $value): ?>
                                        <td><?php echo $value ?? '<em>NULL</em>'; ?></td>
                                    <?php endforeach; ?>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <p><?php _e('No feeds found in database.', 'rss-aggregator'); ?></p>
            <?php endif; ?>
        </div>
        
        <!-- Author Determination Test -->
        <div class="debug-section" style="background: #fff; padding: 20px; margin: 20px 0; border: 1px solid #ddd;">
            <h2><?php _e('4. Author Determination Test', 'rss-aggregator'); ?></h2>
            
            <?php if ($feeds): ?>
                <?php foreach ($feeds as $feed): ?>
                    <div style="border: 1px solid #ccc; padding: 15px; margin: 10px 0;">
                        <h3><?php echo esc_html($feed->name); ?> (ID: <?php echo $feed->id; ?>)</h3>
                        
                        <p><strong><?php _e('geodirectory_place_id:', 'rss-aggregator'); ?></strong> 
                           <?php echo $feed->geodirectory_place_id ?? '<em>NULL</em>'; ?></p>
                        <p><strong><?php _e('assigned_user_id:', 'rss-aggregator'); ?></strong> 
                           <?php echo $feed->assigned_user_id ?? '<em>NULL</em>'; ?></p>
                        
                        <?php
                        // Test place author
                        if (!empty($feed->geodirectory_place_id)) {
                            $place_post = get_post($feed->geodirectory_place_id);
                            if ($place_post) {
                                $place_user = get_user_by('id', $place_post->post_author);
                                echo "<p><strong>Place post author:</strong> {$place_post->post_author} - " . 
                                     ($place_user ? $place_user->display_name : 'User not found') . "</p>";
                            } else {
                                echo "<p><strong>Place post:</strong> <em>Not found</em></p>";
                            }
                        }
                        
                        // Test assigned user
                        if (!empty($feed->assigned_user_id)) {
                            $assigned_user = get_user_by('id', $feed->assigned_user_id);
                            echo "<p><strong>Assigned user:</strong> " . 
                                 ($assigned_user ? $assigned_user->display_name : 'User not found') . "</p>";
                        }
                        
                        // Test default author
                        $default_author = get_option('rss_aggregator_post_author', 1);
                        $default_user = get_user_by('id', $default_author);
                        echo "<p><strong>Default author (settings):</strong> {$default_author} - " .
                             ($default_user ? $default_user->display_name : 'User not found') . "</p>";
                        ?>

                        <button type="button" class="button test-author-btn" data-feed-id="<?php echo $feed->id; ?>">
                            <?php _e('Test Author Determination', 'rss-aggregator'); ?>
                        </button>
                        <div id="author-test-result-<?php echo $feed->id; ?>" style="margin-top: 10px; padding: 10px; background: #f0f0f0; display: none;"></div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <!-- RSS Feed Testing -->
        <div class="debug-section" style="background: #fff; padding: 20px; margin: 20px 0; border: 1px solid #ddd;">
            <h2><?php _e('5. RSS Feed Testing', 'rss-aggregator'); ?></h2>

            <div style="margin-bottom: 20px;">
                <label for="test-feed-url"><?php _e('Test RSS Feed URL:', 'rss-aggregator'); ?></label><br>
                <input type="url" id="test-feed-url" placeholder="https://example.com/feed/" style="width: 400px; margin-right: 10px;">
                <button type="button" class="button button-primary" id="test-feed-btn">
                    <?php _e('Test Feed', 'rss-aggregator'); ?>
                </button>
            </div>

            <div id="feed-test-result" style="margin-top: 15px; padding: 15px; background: #f9f9f9; border-left: 4px solid #0073aa; display: none;">
                <h4><?php _e('Test Results:', 'rss-aggregator'); ?></h4>
                <div id="feed-test-content"></div>
            </div>
        </div>

        <!-- Recent RSS Posts -->
        <div class="debug-section" style="background: #fff; padding: 20px; margin: 20px 0; border: 1px solid #ddd;">
            <h2><?php _e('6. Recent RSS Posts and Authors', 'rss-aggregator'); ?></h2>
            
            <?php
            $recent_posts = $wpdb->get_results("
                SELECT p.ID, p.post_title, p.post_author, p.post_date, pm.meta_value as feed_id
                FROM {$wpdb->posts} p
                LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_rss_aggregator_feed_id'
                WHERE pm.meta_value IS NOT NULL
                ORDER BY p.post_date DESC
                LIMIT 10
            ");
            ?>
            
            <?php if ($recent_posts): ?>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('Post ID', 'rss-aggregator'); ?></th>
                            <th><?php _e('Title', 'rss-aggregator'); ?></th>
                            <th><?php _e('Author ID', 'rss-aggregator'); ?></th>
                            <th><?php _e('Author Name', 'rss-aggregator'); ?></th>
                            <th><?php _e('Feed ID', 'rss-aggregator'); ?></th>
                            <th><?php _e('Date', 'rss-aggregator'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_posts as $post): ?>
                            <?php $author = get_user_by('id', $post->post_author); ?>
                            <tr>
                                <td><?php echo $post->ID; ?></td>
                                <td><?php echo substr($post->post_title, 0, 50) . '...'; ?></td>
                                <td><?php echo $post->post_author; ?></td>
                                <td><?php echo $author ? $author->display_name : 'Unknown'; ?></td>
                                <td><?php echo $post->feed_id; ?></td>
                                <td><?php echo $post->post_date; ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p><?php _e('No RSS posts found.', 'rss-aggregator'); ?></p>
            <?php endif; ?>
        </div>
        
        <!-- Available Users -->
        <div class="debug-section" style="background: #fff; padding: 20px; margin: 20px 0; border: 1px solid #ddd;">
            <h2><?php _e('6. Available Users', 'rss-aggregator'); ?></h2>
            
            <?php $users = get_users(array('number' => 10)); ?>
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('ID', 'rss-aggregator'); ?></th>
                        <th><?php _e('Login', 'rss-aggregator'); ?></th>
                        <th><?php _e('Display Name', 'rss-aggregator'); ?></th>
                        <th><?php _e('Email', 'rss-aggregator'); ?></th>
                        <th><?php _e('Role', 'rss-aggregator'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $user): ?>
                        <?php $roles = implode(', ', $user->roles); ?>
                        <tr>
                            <td><?php echo $user->ID; ?></td>
                            <td><?php echo $user->user_login; ?></td>
                            <td><?php echo $user->display_name; ?></td>
                            <td><?php echo $user->user_email; ?></td>
                            <td><?php echo $roles; ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Settings Check -->
        <div class="debug-section" style="background: #fff; padding: 20px; margin: 20px 0; border: 1px solid #ddd;">
            <h2><?php _e('7. Plugin Settings', 'rss-aggregator'); ?></h2>
            
            <?php
            $settings = array(
                'rss_aggregator_post_author' => get_option('rss_aggregator_post_author', 1),
                'rss_aggregator_post_status' => get_option('rss_aggregator_post_status', 'publish'),
                'rss_aggregator_enable_thumbnails' => get_option('rss_aggregator_enable_thumbnails', 1),
                'rss_aggregator_default_frequency' => get_option('rss_aggregator_default_frequency', 'hourly')
            );
            ?>
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('Setting', 'rss-aggregator'); ?></th>
                        <th><?php _e('Value', 'rss-aggregator'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($settings as $key => $value): ?>
                        <tr>
                            <td><?php echo $key; ?></td>
                            <td><?php echo $value; ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

    </div>
</div>

<script>
// Make sure ajaxurl is available
var ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';

jQuery(document).ready(function($) {
    $('.test-author-btn').on('click', function() {
        var $btn = $(this);
        var feedId = $btn.data('feed-id');
        var $result = $('#author-test-result-' + feedId);

        $btn.prop('disabled', true).text('<?php _e('Testing...', 'rss-aggregator'); ?>');
        $result.hide();

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'rss_aggregator_test_author',
                feed_id: feedId
            },
            success: function(response) {
                if (response.success) {
                    var data = response.data;
                    var html = '<h4><?php _e('Author Test Result:', 'rss-aggregator'); ?></h4>';
                    html += '<p><strong><?php _e('Feed:', 'rss-aggregator'); ?></strong> ' + data.feed_name + '</p>';
                    html += '<p><strong><?php _e('Place ID:', 'rss-aggregator'); ?></strong> ' + (data.geodirectory_place_id || 'None') + '</p>';
                    html += '<p><strong><?php _e('Assigned User ID:', 'rss-aggregator'); ?></strong> ' + (data.assigned_user_id || 'None') + '</p>';
                    html += '<p><strong><?php _e('Determined Author:', 'rss-aggregator'); ?></strong> ' + data.determined_author_id + ' (' + data.determined_author_name + ')</p>';
                    html += '<p><strong><?php _e('Default Setting:', 'rss-aggregator'); ?></strong> ' + data.default_author_setting + '</p>';

                    $result.html(html).show();
                } else {
                    $result.html('<p style="color: red;"><?php _e('Error:', 'rss-aggregator'); ?> ' + response.data + '</p>').show();
                }

                $btn.prop('disabled', false).text('<?php _e('Test Author Determination', 'rss-aggregator'); ?>');
            },
            error: function() {
                $result.html('<p style="color: red;"><?php _e('AJAX error occurred', 'rss-aggregator'); ?></p>').show();
                $btn.prop('disabled', false).text('<?php _e('Test Author Determination', 'rss-aggregator'); ?>');
            }
        });
    });

    // RSS Feed Testing
    $('#test-feed-btn').on('click', function() {
        var $btn = $(this);
        var feedUrl = $('#test-feed-url').val().trim();
        var $result = $('#feed-test-result');
        var $content = $('#feed-test-content');

        console.log('RSS Aggregator: Test button clicked');
        console.log('RSS Aggregator: ajaxurl =', ajaxurl);
        console.log('RSS Aggregator: feedUrl =', feedUrl);

        if (!feedUrl) {
            alert('<?php _e('Please enter a feed URL', 'rss-aggregator'); ?>');
            return;
        }

        $btn.prop('disabled', true).text('<?php _e('Testing...', 'rss-aggregator'); ?>');
        $result.hide();

        console.log('RSS Aggregator: Starting AJAX request');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'rss_aggregator_test_feed',
                nonce: '<?php echo wp_create_nonce('rss_aggregator_admin'); ?>',
                feed_url: feedUrl
            },
            beforeSend: function() {
                console.log('RSS Aggregator: AJAX request sent');
            },
            success: function(response) {
                console.log('RSS Aggregator: AJAX success', response);
                if (response.success) {
                    var data = response.data;
                    var html = '<div style="color: green;"><strong><?php _e('✅ Feed is valid!', 'rss-aggregator'); ?></strong></div>';
                    html += '<p><strong><?php _e('Title:', 'rss-aggregator'); ?></strong> ' + (data.title || 'N/A') + '</p>';
                    html += '<p><strong><?php _e('Description:', 'rss-aggregator'); ?></strong> ' + (data.description || 'N/A') + '</p>';
                    html += '<p><strong><?php _e('Items found:', 'rss-aggregator'); ?></strong> ' + (data.item_count || 0) + '</p>';
                    html += '<p><strong><?php _e('Last updated:', 'rss-aggregator'); ?></strong> ' + (data.last_build_date || 'N/A') + '</p>';

                    if (data.sample_items && data.sample_items.length > 0) {
                        html += '<h4><?php _e('Sample Items:', 'rss-aggregator'); ?></h4>';
                        html += '<ul>';
                        data.sample_items.forEach(function(item) {
                            html += '<li><strong>' + item.title + '</strong><br>';
                            html += '<small>' + item.pub_date + '</small></li>';
                        });
                        html += '</ul>';
                    }

                    $content.html(html);
                    $result.css('border-left-color', '#46b450').show();
                } else {
                    var html = '<div style="color: red;"><strong><?php _e('❌ Feed test failed!', 'rss-aggregator'); ?></strong></div>';
                    html += '<p><strong><?php _e('Error:', 'rss-aggregator'); ?></strong> ' + (response.data || '<?php _e('Unknown error', 'rss-aggregator'); ?>') + '</p>';

                    $content.html(html);
                    $result.css('border-left-color', '#dc3232').show();
                }
            },
            error: function(xhr, status, error) {
                console.log('RSS Aggregator: AJAX error', xhr, status, error);
                console.log('RSS Aggregator: Response text:', xhr.responseText);

                var html = '<div style="color: red;"><strong><?php _e('❌ AJAX Error!', 'rss-aggregator'); ?></strong></div>';
                html += '<p><?php _e('Failed to communicate with server', 'rss-aggregator'); ?></p>';
                html += '<p>Status: ' + status + ', Error: ' + error + '</p>';

                $content.html(html);
                $result.css('border-left-color', '#dc3232').show();
            },
            complete: function() {
                $btn.prop('disabled', false).text('<?php _e('Test Feed', 'rss-aggregator'); ?>');
            }
        });
    });

    // Allow Enter key to trigger test
    $('#test-feed-url').on('keypress', function(e) {
        if (e.which === 13) {
            $('#test-feed-btn').click();
        }
    });
});
</script>
